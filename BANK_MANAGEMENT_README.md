# Hệ Thống Quản Lý Ngân Hàng Tự Động

## Tổng Quan
Hệ thống quản lý ngân hàng tự động cho nền tảng gaming Việt Nam với khả năng:
- Tự động phát hiện tên miền từ URL
- Quản lý và chuyển đổi tài khoản ngân hàng tự động
- Giao diện quản trị web trong folder adminx
- API backend Java để xử lý logic nghiệp vụ

## Cấu Trúc Hệ Thống

### 1. Backend Java Components

#### DomainDetector.java
- **Vị trí**: `BackendMaster/api/VinPlayPortal/src/main/java/com/vinplay/api/common/DomainDetector.java`
- **Chức năng**: 
  - Tự động phát hiện tên miền từ HTTP request
  - Trích xuất tên miền chính từ hostname (ví dụ: từ "iportal.789as.site" → "789as.site")
  - Tạo callback URL động dựa trên tên miền đư<PERSON>c phát hiện

#### BankAccountManager.java
- **Vị trí**: `BackendMaster/api/VinPlayPortal/src/main/java/com/vinplay/api/payment/BankAccountManager.java`
- **Chức năng**:
  - Quản lý cấu hình tài khoản ngân hàng từ file JSON
  - Chuyển đổi tài khoản ngân hàng tự động
  - Kiểm tra trạng thái và hạn mức tài khoản
  - Tránh sử dụng ngân hàng MB khi có thể

#### BankManagementServlet.java
- **Vị trí**: `BackendMaster/api/VinPlayPortal/src/main/java/com/vinplay/api/payment/BankManagementServlet.java`
- **Chức năng**: API endpoint để xử lý các yêu cầu từ giao diện PHP

#### Common.java (Updated)
- **Vị trí**: `BackendMaster/api/VinPlayPortal/src/main/java/com/vinplay/api/common/Common.java`
- **Thêm mới**:
  - Constants cho quản lý domain và bank
  - Methods để set/get current domain
  - Callback URL generation methods

#### pay.java (Updated)
- **Vị trí**: `BackendMaster/api/VinPlayPortal/src/main/java/com/vinplay/api/payment/pay.java`
- **Cập nhật**:
  - Tích hợp DomainDetector cho callback URL động
  - Tích hợp BankAccountManager cho chuyển đổi tài khoản tự động
  - Logic tránh ngân hàng MB

### 2. Frontend PHP Components

#### NapRut_QuanLyNganHang.php
- **Vị trí**: `www/adminx/NapRut_QuanLyNganHang.php`
- **Chức năng**:
  - Giao diện quản lý tài khoản ngân hàng
  - Chuyển đổi tài khoản ngân hàng
  - Thêm/sửa/xóa tài khoản
  - Cập nhật trạng thái tài khoản

#### NapRut_ThongKeNganHang.php
- **Vị trí**: `www/adminx/NapRut_ThongKeNganHang.php`
- **Chức năng**:
  - Hiển thị thống kê tổng quan
  - Theo dõi trạng thái tài khoản
  - Báo cáo sử dụng hạn mức

#### menu.php (Updated)
- **Cập nhật**: Thêm menu items cho quản lý ngân hàng

### 3. Configuration Files

#### bank_accounts.json
- **Vị trí**: `config/bank_accounts.json`
- **Cấu trúc**:
```json
{
  "accounts": [
    {
      "id": "bank_001",
      "bank_name": "VCB",
      "account_number": "**********",
      "account_name": "NGUYEN VAN A",
      "status": 1,
      "priority": 1,
      "daily_limit": ********,
      "current_amount": 0,
      "created_at": "2024-01-01 00:00:00",
      "updated_at": "2024-01-01 00:00:00"
    }
  ],
  "settings": {
    "auto_switch": true,
    "switch_threshold": 80,
    "maintenance_mode": false
  }
}
```

## API Endpoints

### Bank Management API
**Base URL**: `https://iportal.{domain}/api/bank-management`

#### 1. Chuyển đổi tài khoản ngân hàng
```
GET /api/bank-management?c=3057&account_id={account_id}&key=BopVuEmVo123
```

#### 2. Lấy tài khoản hiện tại
```
GET /api/bank-management?c=3058&key=BopVuEmVo123
```

#### 3. Chuyển đổi tài khoản tiếp theo
```
GET /api/bank-management?c=3059&key=BopVuEmVo123
```

#### 4. Lấy tất cả tài khoản
```
GET /api/bank-management?c=3060&key=BopVuEmVo123
```

#### 5. Cập nhật trạng thái tài khoản
```
GET /api/bank-management?c=3061&account_id={account_id}&status={status}&key=BopVuEmVo123
```

## Trạng Thái Tài Khoản

- **0**: Không hoạt động (INACTIVE)
- **1**: Đang hoạt động (ACTIVE)
- **2**: Bảo trì (MAINTENANCE)

## Cách Sử Dụng

### 1. Truy cập giao diện quản trị
- Đăng nhập vào adminx
- Vào menu "Nạp Rút" → "Quản Lý Ngân Hàng"

### 2. Quản lý tài khoản ngân hàng
- **Thêm tài khoản mới**: Click "Thêm Tài Khoản"
- **Chuyển đổi tài khoản**: Chọn tài khoản và click "Chuyển Đổi"
- **Cập nhật trạng thái**: Sử dụng dropdown menu

### 3. Xem thống kê
- Vào menu "Nạp Rút" → "Thống Kê Ngân Hàng"
- Xem tổng quan và trạng thái các tài khoản

## Tính Năng Tự Động

### 1. Phát hiện tên miền
- Hệ thống tự động phát hiện tên miền từ HTTP request
- Tạo callback URL phù hợp với tên miền hiện tại

### 2. Chuyển đổi tài khoản tự động
- Tự động chuyển sang tài khoản khác khi gặp ngân hàng MB
- Chuyển đổi khi đạt ngưỡng hạn mức
- Ưu tiên theo thứ tự priority

### 3. Quản lý trạng thái
- Theo dõi hạn mức hàng ngày
- Cập nhật trạng thái tự động
- Ghi log các thao tác

## Bảo Mật

- API key authentication: `BopVuEmVo123`
- Session-based authentication cho giao diện web
- CORS headers cho cross-domain requests

## Troubleshooting

### 1. Lỗi kết nối API
- Kiểm tra domain detection
- Verify API key
- Check network connectivity

### 2. Không thể chuyển đổi tài khoản
- Kiểm tra trạng thái tài khoản
- Verify account exists
- Check configuration file

### 3. Giao diện không hiển thị dữ liệu
- Kiểm tra API response
- Verify JSON configuration
- Check file permissions

## Maintenance

### 1. Backup configuration
```bash
cp config/bank_accounts.json config/bank_accounts.json.backup
```

### 2. Update bank account
- Sử dụng giao diện web hoặc edit JSON trực tiếp
- Restart application sau khi thay đổi

### 3. Monitor logs
- Check application logs cho errors
- Monitor API response times
- Track bank switching frequency
