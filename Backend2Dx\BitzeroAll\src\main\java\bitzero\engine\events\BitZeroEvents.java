package bitzero.engine.events;

public final class BitZeroEvents {
     public static final String ENGINE_STARTED = "serverStarted";
     public static final String MEMORY_LOW = "memoryLow";
     public static final String SESSION_LOST = "sessionLost";
     public static final String SESSION_RECONNECTION_TRY = "sessionReconnectionTry";
     public static final String SESSION_RECONNECTION_SUCCESS = "sessionReconnectionSuccess";
     public static final String SESSION_ADDED = "sessionAdded";
     public static final String SESSION_IDLE = "sessionIdle";
     public static final String PACKET_DROPPED = "packetDropped";
}
