package bitzero.server.core;

public enum BZEventType implements IBZEventType {
     SERVER_READY,
     USER_LOGIN,
     USER_JOIN_ZONE,
     USER_LOGOUT,
     USER_JOIN_ROOM,
     USER_LEAVE_ROOM,
     USER_QUICK_PLAY,
     USER_DISCONNECT,
     USER_RECONNECTION_TRY,
     USER_RECONNECTION_SUCCESS,
     USER_TEMP_DISCONNECT,
     ROOM_ADDED,
     ROOM_REMOVED,
     PUBLIC_MESSAGE,
     PRIVATE_MESSAGE,
     ROOM_VARIABLES_UPDATE,
     USER_VARIABLES_UPDATE,
     SPECTATOR_TO_PLAYER,
     P<PERSON><PERSON><PERSON>_TO_SPECTATOR,
     JOIN_CHANNEL_SUCCESS,
     OUT_CHANNEL_SUCCESS,
     CH<PERSON>GE_CHANNEL_SUCCESS,
     BUDDY_VARIABLES_UPDATE,
     BUDDY_ONLINE_STATE_UPDATE,
     BUDDY_MESSAGE,
     BUDDY_LIST_INIT,
     EXTENSI<PERSON>_COMMAND,
     GAME_INVITATION_SUCCESS,
     GAME_INVITATION_FAILURE,
     XU_UPDATE,
     CHARGE_XU,
     BAN_USER_CHAT,
     SERVICE_NOTIFY;
}
