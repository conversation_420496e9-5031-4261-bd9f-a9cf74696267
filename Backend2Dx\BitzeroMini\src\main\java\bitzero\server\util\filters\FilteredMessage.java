package bitzero.server.util.filters;

public class FilteredMessage {
     private String message;
     private int occurrences;

     public String getMessage() {
          return this.message;
     }

     public void setMessage(String message) {
          this.message = message;
     }

     public int getOccurrences() {
          return this.occurrences;
     }

     public void setOccurrences(int substitutions) {
          this.occurrences = substitutions;
     }
}
