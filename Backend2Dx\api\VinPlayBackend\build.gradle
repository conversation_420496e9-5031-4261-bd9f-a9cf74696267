plugins {
    id 'java'
}

group 'backend'

sourceCompatibility = 1.8

repositories {
    mavenCentral()
}

dependencies {
    testCompile group: 'junit', name: 'junit', version: '4.12'
    compile project(':VinPlayUserCore')
    compile project(':VinPlayDAL')
    compile project(':VbeeCommon')
    compile fileTree(include: ['*.jar'], dir: '../../java-libs')
}

task copyJar(type: Copy) {
    from configurations.compile {
        include 'VbeeCommon*.jar', "VinPlayDAL*.jar", "VinPlayUserCore*.jar"
    }
    into 'libs/'
}

build.dependsOn copyJar
