# Root logger option
log4j.logger.backend=DEBUG, backend
log4j.appender.backend=org.apache.log4j.DailyRollingFileAppender
log4j.appender.backend.layout=org.apache.log4j.PatternLayout
log4j.appender.backend.File=logs/api_backend/api_backend.log
log4j.appender.backend.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} | %-5p | %t | %c{3} %m%n
log4j.appender.backend.Encoding=UTF-8
log4j.appender.backend.DatePattern='.'yyyy-MM-dd

log4j.logger.report=INFO, report
log4j.appender.report=org.apache.log4j.DailyRollingFileAppender
log4j.appender.report.layout=org.apache.log4j.PatternLayout
log4j.appender.report.File=logs/api_backend/report.log
log4j.appender.report.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} | %-5p | %t | %c{3} %m%n
log4j.appender.report.Encoding=UTF-8
log4j.appender.report.DatePattern='.'yyyy-MM-dd

log4j.logger.org.mongodb.driver=INFO
log4j.logger.snaq.db=INFO
log4j.logger.org.eclipse.jetty=INFO
