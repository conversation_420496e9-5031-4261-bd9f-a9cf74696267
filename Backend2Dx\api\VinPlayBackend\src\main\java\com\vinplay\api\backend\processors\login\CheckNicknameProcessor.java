/*
 * Decompiled with CFR 0.144.
 * 
 * Could not load the following classes:
 *  com.vinplay.usercore.service.impl.UserServiceImpl
 *  com.vinplay.vbee.common.cp.BaseProcessor
 *  com.vinplay.vbee.common.cp.Param
 *  com.vinplay.vbee.common.models.UserModel
 *  javax.servlet.http.HttpServletRequest
 *  org.apache.log4j.Logger
 */
package com.vinplay.api.backend.processors.login;

import com.vinplay.usercore.service.impl.UserServiceImpl;
import com.vinplay.vbee.common.cp.BaseProcessor;
import com.vinplay.vbee.common.cp.Param;
import com.vinplay.vbee.common.models.UserModel;
import javax.servlet.http.HttpServletRequest;
import org.apache.log4j.Logger;

public class CheckNicknameProcessor
implements BaseProcessor<HttpServletRequest, String> {
    private static final Logger logger = Logger.getLogger((String)"backend");

    public String execute(Param<HttpServletRequest> param) {
        HttpServletRequest request = (HttpServletRequest)param.get();
        String nickname = request.getParameter("nn");
        String code = "-2";
        if (nickname != null) {
            try {
                UserServiceImpl service = new UserServiceImpl();
                UserModel model = service.getUserByNickName(nickname);
                code = model != null ? String.valueOf(model.getDaily()) : "-1";
            }
            catch (Exception e) {
                logger.debug((Object)e);
            }
        }
        return code;
    }
}

