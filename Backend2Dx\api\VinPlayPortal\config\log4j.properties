# Root logger option
log4j.rootLogger=DEBUG, api_portal
log4j.appender.api_portal=org.apache.log4j.DailyRollingFileAppender
log4j.appender.api_portal.layout=org.apache.log4j.PatternLayout
log4j.appender.api_portal.File=logs/api_portal/api_portal.log
log4j.appender.api_portal.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} | %-5p | %t | %c{3} %m%n
log4j.appender.api_portal.Encoding=UTF-8
log4j.appender.api_portal.DatePattern='.'yyyy-MM-dd

log4j.logger.org.mongodb.driver=INFO
log4j.logger.snaq.db=INFO
log4j.logger.org.eclipse.jetty=INFO
