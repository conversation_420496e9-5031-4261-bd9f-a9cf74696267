plugins {
    id 'java'
}

group 'backend'

sourceCompatibility = 1.8

repositories {
    mavenCentral()
}

dependencies {
    testCompile group: 'junit', name: 'junit', version: '4.12'
    compile project(':VinPlayUserCore')
    compile project(':VinPlayCardLib')
    compile project(':VinPlayDAL')
    compile project(':VbeeCommon')
    compile fileTree(include: ['*.jar'], dir: '../../java-libs')
}

task copyJar(type: Copy) {
    from configurations.compile {
        include 'VbeeCommon*.jar', 'VinPlayUserCore*.jar', 'VinPlayCardLib*.jar', "VinPlayDAL*.jar"
    }
    into 'libs/'
}

build.dependsOn copyJar
