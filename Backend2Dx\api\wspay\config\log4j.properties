# Root logger option
log4j.rootLogger=DEBUG, wspay
log4j.appender.wspay=org.apache.log4j.DailyRollingFileAppender
log4j.appender.wspay.layout=org.apache.log4j.PatternLayout
log4j.appender.wspay.File=logs/wspay/wspay.log
log4j.appender.wspay.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss,SSS} | %-5p | %t | %c{3} %m%n
log4j.appender.wspay.Encoding=UTF-8
log4j.appender.wspay.DatePattern='.'yyyy-MM-dd

log4j.logger.org.mongodb.driver=INFO
log4j.logger.snaq.db=INFO
log4j.logger.org.eclipse.jetty=INFO

