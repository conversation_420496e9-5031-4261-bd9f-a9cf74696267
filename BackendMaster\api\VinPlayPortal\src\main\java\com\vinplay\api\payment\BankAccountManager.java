package com.vinplay.api.payment;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.vinplay.api.common.Common;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class BankAccountManager {
    
    private static final String CONFIG_FILE = Common.BANK_CONFIG_FILE;
    private static JsonObject bankConfig = null;
    private static final Gson gson = new Gson();
    
    /**
     * Load bank configuration from JSON file
     */
    private static void loadBankConfig() {
        try {
            String content = new String(Files.readAllBytes(Paths.get(CONFIG_FILE)));
            bankConfig = gson.fromJson(content, JsonObject.class);
        } catch (Exception e) {
            // Create default config if file doesn't exist
            createDefaultConfig();
        }
    }
    
    /**
     * Save bank configuration to JSON file
     */
    private static void saveBankConfig() {
        try {
            String json = gson.toJson(bankConfig);
            Files.write(Paths.get(CONFIG_FILE), json.getBytes());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * Create default bank configuration
     */
    private static void createDefaultConfig() {
        bankConfig = new JsonObject();
        
        // Default account
        JsonObject defaultAccount = new JsonObject();
        defaultAccount.addProperty("bank_name", "VCB");
        defaultAccount.addProperty("account_number", "**********");
        defaultAccount.addProperty("account_name", "NGUYEN VAN A");
        defaultAccount.addProperty("status", 1);
        defaultAccount.addProperty("priority", 1);
        defaultAccount.addProperty("daily_limit", ********);
        defaultAccount.addProperty("current_amount", 0);
        defaultAccount.addProperty("created_at", getCurrentTimestamp());
        defaultAccount.addProperty("updated_at", getCurrentTimestamp());
        
        bankConfig.add("default", defaultAccount);
        
        // Accounts array
        JsonArray accounts = new JsonArray();
        accounts.add(defaultAccount);
        bankConfig.add("accounts", accounts);
        
        // Settings
        JsonObject settings = new JsonObject();
        settings.addProperty("auto_switch", true);
        settings.addProperty("switch_threshold", 80);
        settings.addProperty("maintenance_mode", false);
        settings.addProperty("notification_enabled", true);
        settings.addProperty("log_enabled", true);
        bankConfig.add("settings", settings);
        
        saveBankConfig();
    }
    
    /**
     * Get current timestamp
     */
    private static String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * Get active bank account
     */
    public static JsonObject getActiveBankAccount() {
        if (bankConfig == null) {
            loadBankConfig();
        }
        
        JsonArray accounts = bankConfig.getAsJsonArray("accounts");
        if (accounts != null) {
            for (JsonElement element : accounts) {
                JsonObject account = element.getAsJsonObject();
                if (account.get("status").getAsInt() == Common.BANK_STATUS_ACTIVE) {
                    return account;
                }
            }
        }
        
        // Return default if no active account found
        return bankConfig.getAsJsonObject("default");
    }
    
    /**
     * Switch to next available bank account
     */
    public static int switchToNextAccount() {
        if (bankConfig == null) {
            loadBankConfig();
        }
        
        JsonArray accounts = bankConfig.getAsJsonArray("accounts");
        if (accounts == null) {
            return Common.BANK_SWITCH_FAILED;
        }
        
        // Find current active account
        int currentIndex = -1;
        for (int i = 0; i < accounts.size(); i++) {
            JsonObject account = accounts.get(i).getAsJsonObject();
            if (account.get("status").getAsInt() == Common.BANK_STATUS_ACTIVE) {
                currentIndex = i;
                // Deactivate current account
                account.addProperty("status", Common.BANK_STATUS_INACTIVE);
                account.addProperty("updated_at", getCurrentTimestamp());
                break;
            }
        }
        
        // Find next available account
        for (int i = 0; i < accounts.size(); i++) {
            int nextIndex = (currentIndex + 1 + i) % accounts.size();
            JsonObject nextAccount = accounts.get(nextIndex).getAsJsonObject();
            
            if (nextAccount.get("status").getAsInt() != Common.BANK_STATUS_MAINTENANCE) {
                nextAccount.addProperty("status", Common.BANK_STATUS_ACTIVE);
                nextAccount.addProperty("updated_at", getCurrentTimestamp());
                saveBankConfig();
                return Common.BANK_SWITCH_SUCCESS;
            }
        }
        
        return Common.BANK_SWITCH_NOT_FOUND;
    }
    
    /**
     * Switch to specific bank account by ID
     */
    public static int switchToBankAccount(String accountId) {
        if (bankConfig == null) {
            loadBankConfig();
        }
        
        JsonArray accounts = bankConfig.getAsJsonArray("accounts");
        if (accounts == null) {
            return Common.BANK_SWITCH_FAILED;
        }
        
        // Deactivate all accounts first
        for (JsonElement element : accounts) {
            JsonObject account = element.getAsJsonObject();
            account.addProperty("status", Common.BANK_STATUS_INACTIVE);
        }
        
        // Activate target account
        for (JsonElement element : accounts) {
            JsonObject account = element.getAsJsonObject();
            if (accountId.equals(account.get("id").getAsString())) {
                account.addProperty("status", Common.BANK_STATUS_ACTIVE);
                account.addProperty("updated_at", getCurrentTimestamp());
                saveBankConfig();
                return Common.BANK_SWITCH_SUCCESS;
            }
        }
        
        return Common.BANK_SWITCH_NOT_FOUND;
    }
    
    /**
     * Get all bank accounts
     */
    public static JsonArray getAllBankAccounts() {
        if (bankConfig == null) {
            loadBankConfig();
        }
        
        return bankConfig.getAsJsonArray("accounts");
    }
    
    /**
     * Add new bank account
     */
    public static boolean addBankAccount(String id, String bankName, String accountNumber, 
                                       String accountName, int priority, long dailyLimit) {
        if (bankConfig == null) {
            loadBankConfig();
        }
        
        JsonObject newAccount = new JsonObject();
        newAccount.addProperty("id", id);
        newAccount.addProperty("bank_name", bankName);
        newAccount.addProperty("account_number", accountNumber);
        newAccount.addProperty("account_name", accountName);
        newAccount.addProperty("status", Common.BANK_STATUS_INACTIVE);
        newAccount.addProperty("priority", priority);
        newAccount.addProperty("daily_limit", dailyLimit);
        newAccount.addProperty("current_amount", 0);
        newAccount.addProperty("created_at", getCurrentTimestamp());
        newAccount.addProperty("updated_at", getCurrentTimestamp());
        
        JsonArray accounts = bankConfig.getAsJsonArray("accounts");
        if (accounts == null) {
            accounts = new JsonArray();
            bankConfig.add("accounts", accounts);
        }
        
        accounts.add(newAccount);
        saveBankConfig();
        return true;
    }
    
    /**
     * Update bank account status
     */
    public static boolean updateBankAccountStatus(String accountId, int status) {
        if (bankConfig == null) {
            loadBankConfig();
        }
        
        JsonArray accounts = bankConfig.getAsJsonArray("accounts");
        if (accounts == null) {
            return false;
        }
        
        for (JsonElement element : accounts) {
            JsonObject account = element.getAsJsonObject();
            if (accountId.equals(account.get("id").getAsString())) {
                account.addProperty("status", status);
                account.addProperty("updated_at", getCurrentTimestamp());
                saveBankConfig();
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if auto switch is enabled
     */
    public static boolean isAutoSwitchEnabled() {
        if (bankConfig == null) {
            loadBankConfig();
        }
        
        JsonObject settings = bankConfig.getAsJsonObject("settings");
        if (settings != null && settings.has("auto_switch")) {
            return settings.get("auto_switch").getAsBoolean();
        }
        
        return false;
    }
    
    /**
     * Get switch threshold percentage
     */
    public static int getSwitchThreshold() {
        if (bankConfig == null) {
            loadBankConfig();
        }

        JsonObject settings = bankConfig.getAsJsonObject("settings");
        if (settings != null && settings.has("switch_threshold")) {
            return settings.get("switch_threshold").getAsInt();
        }

        return 80; // Default threshold
    }

    /**
     * Get all bank accounts as JSON string
     */
    public static String getAllBankAccountsJson() {
        if (bankConfig == null) {
            loadBankConfig();
        }

        JsonArray accounts = bankConfig.getAsJsonArray("accounts");
        if (accounts == null) {
            return "[]";
        }
        return accounts.toString();
    }
}
