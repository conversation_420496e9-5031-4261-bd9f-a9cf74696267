package com.vinplay.api.payment;

import com.vinplay.api.common.Common;
import com.vinplay.api.common.DomainDetector;
import com.google.gson.JsonObject;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@WebServlet("/api/bank-management")
public class BankManagementServlet extends HttpServlet {
    
    private pay payService = new pay();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        handleRequest(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        handleRequest(request, response);
    }
    
    private void handleRequest(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        // Set response headers
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type");
        
        // Detect domain from request
        DomainDetector.detectDomainFromRequest(request);
        
        // Get parameters
        String c = request.getParameter("c");
        String key = request.getParameter("key");
        String accountId = request.getParameter("account_id");
        
        // Verify API key
        if (!"BopVuEmVo123".equals(key)) {
            response.getWriter().write("{\"success\":false,\"message\":\"Invalid API key\"}");
            return;
        }
        
        try {
            String result = "";
            
            if ("3057".equals(c)) { // Switch bank account
                if (accountId != null && !accountId.isEmpty()) {
                    int switchResult = payService.switchBankAccount(accountId);
                    if (switchResult == Common.BANK_SWITCH_SUCCESS) {
                        result = "{\"success\":true,\"message\":\"Bank account switched successfully\"}";
                    } else if (switchResult == Common.BANK_SWITCH_ACCOUNT_NOT_FOUND) {
                        result = "{\"success\":false,\"message\":\"Bank account not found\"}";
                    } else if (switchResult == Common.BANK_SWITCH_ACCOUNT_INACTIVE) {
                        result = "{\"success\":false,\"message\":\"Bank account is inactive\"}";
                    } else {
                        result = "{\"success\":false,\"message\":\"Failed to switch bank account\"}";
                    }
                } else {
                    result = "{\"success\":false,\"message\":\"Account ID is required\"}";
                }
                
            } else if ("3058".equals(c)) { // Get current bank account
                JsonObject currentAccount = payService.getCurrentBankAccount();
                if (currentAccount != null) {
                    String accountInfo = currentAccount.toString();
                    result = "{\"success\":true,\"account\":" + accountInfo + "}";
                } else {
                    result = "{\"success\":false,\"message\":\"No active bank account found\"}";
                }
                
            } else if ("3059".equals(c)) { // Switch to next bank account
                int switchResult = payService.switchToNextBankAccount();
                if (switchResult == Common.BANK_SWITCH_SUCCESS) {
                    JsonObject newAccount = payService.getCurrentBankAccount();
                    if (newAccount != null) {
                        result = "{\"success\":true,\"message\":\"Switched to next bank account\",\"new_account\":" + newAccount.toString() + "}";
                    } else {
                        result = "{\"success\":true,\"message\":\"Switched to next bank account\"}";
                    }
                } else if (switchResult == Common.BANK_SWITCH_NO_AVAILABLE_ACCOUNTS) {
                    result = "{\"success\":false,\"message\":\"No available bank accounts to switch to\"}";
                } else {
                    result = "{\"success\":false,\"message\":\"Failed to switch to next bank account\"}";
                }
                
            } else if ("3060".equals(c)) { // Get all bank accounts
                try {
                    BankAccountManager bankManager = new BankAccountManager();
                    String allAccounts = bankManager.getAllBankAccountsJson();
                    result = "{\"success\":true,\"accounts\":" + allAccounts + "}";
                } catch (Exception e) {
                    result = "{\"success\":false,\"message\":\"Failed to get bank accounts: " + e.getMessage() + "\"}";
                }
                
            } else if ("3061".equals(c)) { // Update bank account status
                String status = request.getParameter("status");
                if (accountId != null && !accountId.isEmpty() && status != null) {
                    try {
                        BankAccountManager bankManager = new BankAccountManager();
                        int statusInt = Integer.parseInt(status);
                        boolean updateResult = bankManager.updateBankAccountStatus(accountId, statusInt);
                        if (updateResult) {
                            result = "{\"success\":true,\"message\":\"Bank account status updated successfully\"}";
                        } else {
                            result = "{\"success\":false,\"message\":\"Failed to update bank account status\"}";
                        }
                    } catch (NumberFormatException e) {
                        result = "{\"success\":false,\"message\":\"Invalid status value\"}";
                    } catch (Exception e) {
                        result = "{\"success\":false,\"message\":\"Error updating status: " + e.getMessage() + "\"}";
                    }
                } else {
                    result = "{\"success\":false,\"message\":\"Account ID and status are required\"}";
                }
                
            } else {
                result = "{\"success\":false,\"message\":\"Invalid command. Supported commands: 3057 (switch), 3058 (get current), 3059 (switch next), 3060 (get all), 3061 (update status)\"}";
            }
            
            response.getWriter().write(result);
            
        } catch (Exception e) {
            e.printStackTrace();
            String errorResult = "{\"success\":false,\"message\":\"Internal server error: " + e.getMessage().replace("\"", "\\\"") + "\"}";
            response.getWriter().write(errorResult);
        }
    }
    
    @Override
    protected void doOptions(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type");
        response.setStatus(HttpServletResponse.SC_OK);
    }
}
