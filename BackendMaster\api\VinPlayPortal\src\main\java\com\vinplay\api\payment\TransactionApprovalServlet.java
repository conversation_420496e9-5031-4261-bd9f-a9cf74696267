package com.vinplay.api.payment;

import com.vinplay.api.common.Common;
import com.vinplay.api.common.DomainDetector;
import com.vinplay.payment.service.PaymentManualService;
import com.vinplay.payment.service.WithDrawManualBankService;
import com.vinplay.payment.service.RechargeManualBankService;
import com.vinplay.payment.service.impl.PaymentManualServiceImpl;
import com.vinplay.payment.service.impl.WithDrawManualBankServiceImpl;
import com.vinplay.payment.service.impl.RechargeManualBankServiceImpl;
import com.vinplay.payment.entities.DepositPaygateModel;
import com.vinplay.payment.entities.WithDrawPaygateModel;
import com.vinplay.payment.dao.RechargePaygateDao;
import com.vinplay.payment.dao.WithDrawPaygateDao;
import com.vinplay.payment.dao.impl.RechargePaygateDaoImpl;
import com.vinplay.payment.dao.impl.WithDrawPaygateDaoImpl;
import com.vinplay.dichvuthe.response.RechargePaywellResponse;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import java.util.ArrayList;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@WebServlet("/api/transaction-approval")
public class TransactionApprovalServlet extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        handleRequest(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        handleRequest(request, response);
    }
    
    private void handleRequest(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        // Set response headers
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type");
        
        // Detect domain from request
        DomainDetector.detectDomainFromRequest(request);
        
        // Get parameters
        String command = request.getParameter("c");
        String type = request.getParameter("type"); // deposit or withdrawal
        String action = request.getParameter("action"); // approve or reject
        String orderId = request.getParameter("order_id");
        String transactionId = request.getParameter("transaction_id");
        String cartId = request.getParameter("cart_id");
        String bankNumber = request.getParameter("bank_number");
        String reason = request.getParameter("reason");
        String adminName = request.getParameter("admin_name");
        String key = request.getParameter("key");
        String fromDate = request.getParameter("from_date");
        String toDate = request.getParameter("to_date");
        String status = request.getParameter("status");
        String ip = request.getRemoteAddr();

        // Verify API key for approval actions
        if ((action != null || "3062".equals(command) || "3063".equals(command)) && !"BopVuEmVo123".equals(key)) {
            response.getWriter().write("{\"success\":false,\"message\":\"Invalid API key\"}");
            return;
        }

        try {
            String result = "";

            // Handle different commands
            if ("3062".equals(command)) {
                // Get pending deposits
                result = getPendingDeposits(fromDate, toDate, status);

            } else if ("3063".equals(command)) {
                // Get pending withdrawals
                result = getPendingWithdrawals(fromDate, toDate, status);

            } else if ("deposit".equals(type)) {
                // Handle deposit approval/rejection
                result = handleDepositApproval(orderId, action, reason, adminName, ip);

            } else if ("withdrawal".equals(type)) {
                // Handle withdrawal approval/rejection
                result = handleWithdrawalApproval(transactionId, cartId, bankNumber, action, reason, adminName);

            } else {
                result = "{\"success\":false,\"message\":\"Invalid command or transaction type\"}";
            }

            response.getWriter().write(result);

        } catch (Exception e) {
            e.printStackTrace();
            String errorResult = "{\"success\":false,\"message\":\"Internal server error: " + e.getMessage().replace("\"", "\\\"") + "\"}";
            response.getWriter().write(errorResult);
        }
    }
    
    /**
     * Handle deposit approval/rejection
     */
    private String handleDepositApproval(String orderId, String action, String reason, String adminName, String ip) {
        try {
            PaymentManualService paymentService = new PaymentManualServiceImpl();
            RechargePaywellResponse response;
            
            if ("approve".equals(action)) {
                // Approve deposit - status 2 (SUCCESS)
                response = paymentService.depositConfirm(orderId, adminName, ip, 2, "Approved by admin: " + adminName);
                
            } else if ("reject".equals(action)) {
                // Reject deposit - status 3 (FAILED)
                String rejectReason = reason != null && !reason.isEmpty() ? reason : "Rejected by admin: " + adminName;
                response = paymentService.depositConfirm(orderId, adminName, ip, 3, rejectReason);
                
            } else {
                return "{\"success\":false,\"message\":\"Invalid action. Use 'approve' or 'reject'\"}";
            }
            
            if (response != null && response.getCode() == 0) {
                return "{\"success\":true,\"message\":\"Deposit " + action + " successfully\",\"order_id\":\"" + orderId + "\"}";
            } else {
                String errorMsg = response != null ? response.getData() : "Unknown error";
                return "{\"success\":false,\"message\":\"Failed to " + action + " deposit: " + errorMsg + "\"}";
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"success\":false,\"message\":\"Error processing deposit " + action + ": " + e.getMessage() + "\"}";
        }
    }
    
    /**
     * Handle withdrawal approval/rejection
     */
    private String handleWithdrawalApproval(String transactionId, String cartId, String bankNumber, 
                                          String action, String reason, String adminName) {
        try {
            WithDrawManualBankService withdrawService = new WithDrawManualBankServiceImpl();
            RechargePaywellResponse response;
            
            if ("approve".equals(action)) {
                // Approve withdrawal
                response = withdrawService.Approved(transactionId, adminName, cartId, bankNumber);
                
            } else if ("reject".equals(action)) {
                // Reject withdrawal
                response = withdrawService.Reject(transactionId, adminName);
                
            } else {
                return "{\"success\":false,\"message\":\"Invalid action. Use 'approve' or 'reject'\"}";
            }
            
            if (response != null && response.getCode() == 0) {
                return "{\"success\":true,\"message\":\"Withdrawal " + action + " successfully\",\"transaction_id\":\"" + transactionId + "\"}";
            } else {
                String errorMsg = response != null ? response.getData() : "Unknown error";
                return "{\"success\":false,\"message\":\"Failed to " + action + " withdrawal: " + errorMsg + "\"}";
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"success\":false,\"message\":\"Error processing withdrawal " + action + ": " + e.getMessage() + "\"}";
        }
    }
    
    /**
     * Get pending deposits
     */
    private String getPendingDeposits(String fromDate, String toDate, String status) {
        try {
            RechargePaygateDao depositDao = new RechargePaygateDaoImpl();

            // Default values if not provided
            if (fromDate == null || fromDate.isEmpty()) {
                fromDate = Common.getDateString(-7); // 7 days ago
            }
            if (toDate == null || toDate.isEmpty()) {
                toDate = Common.getDateString(0); // today
            }
            if (status == null || status.isEmpty()) {
                status = "0"; // pending
            }

            // Get deposits from database
            ArrayList<DepositPaygateModel> deposits = depositDao.getDepositsByDateAndStatus(fromDate, toDate, Integer.parseInt(status));

            JsonArray jsonArray = new JsonArray();
            Gson gson = new Gson();

            for (DepositPaygateModel deposit : deposits) {
                JsonObject jsonDeposit = new JsonObject();
                jsonDeposit.addProperty("id", deposit.getId());
                jsonDeposit.addProperty("cart_id", deposit.getCartId());
                jsonDeposit.addProperty("username", deposit.getUsername());
                jsonDeposit.addProperty("nickname", deposit.getNickname());
                jsonDeposit.addProperty("amount", deposit.getAmount());
                jsonDeposit.addProperty("bank_name", deposit.getBankName());
                jsonDeposit.addProperty("bank_account", deposit.getBankAccount());
                jsonDeposit.addProperty("account_name", deposit.getAccountName());
                jsonDeposit.addProperty("request_time", deposit.getRequestTime());
                jsonDeposit.addProperty("status", deposit.getStatus());
                jsonDeposit.addProperty("description", deposit.getDescription());
                jsonArray.add(jsonDeposit);
            }

            JsonObject result = new JsonObject();
            result.addProperty("success", true);
            result.addProperty("count", deposits.size());
            result.add("data", jsonArray);

            return gson.toJson(result);

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"success\":false,\"message\":\"Error getting pending deposits: " + e.getMessage() + "\"}";
        }
    }

    /**
     * Get pending withdrawals
     */
    private String getPendingWithdrawals(String fromDate, String toDate, String status) {
        try {
            WithDrawPaygateDao withdrawDao = new WithDrawPaygateDaoImpl();

            // Default values if not provided
            if (fromDate == null || fromDate.isEmpty()) {
                fromDate = Common.getDateString(-7); // 7 days ago
            }
            if (toDate == null || toDate.isEmpty()) {
                toDate = Common.getDateString(0); // today
            }
            if (status == null || status.isEmpty()) {
                status = "0"; // pending
            }

            // Get withdrawals from database
            ArrayList<WithDrawPaygateModel> withdrawals = withdrawDao.getWithdrawalsByDateAndStatus(fromDate, toDate, Integer.parseInt(status));

            JsonArray jsonArray = new JsonArray();
            Gson gson = new Gson();

            for (WithDrawPaygateModel withdrawal : withdrawals) {
                JsonObject jsonWithdrawal = new JsonObject();
                jsonWithdrawal.addProperty("id", withdrawal.getId());
                jsonWithdrawal.addProperty("cart_id", withdrawal.getCartId());
                jsonWithdrawal.addProperty("username", withdrawal.getUsername());
                jsonWithdrawal.addProperty("nickname", withdrawal.getNickname());
                jsonWithdrawal.addProperty("amount", withdrawal.getAmount());
                jsonWithdrawal.addProperty("amount_real", withdrawal.getAmountReal());
                jsonWithdrawal.addProperty("bank_name", withdrawal.getBankName());
                jsonWithdrawal.addProperty("bank_account", withdrawal.getBankAccount());
                jsonWithdrawal.addProperty("account_name", withdrawal.getAccountName());
                jsonWithdrawal.addProperty("request_time", withdrawal.getRequestTime());
                jsonWithdrawal.addProperty("status", withdrawal.getStatus());
                jsonWithdrawal.addProperty("description", withdrawal.getDescription());
                jsonWithdrawal.addProperty("fee", withdrawal.getFee());
                jsonArray.add(jsonWithdrawal);
            }

            JsonObject result = new JsonObject();
            result.addProperty("success", true);
            result.addProperty("count", withdrawals.size());
            result.add("data", jsonArray);

            return gson.toJson(result);

        } catch (Exception e) {
            e.printStackTrace();
            return "{\"success\":false,\"message\":\"Error getting pending withdrawals: " + e.getMessage() + "\"}";
        }
    }

    @Override
    protected void doOptions(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type");
        response.setStatus(HttpServletResponse.SC_OK);
    }
}
