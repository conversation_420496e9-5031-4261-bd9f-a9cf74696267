package com.vinplay.api.payment;

import com.google.gson.Gson;
import com.vinplay.api.XulyThongBao.TelegramAlert;
import com.vinplay.api.common.Common;
import com.vinplay.api.common.DomainDetector;
import com.vinplay.api.common.HttpCommon;
import com.vinplay.api.common.MD5Common;
import com.vinplay.api.common.RSASignature;
import com.vinplay.api.payment.A08.Entity.MoMoQRCode;
import com.vinplay.api.payment.entity.*;
import com.google.gson.JsonObject;
import okhttp3.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import java.util.*;

public class pay {

    public String getTimestamp(){
        long timestamp1 = System.currentTimeMillis();
        String timestamp = timestamp1+"";
        return timestamp;
    }


    public synchronized String payCard2(String maThe, String seRi, int menhGia, int type, String trans_id){
        try {
            String type_card = "";
            if(type == 1){
                type_card = Common.CARD_VIETTEL;
            }else if(type == 2){
                type_card = Common.CARD_VINAPHONE;
            }else if(type == 3){
                type_card = Common.CARD_MOBIFONE;
            }else if(type == 4){
                type_card = Common.CARD_VIETNAMOBILE;
            }else{
                type_card = Common.CARD_VIETTEL;
            }

            String stringToHash = "attach=attach&cardNumber="+seRi+"&mchId="+Common.MERCHANTID+"&nonceStr="+trans_id+"&notifyUrl="+Common.CALL_BACK_NAP_NEW+"&outTradeNo="+trans_id+"&password="+maThe+"&payAmount="+menhGia+".000&tradeType="+Common.TRADETYPE_VNCARD;
            String privateKey= Common.PRIVATE_KEY_NAP;
            String sign =RSASignature.sign(stringToHash, privateKey);

            OkHttpClient client = HttpCommon.getInstance().getHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, "{\r\n  \"mchId\": \""+Common.MERCHANTID+"\",\r\n  \"nonceStr\": \""+trans_id+"\",\r\n  \"notifyUrl\": \""+Common.CALL_BACK_NAP_NEW+"\",\r\n  \"outTradeNo\": \""+trans_id+"\",\r\n  \"payAmount\": \""+menhGia+".000\",\r\n  \"tradeType\": \""+Common.TRADETYPE_VNCARD+"\",\r\n  \"attach\": \""+Common.ATTACH+"\",\r\n  \"cardNumber\": \""+seRi+"\",\r\n  \"password\": \""+maThe+"\",\r\n  \"sign\": \""+sign+"\"\r\n }");
            Request request = new Request.Builder()
                    .url(Common.URL_NAP_NEW)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
            return result;
        }catch (Exception e){
            return null;
        }
    }

    public synchronized ResponsePay2 payBankTransferPay247(long mount, String trans_id){
        try {
            Gson gson = new Gson();
            String callbackUrl = DomainDetector.getPaymentCallbackUrl();
            String signString = Common.MERCHANTNUM+trans_id+mount+callbackUrl+Common.KEYBAOMATPAY247;
            String signature = MD5Common.md5Generate(signString);
            OkHttpClient client = HttpCommon.getInstance().getHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("text/plain");
            RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                    .addFormDataPart("merchantNum",Common.MERCHANTNUM)
                    .addFormDataPart("payType",Common.PAYTYPE)
                    .addFormDataPart("amount",mount+"")
                    .addFormDataPart("orderNo",trans_id)
                    .addFormDataPart("notifyUrl",callbackUrl)
                    .addFormDataPart("systemSorce",Common.SYSTEMSCORE)
                    .addFormDataPart("sign",signature)
                    .build();
            Request request = new Request.Builder()
                    .url(Common.URL_PAYIN_247)
                    .method("POST", body)
                    .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
//            TelegramAlert tele = new TelegramAlert();
//            tele.SendNotifyDebug("Debug nap tien: ",result);
            ResponsePay247 pay247Entity = gson.fromJson(result, ResponsePay247.class);
            ResponsePay2 resp2 = new ResponsePay2();
            resp2.setStatus(pay247Entity.getMsg());
            resp2.setMsg(pay247Entity.getMsg());
            resp2.setTran_id(pay247Entity.getData().getBillNo());
            resp2.setBankname(pay247Entity.getData().getBankAddress());
            resp2.setStk(pay247Entity.getData().getBankCode());
            resp2.setName(pay247Entity.getData().getBankUsername());
            resp2.setComment(pay247Entity.getData().getPostscriptCode());
            String qrLink = "https://img.vietqr.io/image/"+pay247Entity.getData().getBankAddress()+"-"+pay247Entity.getData().getBankCode()+"-qr_only.jpg?amount="+mount+"&addInfo="+pay247Entity.getData().getPostscriptCode()+"&accountName="+pay247Entity.getData().getBankUsername();
            resp2.setQr(qrLink);
            resp2.setUrl_pay(pay247Entity.getData().getPayUrl());
            if(resp2.getBankname().equalsIgnoreCase("MB")){
                // Try to switch to next bank account if MB bank is returned
                if (BankAccountManager.isAutoSwitchEnabled()) {
                    int switchResult = BankAccountManager.switchToNextAccount();
                    if (switchResult == Common.BANK_SWITCH_SUCCESS) {
                        // Retry with new bank account
                        return payBankTransferPay247(mount, trans_id);
                    }
                }
                return null;
            }else{
                return resp2;
            }
        }catch (Exception e){
            TelegramAlert tele = new TelegramAlert();
            tele.SendNotifyDebug("Debug nap tien: ",e+"");
            return null;
        }
    }

    public synchronized ResponsePay2 payBankTransferA08(long mount, String trans_id){
        try {
            //            TelegramAlert tele = new TelegramAlert();
            Gson gson = new Gson();
            vndBankEntity vndEnty = getBankVndA08();
            ArrayList<String> listBank = new ArrayList<>();
            for(String bn : vndEnty.getData()[0].getBank()){
                listBank.add(bn);
            }

            // Sử dụng Random để lấy một phần tử ngẫu nhiên
            Random rand = new Random();
            String payName = listBank.get(rand.nextInt(listBank.size()));

            String stringToHash = "attach=attach&mchId="+Common.MERCHANTID+"&nonceStr="+trans_id+"&notifyUrl="+Common.CALL_BACK_NAP_NEW+"&outTradeNo="+trans_id+"&payAmount="+mount+".000&payBank="+payName+"&tradeType="+Common.TRADETYPE;
            String privateKey= Common.PRIVATE_KEY_NAP;
            String sign =RSASignature.sign(stringToHash, privateKey);

            OkHttpClient client = HttpCommon.getInstance().getHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, "{\r\n  \"mchId\": \""+Common.MERCHANTID+"\",\r\n  \"nonceStr\": \""+trans_id+"\",\r\n  \"notifyUrl\": \""+Common.CALL_BACK_NAP_NEW+"\",\r\n  \"outTradeNo\": \""+trans_id+"\",\r\n  \"payAmount\": \""+mount+".000\",\r\n  \"tradeType\": \""+Common.TRADETYPE+"\",\r\n  \"attach\": \""+Common.ATTACH+"\",\r\n  \"payBank\": \""+payName+"\",\r\n  \"sign\": \""+sign+"\"\r\n }");
            Request request = new Request.Builder()
                    .url(Common.URL_NAP_NEW)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
//            tele.SendNotifyDebug("Response nap Bank: ",result);
            ResponseNewEntity responseEntity = gson.fromJson(result, ResponseNewEntity.class);
            ResponsePay2 resp2 = new ResponsePay2();
            resp2.setStatus(responseEntity.getMsg());
            resp2.setMsg(responseEntity.getMsg());
            resp2.setTran_id(responseEntity.getPayOrderid());
            resp2.setBankname(responseEntity.getData().getBankInfo().getBank());
            resp2.setStk(responseEntity.getData().getBankInfo().getCardnumber());
            resp2.setName(responseEntity.getData().getBankInfo().getAccount());
            resp2.setComment(responseEntity.getData().getBankInfo().getPostscript());
            String qrLink = "https://img.vietqr.io/image/"+responseEntity.getData().getBankInfo().getBank()+"-"+responseEntity.getData().getBankInfo().getCardnumber()+"-qr_only.jpg?amount="+mount+"&addInfo="+responseEntity.getData().getBankInfo().getPostscript()+"&accountName="+responseEntity.getData().getBankInfo().getAccount();
            resp2.setQr(qrLink);
            resp2.setUrl_pay(responseEntity.getRedirect());
            return resp2;
        }catch (Exception e){
            TelegramAlert tele = new TelegramAlert();
            tele.SendNotifyDebug("Debug nap tien: ",e+"");
            return null;
        }
    }

    public synchronized ResponsePay2 payBankTransferOKDPay(long mount, String trans_id){
        try {
            String mchid = "9098";
            String moneyFinal = mount+".00";
            String urlCallBack = DomainDetector.buildCallbackUrl("mid", "/api/auth/callbackokdpay");
            String code = "1001";
            String key = "KaJUWBgPonuAFc003jta1DPVSyOoG1mSz9GPiCSrDOsYtgIbXqKQBajOvn3ySB83";
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String applydate = LocalDateTime.now().format(formatter);
            Gson gson = new Gson();
            String signString = "applydate="+applydate+"&code="+code+"&mchid="+mchid+"&money="+moneyFinal+"&notifyurl="+urlCallBack+"&out_trade_no="+trans_id+"&key="+key;
            String signatureTmp = MD5Common.md5Generate(signString);
            String signature = signatureTmp.toUpperCase();
            OkHttpClient client = HttpCommon.getInstance().getHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("text/plain");
            RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                    .addFormDataPart("mchid",mchid)
                    .addFormDataPart("out_trade_no",trans_id)
                    .addFormDataPart("money",moneyFinal)
                    .addFormDataPart("notifyurl",urlCallBack)
                    .addFormDataPart("code",code)
                    .addFormDataPart("applydate",applydate)
                    .addFormDataPart("returnurl","rikvip99.autos")
                    .addFormDataPart("productname","")
                    .addFormDataPart("attach","")
                    .addFormDataPart("submitname","")
                    .addFormDataPart("sign",signature)
                    .build();
            Request request = new Request.Builder()
                    .url("https://shapi.okdpay888.top/v1/dsapi/add2")
                    .method("POST", body)
                    .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
            ResponseOKDPayEntity respEnity = gson.fromJson(result, ResponseOKDPayEntity.class);
            String html = getHtmlOKDPay(respEnity.getPay_url());
            HtmlParser parser = new HtmlParser();
            InfoBankOKDPayEntity infoBank = parser.getInfoBankOKD(html);
            ResponsePay2 resp2 = new ResponsePay2();
            resp2.setStatus("success");
            resp2.setMsg("");
            resp2.setTran_id(trans_id);
            resp2.setBankname(infoBank.getBankName());
            resp2.setStk(infoBank.getBankNumber());
            resp2.setName(infoBank.getBankAccount());
            resp2.setComment(infoBank.getComment());
            String qrLink = "https://img.vietqr.io/image/"+infoBank.getBankName()+"-"+infoBank.getBankNumber()+"-qr_only.jpg?amount="+mount+"&addInfo="+infoBank.getComment()+"&accountName="+infoBank.getBankAccount();
            resp2.setQr(qrLink);
            resp2.setUrl_pay(respEnity.getPay_url());
            return resp2;
        }catch (Exception e){
            TelegramAlert tele = new TelegramAlert();
            tele.SendNotifyDebug("Debug nap tien: ",e+"");
            return null;
        }
    }

    public synchronized ResponsePay2 payBankTransferBaoTri(long mount, String trans_id){
        try {
            return null;
        }catch (Exception e){
            TelegramAlert tele = new TelegramAlert();
            tele.SendNotifyDebug("Debug nap tien: ",e+"");
            return null;
        }
    }

    public synchronized ResponsePayMomo2 payMoMo2(long mount, String trans_id){
        try {
            TelegramAlert tele = new TelegramAlert();
            Gson gson = new Gson();

            String stringToHash = "attach=attach&mchId="+Common.MERCHANTID+"&nonceStr="+trans_id+"&notifyUrl="+Common.CALL_BACK_NAP_NEW+"&outTradeNo="+trans_id+"&payAmount="+mount+"&tradeType="+Common.TRADETYPE_MOMO;
            String privateKey= Common.PRIVATE_KEY_NAP;
            String sign =RSASignature.sign(stringToHash, privateKey);

            OkHttpClient client = HttpCommon.getInstance().getHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, "{\r\n  \"mchId\": \""+Common.MERCHANTID+"\",\r\n  \"nonceStr\": \""+trans_id+"\",\r\n  \"notifyUrl\": \""+Common.CALL_BACK_NAP_NEW+"\",\r\n  \"outTradeNo\": \""+trans_id+"\",\r\n  \"payAmount\": \""+mount+"\",\r\n  \"tradeType\": \""+Common.TRADETYPE_MOMO+"\",\r\n  \"attach\": \""+Common.ATTACH+"\",\r\n  \"sign\": \""+sign+"\"\r\n }");
            Request request = new Request.Builder()
                    .url(Common.URL_NAP_NEW)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
//            tele.SendNotifyDebug("Request nap MoMo: ","{\r\n  \"mchId\": \""+Common.MERCHANTID+"\",\r\n  \"nonceStr\": \""+trans_id+"\",\r\n  \"notifyUrl\": \""+Common.CALL_BACK_NAP_NEW+"\",\r\n  \"outTradeNo\": \""+trans_id+"\",\r\n  \"payAmount\": \""+mount+"\",\r\n  \"tradeType\": \""+Common.TRADETYPE_MOMO+"\",\r\n  \"attach\": \""+Common.ATTACH+"\",\r\n  \"sign\": \""+sign+"\"\r\n }");
//            tele.SendNotifyDebug("Response nap MoMo: ",result);
            ResponseNewEntity responseEntity = gson.fromJson(result, ResponseNewEntity.class);
            ResponsePayMomo2 resp2 = new ResponsePayMomo2();
            resp2.setStatus(responseEntity.getMsg());
            resp2.setMsg(responseEntity.getMsg());
            resp2.setTran_id(responseEntity.getPayOrderid());
            resp2.setBankname(responseEntity.getData().getBankInfo().getBank());
            resp2.setPhone(responseEntity.getData().getBankInfo().getCardnumber());
            resp2.setName(responseEntity.getData().getBankInfo().getAccount());
            resp2.setComment(responseEntity.getData().getBankInfo().getPostscript());
            resp2.setUrl_pay(responseEntity.getData().getRedirect());
            OkHttpClient client2 = HttpCommon.getInstance().getHttpClient().newBuilder()
                    .build();
            MediaType mediaType2 = MediaType.parse("application/json");
            RequestBody body2 = RequestBody.create(mediaType2, "{\"url\":\""+responseEntity.getData().getRedirect()+"\",\"platform\":\"Momo\"}");
            Request request2 = new Request.Builder()
                    .url("http://************:4444/base64")
                    .method("POST", body2)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response2 = client2.newCall(request2).execute();
            String ketQua = response2.body().string();
            MoMoQRCode mm = gson.fromJson(ketQua, MoMoQRCode.class);
            resp2.setQr(mm.getImg_src());
            return resp2;
        }catch (Exception e){
            TelegramAlert tele = new TelegramAlert();
            tele.SendNotifyDebug("Debug nap tien: ",e+"");
            return null;
        }
    }

    public synchronized ResponsePayMomo2 payZaloPay2(long mount, String trans_id){
        try {
            TelegramAlert tele = new TelegramAlert();
            Gson gson = new Gson();

            String stringToHash = "attach=attach&mchId="+Common.MERCHANTID+"&nonceStr="+trans_id+"&notifyUrl="+Common.CALL_BACK_NAP_NEW+"&outTradeNo="+trans_id+"&payAmount="+mount+"&tradeType="+Common.TRADETYPE_ZALO_PAY;
            String privateKey= Common.PRIVATE_KEY_NAP;
            String sign =RSASignature.sign(stringToHash, privateKey);

            OkHttpClient client = HttpCommon.getInstance().getHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, "{\r\n  \"mchId\": \""+Common.MERCHANTID+"\",\r\n  \"nonceStr\": \""+trans_id+"\",\r\n  \"notifyUrl\": \""+Common.CALL_BACK_NAP_NEW+"\",\r\n  \"outTradeNo\": \""+trans_id+"\",\r\n  \"payAmount\": \""+mount+"\",\r\n  \"tradeType\": \""+Common.TRADETYPE_ZALO_PAY+"\",\r\n  \"attach\": \""+Common.ATTACH+"\",\r\n  \"sign\": \""+sign+"\"\r\n }");
            Request request = new Request.Builder()
                    .url(Common.URL_NAP_NEW)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
//            tele.SendNotifyDebug("Request nap MoMo: ","{\r\n  \"mchId\": \""+Common.MERCHANTID+"\",\r\n  \"nonceStr\": \""+trans_id+"\",\r\n  \"notifyUrl\": \""+Common.CALL_BACK_NAP_NEW+"\",\r\n  \"outTradeNo\": \""+trans_id+"\",\r\n  \"payAmount\": \""+mount+"\",\r\n  \"tradeType\": \""+Common.TRADETYPE_ZALO_PAY+"\",\r\n  \"attach\": \""+Common.ATTACH+"\",\r\n  \"sign\": \""+sign+"\"\r\n }");
//            tele.SendNotifyDebug("Response nap Zalo: ",result);
            ResponseNewEntity responseEntity = gson.fromJson(result, ResponseNewEntity.class);
            ResponsePayMomo2 resp2 = new ResponsePayMomo2();
            resp2.setStatus(responseEntity.getMsg());
            resp2.setMsg(responseEntity.getMsg());
            resp2.setTran_id(responseEntity.getPayOrderid());
            resp2.setBankname(responseEntity.getData().getBankInfo().getBank());
            resp2.setPhone(responseEntity.getData().getBankInfo().getCardnumber());
            resp2.setName(responseEntity.getData().getBankInfo().getAccount());
            resp2.setComment(responseEntity.getData().getBankInfo().getPostscript());
            resp2.setUrl_pay(responseEntity.getData().getRedirect());
            OkHttpClient client2 = HttpCommon.getInstance().getHttpClient().newBuilder()
                    .build();
            MediaType mediaType2 = MediaType.parse("application/json");
            RequestBody body2 = RequestBody.create(mediaType2, "{\"url\":\""+responseEntity.getData().getRedirect()+"\",\"platform\":\"Momo\"}");
            Request request2 = new Request.Builder()
                    .url("http://************:4444/base64")
                    .method("POST", body2)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response2 = client2.newCall(request2).execute();
            String ketQua = response2.body().string();
            MoMoQRCode mm = gson.fromJson(ketQua, MoMoQRCode.class);
            resp2.setQr(mm.getImg_src());
            return resp2;
        }catch (Exception e){
            TelegramAlert tele = new TelegramAlert();
            tele.SendNotifyDebug("Debug nap tien: ",e+"");
            return null;
        }
    }

    public synchronized ResponsePayMomo2 payViettelPay2(long mount, String trans_id){
        try {
            TelegramAlert tele = new TelegramAlert();
            Gson gson = new Gson();

            String stringToHash = "attach=attach&mchId="+Common.MERCHANTID+"&nonceStr="+trans_id+"&notifyUrl="+Common.CALL_BACK_NAP_NEW+"&outTradeNo="+trans_id+"&payAmount="+mount+"&tradeType="+Common.TRADETYPE_VIETTEL_PAY;
            String privateKey= Common.PRIVATE_KEY_NAP;
            String sign =RSASignature.sign(stringToHash, privateKey);

            OkHttpClient client = HttpCommon.getInstance().getHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, "{\r\n  \"mchId\": \""+Common.MERCHANTID+"\",\r\n  \"nonceStr\": \""+trans_id+"\",\r\n  \"notifyUrl\": \""+Common.CALL_BACK_NAP_NEW+"\",\r\n  \"outTradeNo\": \""+trans_id+"\",\r\n  \"payAmount\": \""+mount+"\",\r\n  \"tradeType\": \""+Common.TRADETYPE_VIETTEL_PAY+"\",\r\n  \"attach\": \""+Common.ATTACH+"\",\r\n  \"sign\": \""+sign+"\"\r\n }");
            Request request = new Request.Builder()
                    .url(Common.URL_NAP_NEW)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
//            tele.SendNotifyDebug("Request nap MoMo: ","{\r\n  \"mchId\": \""+Common.MERCHANTID+"\",\r\n  \"nonceStr\": \""+trans_id+"\",\r\n  \"notifyUrl\": \""+Common.CALL_BACK_NAP_NEW+"\",\r\n  \"outTradeNo\": \""+trans_id+"\",\r\n  \"payAmount\": \""+mount+"\",\r\n  \"tradeType\": \""+Common.TRADETYPE_VIETTEL_PAY+"\",\r\n  \"attach\": \""+Common.ATTACH+"\",\r\n  \"sign\": \""+sign+"\"\r\n }");
//            tele.SendNotifyDebug("Response nap Viettel Pay: ",result);
            ResponseNewEntity responseEntity = gson.fromJson(result, ResponseNewEntity.class);
            ResponsePayMomo2 resp2 = new ResponsePayMomo2();
            resp2.setStatus(responseEntity.getMsg());
            resp2.setMsg(responseEntity.getMsg());
            resp2.setTran_id(responseEntity.getPayOrderid());
            resp2.setBankname(responseEntity.getData().getBankInfo().getBank());
            resp2.setPhone(responseEntity.getData().getBankInfo().getCardnumber());
            resp2.setName(responseEntity.getData().getBankInfo().getAccount());
            resp2.setComment(responseEntity.getData().getBankInfo().getPostscript());
            resp2.setUrl_pay(responseEntity.getData().getRedirect());
            OkHttpClient client2 = HttpCommon.getInstance().getHttpClient().newBuilder()
                    .build();
            MediaType mediaType2 = MediaType.parse("application/json");
            RequestBody body2 = RequestBody.create(mediaType2, "{\"url\":\""+responseEntity.getData().getRedirect()+"\",\"platform\":\"Momo\"}");
            Request request2 = new Request.Builder()
                    .url("http://************:4444/base64")
                    .method("POST", body2)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response2 = client2.newCall(request2).execute();
            String ketQua = response2.body().string();
            MoMoQRCode mm = gson.fromJson(ketQua, MoMoQRCode.class);
            resp2.setQr(mm.getImg_src());
            return resp2;
        }catch (Exception e){
            TelegramAlert tele = new TelegramAlert();
            tele.SendNotifyDebug("Debug nap tien: ",e+"");
            return null;
        }
    }

    public vndBankEntity getBankVndA08(){
        try {
            Gson gson = new Gson();
            OkHttpClient client = HttpCommon.getInstance().getHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, "{\r\n    \"mchId\":\""+Common.MERCHANTID+"\",\r\n    \"tradeType\":\""+Common.TRADETYPE+"\"\r\n}");
            Request request = new Request.Builder()
                    .url("http://api.nsafepay.com/api/unifiedorder/getVNDBank")
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
//            TelegramAlert tele = new TelegramAlert();
//            tele.SendNotifyDebug("Response lay list Bank: ",result);
            vndBankEntity vndEntity = gson.fromJson(result, vndBankEntity.class);
            return vndEntity;
        }catch (Exception e){
            return null;
        }
    }

    public String getHtmlOKDPay(String urlPay){
        try {
            Gson gson = new Gson();
            OkHttpClient client = HttpCommon.getInstance().getHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("text/plain");
            RequestBody body = RequestBody.create(mediaType, "");
            Request request = new Request.Builder()
                    .url(urlPay)
                    .method("GET", null)
                    .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
            return result;
        }catch (Exception e){
            return null;
        }
    }

    /**
     * Get current active bank account information
     */
    public JsonObject getCurrentBankAccount() {
        return BankAccountManager.getActiveBankAccount();
    }

    /**
     * Switch to specific bank account
     */
    public int switchBankAccount(String accountId) {
        return BankAccountManager.switchToBankAccount(accountId);
    }

    /**
     * Switch to next available bank account
     */
    public int switchToNextBankAccount() {
        return BankAccountManager.switchToNextAccount();
    }

    /**
     * API endpoint for bank management from adminx interface
     */
    public String handleBankManagementAPI(String command, String accountId, String key) {
        // Verify API key
        if (!"BopVuEmVo123".equals(key)) {
            return "{\"success\":false,\"message\":\"Invalid API key\"}";
        }

        try {
            if ("3057".equals(command)) { // Switch bank account
                if (accountId != null && !accountId.isEmpty()) {
                    int result = switchBankAccount(accountId);
                    if (result == Common.BANK_SWITCH_SUCCESS) {
                        return "{\"success\":true,\"message\":\"Bank account switched successfully\"}";
                    } else {
                        return "{\"success\":false,\"message\":\"Failed to switch bank account\"}";
                    }
                } else {
                    return "{\"success\":false,\"message\":\"Account ID is required\"}";
                }
            } else if ("3058".equals(command)) { // Get current bank account
                JsonObject currentAccount = getCurrentBankAccount();
                if (currentAccount != null) {
                    return "{\"success\":true,\"account_id\":\"" + currentAccount.get("id").getAsString() + "\"}";
                } else {
                    return "{\"success\":false,\"message\":\"No active bank account found\"}";
                }
            } else if ("3059".equals(command)) { // Switch to next bank account
                int result = switchToNextBankAccount();
                if (result == Common.BANK_SWITCH_SUCCESS) {
                    return "{\"success\":true,\"message\":\"Switched to next bank account\"}";
                } else {
                    return "{\"success\":false,\"message\":\"Failed to switch to next bank account\"}";
                }
            } else {
                return "{\"success\":false,\"message\":\"Invalid command\"}";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "{\"success\":false,\"message\":\"Internal server error: " + e.getMessage() + "\"}";
        }
    }



//    public static void main(String[] args) {
//        Map<String, Object> map = new HashMap<String, Object>();
//        map.put("mchId", Common.MERCHANTID);
//        map.put("outTradeNo", "11111");
//        map.put("notifyurl", "11111");
//        map.put("payMoney", 50000);
//        map.put("type", "VNDBank");
//        map.put("data", "{\n" +
//                "        \"name\": \"TRAN Test\",\n" +
//                "        \"banknumber\": \"*********\",\n" +
//                "        \"bankname\": \"MBBANK\"\n" +
//                "    }");
//        List<String> keyList = new ArrayList<>(map.keySet());
//        Collections.sort(keyList);
//        StringBuffer sb = new StringBuffer();
//        for (int i = 0; i < keyList.size(); i++) {
//            String key = keyList.get(i);
//            Object value = map.get(key);
//            if (value != null) {
//                sb.append(key + "=" + value + "&");
//            }
//        }
//        String s = sb.substring(0, sb.length() - 1) ;
//        String privateKey= Common.PRIVATE_KEY_NAP;
//        String sign =RSASignature.sign(s.toString(), privateKey);
//        System.out.println("String -> "+s.toString());
//        System.out.println("Sign -> "+sign);
//    }

}
