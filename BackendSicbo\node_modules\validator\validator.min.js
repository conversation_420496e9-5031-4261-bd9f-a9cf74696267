/*!
 * Copyright (c) 2018 <PERSON> <<EMAIL>>
 * 
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 * 
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.validator=e()}(this,function(){"use strict";function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function A(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=[],n=!0,o=!1,i=void 0;try{for(var a,l=t[Symbol.iterator]();!(n=(a=l.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==l.return||l.return()}finally{if(o)throw i}}return r}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function m(t){var e;if(!("string"==typeof t||t instanceof String))throw e=null===t?"null":"object"===(e=a(t))&&t.constructor&&t.constructor.hasOwnProperty("name")?t.constructor.name:"a ".concat(e),new TypeError("Expected string but received ".concat(e,"."))}function o(t){return m(t),t=Date.parse(t),isNaN(t)?null:new Date(t)}function r(t){return m(t),parseFloat(t)}function i(t){return"object"===a(t)&&null!==t?t="function"==typeof t.toString?t.toString():"[object Object]":(null==t||isNaN(t)&&!t.length)&&(t=""),String(t)}function $(t,e){var r=0<arguments.length&&void 0!==t?t:{},n=1<arguments.length?e:void 0;for(var o in n)void 0===r[o]&&(r[o]=n[o]);return r}function v(t,e){var r,n;m(t),n="object"===a(e)?(r=e.min||0,e.max):(r=e,arguments[2]);var o=encodeURI(t).split(/%..|./).length-1;return r<=o&&(void 0===n||o<=n)}var l={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1};function _(t,e){m(t),(e=$(e,l)).allow_trailing_dot&&"."===t[t.length-1]&&(t=t.substring(0,t.length-1));for(var r=t.split("."),n=0;n<r.length;n++)if(63<r[n].length)return!1;if(e.require_tld){var o=r.pop();if(!r.length||!/^([a-z\u00a1-\uffff]{2,}|xn[a-z0-9-]{2,})$/i.test(o))return!1;if(/[\s\u2002-\u200B\u202F\u205F\u3000\uFEFF\uDB40\uDC20]/.test(o))return!1}for(var i,a=0;a<r.length;a++){if(i=r[a],e.allow_underscores&&(i=i.replace(/_/g,"")),!/^[a-z\u00a1-\uffff0-9-]+$/i.test(i))return!1;if(/[\uff01-\uff5e]/.test(i))return!1;if("-"===i[0]||"-"===i[i.length-1])return!1}return!0}var s=/^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/,u=/^[0-9A-F]{1,4}$/i;function F(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";if(m(t),!(e=String(e)))return F(t,4)||F(t,6);if("4"===e)return!!s.test(t)&&t.split(".").sort(function(t,e){return t-e})[3]<=255;if("6"!==e)return!1;var r=t.split(":"),n=!1,o=F(r[r.length-1],4),i=o?7:8;if(r.length>i)return!1;if("::"===t)return!0;"::"===t.substr(0,2)?(r.shift(),r.shift(),n=!0):"::"===t.substr(t.length-2)&&(r.pop(),r.pop(),n=!0);for(var a=0;a<r.length;++a)if(""===r[a]&&0<a&&a<r.length-1){if(n)return!1;n=!0}else if(o&&a===r.length-1);else if(!u.test(r[a]))return!1;return n?1<=r.length:r.length===i}var S={allow_display_name:!1,require_display_name:!1,allow_utf8_local_part:!0,require_tld:!0},R=/^([^\x00-\x1F\x7F-\x9F\cX]+)<(.+)>$/i,E=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~]+$/i,x=/^[a-z\d]+$/,M=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f]))*$/i,N=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+$/i,C=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))*$/i;var d={protocols:["http","https","ftp"],require_tld:!0,require_protocol:!1,require_host:!0,require_valid_protocol:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_protocol_relative_urls:!1},f=/^\[([^\]]+)\](?::([0-9]+))?$/;function p(t,e){for(var r=0;r<e.length;r++){var n=e[r];if(t===n||(o=n,"[object RegExp]"===Object.prototype.toString.call(o)&&n.test(t)))return!0}var o;return!1}var n=/^([0-9a-fA-F][0-9a-fA-F]:){5}([0-9a-fA-F][0-9a-fA-F])$/,c=/^([0-9a-fA-F]){12}$/;var g=/^\d{1,2}$/;for(var t,h={"en-US":/^[A-Z]+$/i,"bg-BG":/^[А-Я]+$/i,"cs-CZ":/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]+$/i,"da-DK":/^[A-ZÆØÅ]+$/i,"de-DE":/^[A-ZÄÖÜß]+$/i,"el-GR":/^[Α-ω]+$/i,"es-ES":/^[A-ZÁÉÍÑÓÚÜ]+$/i,"fr-FR":/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]+$/i,"it-IT":/^[A-ZÀÉÈÌÎÓÒÙ]+$/i,"nb-NO":/^[A-ZÆØÅ]+$/i,"nl-NL":/^[A-ZÁÉËÏÓÖÜÚ]+$/i,"nn-NO":/^[A-ZÆØÅ]+$/i,"hu-HU":/^[A-ZÁÉÍÓÖŐÚÜŰ]+$/i,"pl-PL":/^[A-ZĄĆĘŚŁŃÓŻŹ]+$/i,"pt-PT":/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]+$/i,"ru-RU":/^[А-ЯЁ]+$/i,"sl-SI":/^[A-ZČĆĐŠŽ]+$/i,"sk-SK":/^[A-ZÁČĎÉÍŇÓŠŤÚÝŽĹŔĽÄÔ]+$/i,"sr-RS@latin":/^[A-ZČĆŽŠĐ]+$/i,"sr-RS":/^[А-ЯЂЈЉЊЋЏ]+$/i,"sv-SE":/^[A-ZÅÄÖ]+$/i,"tr-TR":/^[A-ZÇĞİıÖŞÜ]+$/i,"uk-UA":/^[А-ЩЬЮЯЄIЇҐі]+$/i,"ku-IQ":/^[ئابپتجچحخدرڕزژسشعغفڤقکگلڵمنوۆھەیێيطؤثآإأكضصةظذ]+$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$/},L={"en-US":/^[0-9A-Z]+$/i,"bg-BG":/^[0-9А-Я]+$/i,"cs-CZ":/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]+$/i,"da-DK":/^[0-9A-ZÆØÅ]+$/i,"de-DE":/^[0-9A-ZÄÖÜß]+$/i,"el-GR":/^[0-9Α-ω]+$/i,"es-ES":/^[0-9A-ZÁÉÍÑÓÚÜ]+$/i,"fr-FR":/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]+$/i,"it-IT":/^[0-9A-ZÀÉÈÌÎÓÒÙ]+$/i,"hu-HU":/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]+$/i,"nb-NO":/^[0-9A-ZÆØÅ]+$/i,"nl-NL":/^[0-9A-ZÁÉËÏÓÖÜÚ]+$/i,"nn-NO":/^[0-9A-ZÆØÅ]+$/i,"pl-PL":/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]+$/i,"pt-PT":/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]+$/i,"ru-RU":/^[0-9А-ЯЁ]+$/i,"sl-SI":/^[0-9A-ZČĆĐŠŽ]+$/i,"sk-SK":/^[0-9A-ZÁČĎÉÍŇÓŠŤÚÝŽĹŔĽÄÔ]+$/i,"sr-RS@latin":/^[0-9A-ZČĆŽŠĐ]+$/i,"sr-RS":/^[0-9А-ЯЂЈЉЊЋЏ]+$/i,"sv-SE":/^[0-9A-ZÅÄÖ]+$/i,"tr-TR":/^[0-9A-ZÇĞİıÖŞÜ]+$/i,"uk-UA":/^[0-9А-ЩЬЮЯЄIЇҐі]+$/i,"ku-IQ":/^[٠١٢٣٤٥٦٧٨٩0-9ئابپتجچحخدرڕزژسشعغفڤقکگلڵمنوۆھەیێيطؤثآإأكضصةظذ]+$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$/},w={"en-US":".",ar:"٫"},e=["AU","GB","HK","IN","NZ","ZA","ZM"],I=0;I<e.length;I++)t="en-".concat(e[I]),h[t]=h["en-US"],L[t]=L["en-US"],w[t]=w["en-US"];for(var Z,T=["AE","BH","DZ","EG","IQ","JO","KW","LB","LY","MA","QM","QA","SA","SD","SY","TN","YE"],y=0;y<T.length;y++)Z="ar-".concat(T[y]),h[Z]=h.ar,L[Z]=L.ar,w[Z]=w.ar;for(var B=["ar-EG","ar-LB","ar-LY"],b=["bg-BG","cs-CZ","da-DK","de-DE","el-GR","en-ZM","es-ES","fr-FR","it-IT","ku-IQ","hu-HU","nb-NO","nn-NO","nl-NL","pl-PL","pt-PT","ru-RU","sl-SI","sr-RS@latin","sr-RS","sv-SE","tr-TR","uk-UA"],U=0;U<B.length;U++)w[B[U]]=w["en-US"];for(var G=0;G<b.length;G++)w[b[G]]=",";h["pt-BR"]=h["pt-PT"],L["pt-BR"]=L["pt-PT"],w["pt-BR"]=w["pt-PT"],h["pl-Pl"]=h["pl-PL"],L["pl-Pl"]=L["pl-PL"],w["pl-Pl"]=w["pl-PL"];var O=Object.keys(h);var P=Object.keys(L),D=/^[+-]?([0-9]*[.])?[0-9]+$/,k=/^[0-9]+$/;var K=/^(?:[-+]?(?:0|[1-9][0-9]*))$/,H=/^[-+]?[0-9]+$/;function z(t,e){m(t);var r=(e=e||{}).hasOwnProperty("allow_leading_zeroes")&&!e.allow_leading_zeroes?K:H,n=!e.hasOwnProperty("min")||t>=e.min,o=!e.hasOwnProperty("max")||t<=e.max,i=!e.hasOwnProperty("lt")||t<e.lt,a=!e.hasOwnProperty("gt")||t>e.gt;return r.test(t)&&n&&o&&i&&a}var W=/^[\x00-\x7F]+$/;var Y=/[^\u0020-\u007E\uFF61-\uFF9F\uFFA0-\uFFDC\uFFE8-\uFFEE0-9a-zA-Z]/;var V=/[\u0020-\u007E\uFF61-\uFF9F\uFFA0-\uFFDC\uFFE8-\uFFEE0-9a-zA-Z]/;var j=/[^\x00-\x7F]/;var J=/[\uD800-\uDBFF][\uDC00-\uDFFF]/;function q(t,e){return t.some(function(t){return e===t})}var X=Object.keys(w);var Q={force_decimal:!1,decimal_digits:"1,",locale:"en-US"},tt=["","-","+"];var et=/^[0-9A-F]+$/i;function rt(t){return m(t),et.test(t)}var nt=/^#?([0-9A-F]{3}|[0-9A-F]{6})$/i;var ot=/^[A-Z]{2}[0-9A-Z]{3}\d{2}\d{5}$/;var it=/^[a-f0-9]{32}$/;var at={md5:32,md4:32,sha1:40,sha256:64,sha384:96,sha512:128,ripemd128:32,ripemd160:40,tiger128:32,tiger160:40,tiger192:48,crc32:8,crc32b:8};var lt=/^([A-Za-z0-9\-_~+\/]+[=]{0,2})\.([A-Za-z0-9\-_~+\/]+[=]{0,2})(?:\.([A-Za-z0-9\-_~+\/]+[=]{0,2}))?$/;var st={ignore_whitespace:!1};var ut={3:/^[0-9A-F]{8}-[0-9A-F]{4}-3[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,4:/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,5:/^[0-9A-F]{8}-[0-9A-F]{4}-5[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,all:/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i};var ct=/^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|(222[1-9]|22[3-9][0-9]|2[3-6][0-9]{2}|27[01][0-9]|2720)[0-9]{12}|6(?:011|5[0-9][0-9])[0-9]{12}|3[47][0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|(?:2131|1800|35\d{3})\d{11}|6[27][0-9]{14})$/;var dt={ES:function(t){m(t);var e={X:0,Y:1,Z:2},r=t.trim().toUpperCase();if(!/^[0-9X-Z][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/.test(r))return!1;var n=r.slice(0,-1).replace(/[X,Y,Z]/g,function(t){return e[t]});return r.endsWith(["T","R","W","A","G","M","Y","F","P","D","X","B","N","J","Z","S","Q","V","H","L","C","K","E"][n%23])},"he-IL":function(t){var e=t.trim();if(!/^\d{9}$/.test(e))return!1;for(var r,n=e,o=0,i=0;i<n.length;i++)o+=9<(r=Number(n[i])*(i%2+1))?r-9:r;return o%10==0},"zh-TW":function(t){var o={A:10,B:11,C:12,D:13,E:14,F:15,G:16,H:17,I:34,J:18,K:19,L:20,M:21,N:22,O:35,P:23,Q:24,R:25,S:26,T:27,U:28,V:29,W:32,X:30,Y:31,Z:33},e=t.trim().toUpperCase();return!!/^[A-Z][0-9]{9}$/.test(e)&&Array.from(e).reduce(function(t,e,r){if(0!==r)return 9===r?(10-t%10-Number(e))%10==0:t+Number(e)*(9-r);var n=o[e];return n%10*9+Math.floor(n/10)},0)}};var ft=/^[A-Z]{2}[0-9A-Z]{9}[0-9]$/;var pt=/^(?:[0-9]{9}X|[0-9]{10})$/,gt=/^(?:[0-9]{13})$/,ht=[1,3];var At={"ar-AE":/^((\+?971)|0)?5[024568]\d{7}$/,"ar-BH":/^(\+?973)?(3|6)\d{7}$/,"ar-DZ":/^(\+?213|0)(5|6|7)\d{8}$/,"ar-EG":/^((\+?20)|0)?1[0125]\d{8}$/,"ar-IQ":/^(\+?964|0)?7[0-9]\d{8}$/,"ar-JO":/^(\+?962|0)?7[789]\d{7}$/,"ar-KW":/^(\+?965)[569]\d{7}$/,"ar-SA":/^(!?(\+?966)|0)?5\d{8}$/,"ar-SY":/^(!?(\+?963)|0)?9\d{8}$/,"ar-TN":/^(\+?216)?[2459]\d{7}$/,"be-BY":/^(\+?375)?(24|25|29|33|44)\d{7}$/,"bg-BG":/^(\+?359|0)?8[789]\d{7}$/,"bn-BD":/^(\+?880|0)1[1356789][0-9]{8}$/,"cs-CZ":/^(\+?420)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/,"da-DK":/^(\+?45)?\s?\d{2}\s?\d{2}\s?\d{2}\s?\d{2}$/,"de-DE":/^(\+49)?0?1(5[0-25-9]\d|6([23]|0\d?)|7([0-57-9]|6\d))\d{7}$/,"el-GR":/^(\+?30|0)?(69\d{8})$/,"en-AU":/^(\+?61|0)4\d{8}$/,"en-GB":/^(\+?44|0)7\d{9}$/,"en-GH":/^(\+233|0)(20|50|24|54|27|57|26|56|23|28)\d{7}$/,"en-HK":/^(\+?852\-?)?[456789]\d{3}\-?\d{4}$/,"en-IE":/^(\+?353|0)8[356789]\d{7}$/,"en-IN":/^(\+?91|0)?[6789]\d{9}$/,"en-KE":/^(\+?254|0)(7|1)\d{8}$/,"en-MT":/^(\+?356|0)?(99|79|77|21|27|22|25)[0-9]{6}$/,"en-MU":/^(\+?230|0)?\d{8}$/,"en-NG":/^(\+?234|0)?[789]\d{9}$/,"en-NZ":/^(\+?64|0)[28]\d{7,9}$/,"en-PK":/^((\+92)|(0092))-{0,1}\d{3}-{0,1}\d{7}$|^\d{11}$|^\d{4}-\d{7}$/,"en-RW":/^(\+?250|0)?[7]\d{8}$/,"en-SG":/^(\+65)?[89]\d{7}$/,"en-TZ":/^(\+?255|0)?[67]\d{8}$/,"en-UG":/^(\+?256|0)?[7]\d{8}$/,"en-US":/^((\+1|1)?( |-)?)?(\([2-9][0-9]{2}\)|[2-9][0-9]{2})( |-)?([2-9][0-9]{2}( |-)?[0-9]{4})$/,"en-ZA":/^(\+?27|0)\d{9}$/,"en-ZM":/^(\+?26)?09[567]\d{7}$/,"es-CL":/^(\+?56|0)[2-9]\d{1}\d{7}$/,"es-ES":/^(\+?34)?(6\d{1}|7[1234])\d{7}$/,"es-MX":/^(\+?52)?(1|01)?\d{10,11}$/,"es-PY":/^(\+?595|0)9[9876]\d{7}$/,"es-UY":/^(\+598|0)9[1-9][\d]{6}$/,"et-EE":/^(\+?372)?\s?(5|8[1-4])\s?([0-9]\s?){6,7}$/,"fa-IR":/^(\+?98[\-\s]?|0)9[0-39]\d[\-\s]?\d{3}[\-\s]?\d{4}$/,"fi-FI":/^(\+?358|0)\s?(4(0|1|2|4|5|6)?|50)\s?(\d\s?){4,8}\d$/,"fj-FJ":/^(\+?679)?\s?\d{3}\s?\d{4}$/,"fo-FO":/^(\+?298)?\s?\d{2}\s?\d{2}\s?\d{2}$/,"fr-FR":/^(\+?33|0)[67]\d{8}$/,"he-IL":/^(\+972|0)([23489]|5[012345689]|77)[1-9]\d{6}$/,"hu-HU":/^(\+?36)(20|30|70)\d{7}$/,"id-ID":/^(\+?62|0)8(1[123456789]|2[1238]|3[1238]|5[12356789]|7[78]|9[56789]|8[123456789])([\s?|\d]{5,11})$/,"it-IT":/^(\+?39)?\s?3\d{2} ?\d{6,7}$/,"ja-JP":/^(\+?81|0)[789]0[ \-]?[1-9]\d{2}[ \-]?\d{5}$/,"kk-KZ":/^(\+?7|8)?7\d{9}$/,"kl-GL":/^(\+?299)?\s?\d{2}\s?\d{2}\s?\d{2}$/,"ko-KR":/^((\+?82)[ \-]?)?0?1([0|1|6|7|8|9]{1})[ \-]?\d{3,4}[ \-]?\d{4}$/,"lt-LT":/^(\+370|8)\d{8}$/,"ms-MY":/^(\+?6?01){1}(([0145]{1}(\-|\s)?\d{7,8})|([236789]{1}(\s|\-)?\d{7}))$/,"nb-NO":/^(\+?47)?[49]\d{7}$/,"nl-BE":/^(\+?32|0)4?\d{8}$/,"nl-NL":/^(\+?31|0)6?\d{8}$/,"nn-NO":/^(\+?47)?[49]\d{7}$/,"pl-PL":/^(\+?48)? ?[5-8]\d ?\d{3} ?\d{2} ?\d{2}$/,"pt-BR":/(?=^(\+?5{2}\-?|0)[1-9]{2}\-?\d{4}\-?\d{4}$)(^(\+?5{2}\-?|0)[1-9]{2}\-?[6-9]{1}\d{3}\-?\d{4}$)|(^(\+?5{2}\-?|0)[1-9]{2}\-?9[6-9]{1}\d{3}\-?\d{4}$)/,"pt-PT":/^(\+?351)?9[1236]\d{7}$/,"ro-RO":/^(\+?4?0)\s?7\d{2}(\/|\s|\.|\-)?\d{3}(\s|\.|\-)?\d{3}$/,"ru-RU":/^(\+?7|8)?9\d{9}$/,"sl-SI":/^(\+386\s?|0)(\d{1}\s?\d{3}\s?\d{2}\s?\d{2}|\d{2}\s?\d{3}\s?\d{3})$/,"sk-SK":/^(\+?421)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/,"sr-RS":/^(\+3816|06)[- \d]{5,9}$/,"sv-SE":/^(\+?46|0)[\s\-]?7[\s\-]?[02369]([\s\-]?\d){7}$/,"th-TH":/^(\+66|66|0)\d{9}$/,"tr-TR":/^(\+?90|0)?5\d{9}$/,"uk-UA":/^(\+?38|8)?0\d{9}$/,"vi-VN":/^(\+?84|0)((3([2-9]))|(5([2689]))|(7([0|6-9]))|(8([1-6|89]))|(9([0-9])))([0-9]{7})$/,"zh-CN":/^((\+|00)86)?1([358][0-9]|4[579]|6[67]|7[0135678]|9[189])[0-9]{8}$/,"zh-TW":/^(\+?886\-?|0)?9\d{8}$/};At["en-CA"]=At["en-US"],At["fr-BE"]=At["nl-BE"],At["zh-HK"]=At["en-HK"];var mt=Object.keys(At);var $t={symbol:"$",require_symbol:!1,allow_space_after_symbol:!1,symbol_after_digits:!1,allow_negatives:!0,parens_for_negatives:!1,negative_sign_before_digits:!1,negative_sign_after_digits:!1,allow_negative_sign_placeholder:!1,thousands_separator:",",decimal_separator:".",allow_decimal:!0,require_decimal:!1,digits_after_decimal:[2],allow_space_after_digits:!1};var vt=/^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-3])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/;var _t=/([01][0-9]|2[0-3])/,Ft=/[0-5][0-9]/,St=new RegExp("[-+]".concat(_t.source,":").concat(Ft.source)),Rt=new RegExp("([zZ]|".concat(St.source,")")),Et=new RegExp("".concat(_t.source,":").concat(Ft.source,":").concat(/([0-5][0-9]|60)/.source).concat(/(\.[0-9]+)?/.source)),xt=new RegExp("".concat(/[0-9]{4}/.source,"-").concat(/(0[1-9]|1[0-2])/.source,"-").concat(/([12]\d|0[1-9]|3[01])/.source)),Mt=new RegExp("".concat(Et.source).concat(Rt.source)),Nt=new RegExp("".concat(xt.source,"[ tT]").concat(Mt.source));var Ct=["AD","AE","AF","AG","AI","AL","AM","AO","AQ","AR","AS","AT","AU","AW","AX","AZ","BA","BB","BD","BE","BF","BG","BH","BI","BJ","BL","BM","BN","BO","BQ","BR","BS","BT","BV","BW","BY","BZ","CA","CC","CD","CF","CG","CH","CI","CK","CL","CM","CN","CO","CR","CU","CV","CW","CX","CY","CZ","DE","DJ","DK","DM","DO","DZ","EC","EE","EG","EH","ER","ES","ET","FI","FJ","FK","FM","FO","FR","GA","GB","GD","GE","GF","GG","GH","GI","GL","GM","GN","GP","GQ","GR","GS","GT","GU","GW","GY","HK","HM","HN","HR","HT","HU","ID","IE","IL","IM","IN","IO","IQ","IR","IS","IT","JE","JM","JO","JP","KE","KG","KH","KI","KM","KN","KP","KR","KW","KY","KZ","LA","LB","LC","LI","LK","LR","LS","LT","LU","LV","LY","MA","MC","MD","ME","MF","MG","MH","MK","ML","MM","MN","MO","MP","MQ","MR","MS","MT","MU","MV","MW","MX","MY","MZ","NA","NC","NE","NF","NG","NI","NL","NO","NP","NR","NU","NZ","OM","PA","PE","PF","PG","PH","PK","PL","PM","PN","PR","PS","PT","PW","PY","QA","RE","RO","RS","RU","RW","SA","SB","SC","SD","SE","SG","SH","SI","SJ","SK","SL","SM","SN","SO","SR","SS","ST","SV","SX","SY","SZ","TC","TD","TF","TG","TH","TJ","TK","TL","TM","TN","TO","TR","TT","TV","TW","TZ","UA","UG","UM","US","UY","UZ","VA","VC","VE","VG","VI","VN","VU","WF","WS","YE","YT","ZA","ZM","ZW"];var Lt=["AFG","ALA","ALB","DZA","ASM","AND","AGO","AIA","ATA","ATG","ARG","ARM","ABW","AUS","AUT","AZE","BHS","BHR","BGD","BRB","BLR","BEL","BLZ","BEN","BMU","BTN","BOL","BES","BIH","BWA","BVT","BRA","IOT","BRN","BGR","BFA","BDI","KHM","CMR","CAN","CPV","CYM","CAF","TCD","CHL","CHN","CXR","CCK","COL","COM","COG","COD","COK","CRI","CIV","HRV","CUB","CUW","CYP","CZE","DNK","DJI","DMA","DOM","ECU","EGY","SLV","GNQ","ERI","EST","ETH","FLK","FRO","FJI","FIN","FRA","GUF","PYF","ATF","GAB","GMB","GEO","DEU","GHA","GIB","GRC","GRL","GRD","GLP","GUM","GTM","GGY","GIN","GNB","GUY","HTI","HMD","VAT","HND","HKG","HUN","ISL","IND","IDN","IRN","IRQ","IRL","IMN","ISR","ITA","JAM","JPN","JEY","JOR","KAZ","KEN","KIR","PRK","KOR","KWT","KGZ","LAO","LVA","LBN","LSO","LBR","LBY","LIE","LTU","LUX","MAC","MKD","MDG","MWI","MYS","MDV","MLI","MLT","MHL","MTQ","MRT","MUS","MYT","MEX","FSM","MDA","MCO","MNG","MNE","MSR","MAR","MOZ","MMR","NAM","NRU","NPL","NLD","NCL","NZL","NIC","NER","NGA","NIU","NFK","MNP","NOR","OMN","PAK","PLW","PSE","PAN","PNG","PRY","PER","PHL","PCN","POL","PRT","PRI","QAT","REU","ROU","RUS","RWA","BLM","SHN","KNA","LCA","MAF","SPM","VCT","WSM","SMR","STP","SAU","SEN","SRB","SYC","SLE","SGP","SXM","SVK","SVN","SLB","SOM","ZAF","SGS","SSD","ESP","LKA","SDN","SUR","SJM","SWZ","SWE","CHE","SYR","TWN","TJK","TZA","THA","TLS","TGO","TKL","TON","TTO","TUN","TUR","TKM","TCA","TUV","UGA","UKR","ARE","GBR","USA","UMI","URY","UZB","VUT","VEN","VNM","VGB","VIR","WLF","ESH","YEM","ZMB","ZWE"];var wt=/^[A-Z2-7]+=*$/;var It=/[^A-Z0-9+\/=]/i;var Zt=/^[a-z]+\/[a-z0-9\-\+]+$/i,Tt=/^[a-z\-]+=[a-z0-9\-]+$/i,yt=/^[a-z0-9!\$&'\(\)\*\+,;=\-\._~:@\/\?%\s]*$/i;var Bt=/^magnet:\?xt=urn:[a-z0-9]+:[a-z0-9]{32,40}&dn=.+&tr=.+$/i;var bt=/^(application|audio|font|image|message|model|multipart|text|video)\/[a-zA-Z0-9\.\-\+]{1,100}$/i,Ut=/^text\/[a-zA-Z0-9\.\-\+]{1,100};\s?charset=("[a-zA-Z0-9\.\-\+\s]{0,70}"|[a-zA-Z0-9\.\-\+]{0,70})(\s?\([a-zA-Z0-9\.\-\+\s]{1,20}\))?$/i,Gt=/^multipart\/[a-zA-Z0-9\.\-\+]{1,100}(;\s?(boundary|charset)=("[a-zA-Z0-9\.\-\+\s]{0,70}"|[a-zA-Z0-9\.\-\+]{0,70})(\s?\([a-zA-Z0-9\.\-\+\s]{1,20}\))?){0,2}$/i;var Ot=/^\(?[+-]?(90(\.0+)?|[1-8]?\d(\.\d+)?)$/,Pt=/^\s?[+-]?(180(\.0+)?|1[0-7]\d(\.\d+)?|\d{1,2}(\.\d+)?)\)?$/,Dt=/^\d{4}$/,kt=/^\d{5}$/,Kt=/^\d{6}$/,Ht={AD:/^AD\d{3}$/,AT:Dt,AU:Dt,BE:Dt,BG:Dt,BR:/^\d{5}-\d{3}$/,CA:/^[ABCEGHJKLMNPRSTVXY]\d[ABCEGHJ-NPRSTV-Z][\s\-]?\d[ABCEGHJ-NPRSTV-Z]\d$/i,CH:Dt,CZ:/^\d{3}\s?\d{2}$/,DE:kt,DK:Dt,DZ:kt,EE:kt,ES:kt,FI:kt,FR:/^\d{2}\s?\d{3}$/,GB:/^(gir\s?0aa|[a-z]{1,2}\d[\da-z]?\s?(\d[a-z]{2})?)$/i,GR:/^\d{3}\s?\d{2}$/,HR:/^([1-5]\d{4}$)/,HU:Dt,ID:kt,IL:kt,IN:Kt,IS:/^\d{3}$/,IT:kt,JP:/^\d{3}\-\d{4}$/,KE:kt,LI:/^(948[5-9]|949[0-7])$/,LT:/^LT\-\d{5}$/,LU:Dt,LV:/^LV\-\d{4}$/,MX:kt,MT:/^[A-Za-z]{3}\s{0,1}\d{4}$/,NL:/^\d{4}\s?[a-z]{2}$/i,NO:Dt,NZ:Dt,PL:/^\d{2}\-\d{3}$/,PR:/^00[679]\d{2}([ -]\d{4})?$/,PT:/^\d{4}\-\d{3}?$/,RO:Kt,RU:Kt,SA:kt,SE:/^\d{3}\s?\d{2}$/,SI:Dt,SK:/^\d{3}\s?\d{2}$/,TN:Dt,TW:/^\d{3}(\d{2})?$/,UA:kt,US:/^\d{5}(-\d{4})?$/,ZA:Dt,ZM:kt},zt=Object.keys(Ht);function Wt(t,e){m(t);var r=e?new RegExp("^[".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"]+"),"g"):/^\s+/g;return t.replace(r,"")}function Yt(t,e){m(t);var r=e?new RegExp("[".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"]+$"),"g"):/\s+$/g;return t.replace(r,"")}function Vt(t,e){return m(t),t.replace(new RegExp("[".concat(e,"]+"),"g"),"")}var jt={all_lowercase:!0,gmail_lowercase:!0,gmail_remove_dots:!0,gmail_remove_subaddress:!0,gmail_convert_googlemaildotcom:!0,outlookdotcom_lowercase:!0,outlookdotcom_remove_subaddress:!0,yahoo_lowercase:!0,yahoo_remove_subaddress:!0,yandex_lowercase:!0,icloud_lowercase:!0,icloud_remove_subaddress:!0},Jt=["icloud.com","me.com"],qt=["hotmail.at","hotmail.be","hotmail.ca","hotmail.cl","hotmail.co.il","hotmail.co.nz","hotmail.co.th","hotmail.co.uk","hotmail.com","hotmail.com.ar","hotmail.com.au","hotmail.com.br","hotmail.com.gr","hotmail.com.mx","hotmail.com.pe","hotmail.com.tr","hotmail.com.vn","hotmail.cz","hotmail.de","hotmail.dk","hotmail.es","hotmail.fr","hotmail.hu","hotmail.id","hotmail.ie","hotmail.in","hotmail.it","hotmail.jp","hotmail.kr","hotmail.lv","hotmail.my","hotmail.ph","hotmail.pt","hotmail.sa","hotmail.sg","hotmail.sk","live.be","live.co.uk","live.com","live.com.ar","live.com.mx","live.de","live.es","live.eu","live.fr","live.it","live.nl","msn.com","outlook.at","outlook.be","outlook.cl","outlook.co.il","outlook.co.nz","outlook.co.th","outlook.com","outlook.com.ar","outlook.com.au","outlook.com.br","outlook.com.gr","outlook.com.pe","outlook.com.tr","outlook.com.vn","outlook.cz","outlook.de","outlook.dk","outlook.es","outlook.fr","outlook.hu","outlook.id","outlook.ie","outlook.in","outlook.it","outlook.jp","outlook.kr","outlook.lv","outlook.my","outlook.ph","outlook.pt","outlook.sa","outlook.sg","outlook.sk","passport.com"],Xt=["rocketmail.com","yahoo.ca","yahoo.co.uk","yahoo.com","yahoo.de","yahoo.fr","yahoo.in","yahoo.it","ymail.com"],Qt=["yandex.ru","yandex.ua","yandex.kz","yandex.com","yandex.by","ya.ru"];function te(t){return 1<t.length?t:""}return{version:"11.1.0",toDate:o,toFloat:r,toInt:function(t,e){return m(t),parseInt(t,e||10)},toBoolean:function(t,e){return m(t),e?"1"===t||"true"===t:"0"!==t&&"false"!==t&&""!==t},equals:function(t,e){return m(t),t===e},contains:function(t,e){return m(t),0<=t.indexOf(i(e))},matches:function(t,e,r){return m(t),"[object RegExp]"!==Object.prototype.toString.call(e)&&(e=new RegExp(e,r)),e.test(t)},isEmail:function(t,e){if(m(t),(e=$(e,S)).require_display_name||e.allow_display_name){var r=t.match(R);if(r){var n,o=A(r,3);if(n=o[1],t=o[2],n.endsWith(" ")&&(n=n.substr(0,n.length-1)),!function(t){var e=t.match(/^"(.+)"$/i),r=e?e[1]:t;if(!r.trim())return!1;if(/[\.";<>]/.test(r)){if(!e)return!1;if(!(r.split('"').length===r.split('\\"').length))return!1}return!0}(n))return!1}else if(e.require_display_name)return!1}if(!e.ignore_max_length&&254<t.length)return!1;var i=t.split("@"),a=i.pop(),l=i.join("@"),s=a.toLowerCase();if(e.domain_specific_validation&&("gmail.com"===s||"googlemail.com"===s)){var u=(l=l.toLowerCase()).split("+")[0];if(!v(u.replace(".",""),{min:6,max:30}))return!1;for(var c=u.split("."),d=0;d<c.length;d++)if(!x.test(c[d]))return!1}if(!v(l,{max:64})||!v(a,{max:254}))return!1;if(!_(a,{require_tld:e.require_tld})){if(!e.allow_ip_domain)return!1;if(!F(a)){if(!a.startsWith("[")||!a.endsWith("]"))return!1;var f=a.substr(1,a.length-2);if(0===f.length||!F(f))return!1}}if('"'===l[0])return l=l.slice(1,l.length-1),e.allow_utf8_local_part?C.test(l):M.test(l);for(var p=e.allow_utf8_local_part?N:E,g=l.split("."),h=0;h<g.length;h++)if(!p.test(g[h]))return!1;return!0},isURL:function(t,e){if(m(t),!t||2083<=t.length||/[\s<>]/.test(t))return!1;if(0===t.indexOf("mailto:"))return!1;var r,n,o,i,a,l,s,u;if(e=$(e,d),1<(s=(t=(s=(t=(s=t.split("#")).shift()).split("?")).shift()).split("://")).length){if(r=s.shift().toLowerCase(),e.require_valid_protocol&&-1===e.protocols.indexOf(r))return!1}else{if(e.require_protocol)return!1;if("//"===t.substr(0,2)){if(!e.allow_protocol_relative_urls)return!1;s[0]=t.substr(2)}}if(""===(t=s.join("://")))return!1;if(""===(t=(s=t.split("/")).shift())&&!e.require_host)return!0;if(1<(s=t.split("@")).length){if(e.disallow_auth)return!1;if(0<=(n=s.shift()).indexOf(":")&&2<n.split(":").length)return!1}u=l=null;var c=(i=s.join("@")).match(f);return c?(o="",u=c[1],l=c[2]||null):(o=(s=i.split(":")).shift(),s.length&&(l=s.join(":"))),!(null!==l&&(a=parseInt(l,10),!/^[0-9]+$/.test(l)||a<=0||65535<a))&&(!!(F(o)||_(o,e)||u&&F(u,6))&&(o=o||u,!(e.host_whitelist&&!p(o,e.host_whitelist))&&(!e.host_blacklist||!p(o,e.host_blacklist))))},isMACAddress:function(t,e){return m(t),e&&e.no_colons?c.test(t):n.test(t)},isIP:F,isIPRange:function(t){m(t);var e=t.split("/");return 2===e.length&&(!!g.test(e[1])&&(!(1<e[1].length&&e[1].startsWith("0"))&&(F(e[0],4)&&e[1]<=32&&0<=e[1])))},isFQDN:_,isBoolean:function(t){return m(t),0<=["true","false","1","0"].indexOf(t)},isAlpha:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"en-US";if(m(t),e in h)return h[e].test(t);throw new Error("Invalid locale '".concat(e,"'"))},isAlphaLocales:O,isAlphanumeric:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"en-US";if(m(t),e in L)return L[e].test(t);throw new Error("Invalid locale '".concat(e,"'"))},isAlphanumericLocales:P,isNumeric:function(t,e){return m(t),e&&e.no_symbols?k.test(t):D.test(t)},isPort:function(t){return z(t,{min:0,max:65535})},isLowercase:function(t){return m(t),t===t.toLowerCase()},isUppercase:function(t){return m(t),t===t.toUpperCase()},isAscii:function(t){return m(t),W.test(t)},isFullWidth:function(t){return m(t),Y.test(t)},isHalfWidth:function(t){return m(t),V.test(t)},isVariableWidth:function(t){return m(t),Y.test(t)&&V.test(t)},isMultibyte:function(t){return m(t),j.test(t)},isSurrogatePair:function(t){return m(t),J.test(t)},isInt:z,isFloat:function(t,e){m(t),e=e||{};var r=new RegExp("^(?:[-+])?(?:[0-9]+)?(?:\\".concat(e.locale?w[e.locale]:".","[0-9]*)?(?:[eE][\\+\\-]?(?:[0-9]+))?$"));if(""===t||"."===t||"-"===t||"+"===t)return!1;var n=parseFloat(t.replace(",","."));return r.test(t)&&(!e.hasOwnProperty("min")||n>=e.min)&&(!e.hasOwnProperty("max")||n<=e.max)&&(!e.hasOwnProperty("lt")||n<e.lt)&&(!e.hasOwnProperty("gt")||n>e.gt)},isFloatLocales:X,isDecimal:function(t,e){if(m(t),(e=$(e,Q)).locale in w)return!q(tt,t.replace(/ /g,""))&&function(t){return new RegExp("^[-+]?([0-9]+)?(\\".concat(w[t.locale],"[0-9]{").concat(t.decimal_digits,"})").concat(t.force_decimal?"":"?","$"))}(e).test(t);throw new Error("Invalid locale '".concat(e.locale,"'"))},isHexadecimal:rt,isDivisibleBy:function(t,e){return m(t),r(t)%parseInt(e,10)==0},isHexColor:function(t){return m(t),nt.test(t)},isISRC:function(t){return m(t),ot.test(t)},isMD5:function(t){return m(t),it.test(t)},isHash:function(t,e){return m(t),new RegExp("^[a-f0-9]{".concat(at[e],"}$")).test(t)},isJWT:function(t){return m(t),lt.test(t)},isJSON:function(t){m(t);try{var e=JSON.parse(t);return!!e&&"object"===a(e)}catch(t){}return!1},isEmpty:function(t,e){return m(t),0===((e=$(e,st)).ignore_whitespace?t.trim().length:t.length)},isLength:function(t,e){var r,n;m(t),n="object"===a(e)?(r=e.min||0,e.max):(r=e,arguments[2]);var o=t.match(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g)||[],i=t.length-o.length;return r<=i&&(void 0===n||i<=n)},isByteLength:v,isUUID:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"all";m(t);var r=ut[e];return r&&r.test(t)},isMongoId:function(t){return m(t),rt(t)&&24===t.length},isAfter:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:String(new Date);m(t);var r=o(e),n=o(t);return!!(n&&r&&r<n)},isBefore:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:String(new Date);m(t);var r=o(e),n=o(t);return!!(n&&r&&n<r)},isIn:function(t,e){var r;if(m(t),"[object Array]"!==Object.prototype.toString.call(e))return"object"===a(e)?e.hasOwnProperty(t):!(!e||"function"!=typeof e.indexOf)&&0<=e.indexOf(t);var n=[];for(r in e)!{}.hasOwnProperty.call(e,r)||(n[r]=i(e[r]));return 0<=n.indexOf(t)},isCreditCard:function(t){m(t);var e=t.replace(/[- ]+/g,"");if(!ct.test(e))return!1;for(var r,n,o,i=0,a=e.length-1;0<=a;a--)r=e.substring(a,a+1),n=parseInt(r,10),i+=o&&10<=(n*=2)?n%10+1:n,o=!o;return!(i%10!=0||!e)},isIdentityCard:function(t,e){if(m(t),e in dt)return dt[e](t);if("any"!==e)throw new Error("Invalid locale '".concat(e,"'"));for(var r in dt){if(dt.hasOwnProperty(r))if((0,dt[r])(t))return!0}return!1},isISIN:function(t){if(m(t),!ft.test(t))return!1;for(var e,r,n=t.replace(/[A-Z]/g,function(t){return parseInt(t,36)}),o=0,i=!0,a=n.length-2;0<=a;a--)e=n.substring(a,a+1),r=parseInt(e,10),o+=i&&10<=(r*=2)?r+1:r,i=!i;return parseInt(t.substr(t.length-1),10)===(1e4-o)%10},isISBN:function t(e){var r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"";if(m(e),!(r=String(r)))return t(e,10)||t(e,13);var n,o=e.replace(/[\s-]+/g,""),i=0;if("10"===r){if(!pt.test(o))return!1;for(n=0;n<9;n++)i+=(n+1)*o.charAt(n);if("X"===o.charAt(9)?i+=100:i+=10*o.charAt(9),i%11==0)return!!o}else if("13"===r){if(!gt.test(o))return!1;for(n=0;n<12;n++)i+=ht[n%2]*o.charAt(n);if(o.charAt(12)-(10-i%10)%10==0)return!!o}return!1},isISSN:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};m(t);var r="^\\d{4}-?\\d{3}[\\dX]$";if(r=e.require_hyphen?r.replace("?",""):r,!(r=e.case_sensitive?new RegExp(r):new RegExp(r,"i")).test(t))return!1;for(var n=t.replace("-","").toUpperCase(),o=0,i=0;i<n.length;i++){var a=n[i];o+=("X"===a?10:+a)*(8-i)}return o%11==0},isMobilePhone:function(e,t,r){if(m(e),r&&r.strictMode&&!e.startsWith("+"))return!1;if(Array.isArray(t))return t.some(function(t){if(At.hasOwnProperty(t)&&At[t].test(e))return!0;return!1});if(t in At)return At[t].test(e);if(t&&"any"!==t)throw new Error("Invalid locale '".concat(t,"'"));for(var n in At){if(At.hasOwnProperty(n))if(At[n].test(e))return!0}return!1},isMobilePhoneLocales:mt,isPostalCode:function(t,e){if(m(t),e in Ht)return Ht[e].test(t);if("any"!==e)throw new Error("Invalid locale '".concat(e,"'"));for(var r in Ht){if(Ht.hasOwnProperty(r))if(Ht[r].test(t))return!0}return!1},isPostalCodeLocales:zt,isCurrency:function(t,e){return m(t),function(t){var r="\\d{".concat(t.digits_after_decimal[0],"}");t.digits_after_decimal.forEach(function(t,e){0!==e&&(r="".concat(r,"|\\d{").concat(t,"}"))});var e="(\\".concat(t.symbol.replace(/\./g,"\\."),")").concat(t.require_symbol?"":"?"),n="[1-9]\\d{0,2}(\\".concat(t.thousands_separator,"\\d{3})*"),o="(".concat(["0","[1-9]\\d*",n].join("|"),")?"),i="(\\".concat(t.decimal_separator,"(").concat(r,"))").concat(t.require_decimal?"":"?"),a=o+(t.allow_decimal||t.require_decimal?i:"");return t.allow_negatives&&!t.parens_for_negatives&&(t.negative_sign_after_digits?a+="-?":t.negative_sign_before_digits&&(a="-?"+a)),t.allow_negative_sign_placeholder?a="( (?!\\-))?".concat(a):t.allow_space_after_symbol?a=" ?".concat(a):t.allow_space_after_digits&&(a+="( (?!$))?"),t.symbol_after_digits?a+=e:a=e+a,t.allow_negatives&&(t.parens_for_negatives?a="(\\(".concat(a,"\\)|").concat(a,")"):t.negative_sign_before_digits||t.negative_sign_after_digits||(a="-?"+a)),new RegExp("^(?!-? )(?=.*\\d)".concat(a,"$"))}(e=$(e,$t)).test(t)},isISO8601:function(t,e){m(t);var r=vt.test(t);return e&&r&&e.strict?function(t){var e=t.match(/^(\d{4})-?(\d{3})([ T]{1}\.*|$)/);if(e){var r=Number(e[1]),n=Number(e[2]);return r%4==0&&r%100!=0||r%400==0?n<=366:n<=365}var o=t.match(/(\d{4})-?(\d{0,2})-?(\d*)/).map(Number),i=o[1],a=o[2],l=o[3],s=a?"0".concat(a).slice(-2):a,u=l?"0".concat(l).slice(-2):l,c=new Date("".concat(i,"-").concat(s||"01","-").concat(u||"01"));return!a||!l||c.getUTCFullYear()===i&&c.getUTCMonth()+1===a&&c.getUTCDate()===l}(t):r},isRFC3339:function(t){return m(t),Nt.test(t)},isISO31661Alpha2:function(t){return m(t),q(Ct,t.toUpperCase())},isISO31661Alpha3:function(t){return m(t),q(Lt,t.toUpperCase())},isBase32:function(t){m(t);var e=t.length;return!!(0<e&&e%8==0&&wt.test(t))},isBase64:function(t){m(t);var e=t.length;if(!e||e%4!=0||It.test(t))return!1;var r=t.indexOf("=");return-1===r||r===e-1||r===e-2&&"="===t[e-1]},isDataURI:function(t){m(t);var e=t.split(",");if(e.length<2)return!1;var r=e.shift().trim().split(";"),n=r.shift();if("data:"!==n.substr(0,5))return!1;var o=n.substr(5);if(""!==o&&!Zt.test(o))return!1;for(var i=0;i<r.length;i++)if(i===r.length-1&&"base64"===r[i].toLowerCase());else if(!Tt.test(r[i]))return!1;for(var a=0;a<e.length;a++)if(!yt.test(e[a]))return!1;return!0},isMagnetURI:function(t){return m(t),Bt.test(t.trim())},isMimeType:function(t){return m(t),bt.test(t)||Ut.test(t)||Gt.test(t)},isLatLong:function(t){if(m(t),!t.includes(","))return!1;var e=t.split(",");return Ot.test(e[0])&&Pt.test(e[1])},ltrim:Wt,rtrim:Yt,trim:function(t,e){return Yt(Wt(t,e),e)},escape:function(t){return m(t),t.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\//g,"&#x2F;").replace(/\\/g,"&#x5C;").replace(/`/g,"&#96;")},unescape:function(t){return m(t),t.replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#x27;/g,"'").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&#x2F;/g,"/").replace(/&#x5C;/g,"\\").replace(/&#96;/g,"`")},stripLow:function(t,e){return m(t),Vt(t,e?"\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F":"\\x00-\\x1F\\x7F")},whitelist:function(t,e){return m(t),t.replace(new RegExp("[^".concat(e,"]+"),"g"),"")},blacklist:Vt,isWhitelisted:function(t,e){m(t);for(var r=t.length-1;0<=r;r--)if(-1===e.indexOf(t[r]))return!1;return!0},normalizeEmail:function(t,e){e=$(e,jt);var r=t.split("@"),n=r.pop(),o=[r.join("@"),n];if(o[1]=o[1].toLowerCase(),"gmail.com"===o[1]||"googlemail.com"===o[1]){if(e.gmail_remove_subaddress&&(o[0]=o[0].split("+")[0]),e.gmail_remove_dots&&(o[0]=o[0].replace(/\.+/g,te)),!o[0].length)return!1;(e.all_lowercase||e.gmail_lowercase)&&(o[0]=o[0].toLowerCase()),o[1]=e.gmail_convert_googlemaildotcom?"gmail.com":o[1]}else if(0<=Jt.indexOf(o[1])){if(e.icloud_remove_subaddress&&(o[0]=o[0].split("+")[0]),!o[0].length)return!1;(e.all_lowercase||e.icloud_lowercase)&&(o[0]=o[0].toLowerCase())}else if(0<=qt.indexOf(o[1])){if(e.outlookdotcom_remove_subaddress&&(o[0]=o[0].split("+")[0]),!o[0].length)return!1;(e.all_lowercase||e.outlookdotcom_lowercase)&&(o[0]=o[0].toLowerCase())}else if(0<=Xt.indexOf(o[1])){if(e.yahoo_remove_subaddress){var i=o[0].split("-");o[0]=1<i.length?i.slice(0,-1).join("-"):i[0]}if(!o[0].length)return!1;(e.all_lowercase||e.yahoo_lowercase)&&(o[0]=o[0].toLowerCase())}else 0<=Qt.indexOf(o[1])?((e.all_lowercase||e.yandex_lowercase)&&(o[0]=o[0].toLowerCase()),o[1]="yandex.ru"):e.all_lowercase&&(o[0]=o[0].toLowerCase());return o.join("@")},toString:toString}});