Contributing
============

Before making a PR, please open an issue so we can discuss the feature/change.

## Install

Clone the repository:

```shell
git clone https://github.com/skaterdav85/validatorjs.git && cd validatorjs
```

Install dependencies:

```shell
npm install
```

Now you're ready to develop.

## Workflow

Source files are located in `src/`. Tests are located in `spec/`, and can be run with:

```shell
npm test
```

Please use spaces instead of tabs.

## Language Support

If you'd like to add support for another language, create the appropriate module in `src/lang/`.
