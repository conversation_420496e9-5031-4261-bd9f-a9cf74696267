<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      /
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">85.85% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>734/855</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">68.61% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>330/481</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">92.59% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>175/189</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">87.56% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>711/812</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="dist/"><a href="dist/index.html">dist/</a></td>
	<td data-value="85.85" class="pic high"><div class="chart"><div class="cover-fill" style="width: 85%;"></div><div class="cover-empty" style="width:15%;"></div></div></td>
	<td data-value="85.85" class="pct high">85.85%</td>
	<td data-value="855" class="abs high">734/855</td>
	<td data-value="68.61" class="pct medium">68.61%</td>
	<td data-value="481" class="abs medium">330/481</td>
	<td data-value="92.59" class="pct high">92.59%</td>
	<td data-value="189" class="abs high">175/189</td>
	<td data-value="87.56" class="pct high">87.56%</td>
	<td data-value="812" class="abs high">711/812</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="http://istanbul-js.org/" target="_blank">istanbul</a> at Thu Dec 03 2020 11:46:52 GMT-0800 (Pacific Standard Time)
</div>
</div>
<script src="prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="sorter.js"></script>
</body>
</html>
