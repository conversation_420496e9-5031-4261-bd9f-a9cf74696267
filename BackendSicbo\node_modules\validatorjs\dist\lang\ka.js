require=(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({"./lang/ka":[function(require,module,exports){
module.exports = {
  accepted: ":attribute უნდა იყოს მონიშნული.",
  active_url: ":attribute უნდა იყოს URL მისამართი.",
  after: ":attribute უნდა იყოს :date-ის შემდეგ.",
  after_or_equal: ":attribute უნდა იყოს :date-ის შემდეგ ან მისი ტოლი.",
  alpha: ":attribute უნდა შეიცავდეს მხოლოდ ასოებს.",
  alpha_dash: ":attribute უნდა შეიცავდეს მხოლოდ ასოებს, რიცხვებს და ტირეებს.",
  alpha_num: ":attribute უნდა შეიცავდეს მხოლოდ ასოებს და რიცხვებს.",
  attributes: {},
  array: ":attribute უნდა იყოს მასივი.",
  before: ":attribute უნდა იყოს :date-მდე.",
  before_or_equal: ":attribute უნდა იყოს :date-მდე ან მისი ტოლი.",
  between: {
    numeric: ":attribute უნდა იყოს :min-სა და :max-ს შორის.",
    file: ":attribute უნდა იყოს :min-სა და :max კილობაიტს შორის.",
    string: ":attribute უნდა იყოს :min-სა და :max სიმბოლოს შორის.",
    array: ":attribute-ის რაოდენობა უნდა იყოს :min-დან :max-მდე."
  },
  boolean: ":attribute უნდა იყოს true, false, 0 ან 1.",
  confirmed: ":attribute არ ემთხვევა დადასტურებას.",
  date: ":attribute შეიცავს თარიღის არასწორ ფორმატს.",
  date_format: ":attribute არ ემთხვევა თარიღის ფორმატს: :format.",
  different: ":attribute და :other არ უნდა ემთხვეოდეს ერთმანეთს.",
  digits: ":attribute უნდა შედგებოდეს :digits ციფრისგან.",
  digits_between: ":attribute უნდა შედგებოდეს :min-დან :max ციფრამბდე.",
  dimensions: ":attribute შეიცავს სურათის არასწორ ზომებს.",
  distinct: ":attribute უნდა იყოს უნიკალური.",
  email: ":attribute უნდა იყოს სწორი ელ.ფოსტა.",
  exists: "ასეთი :attribute არ არსებობს.",
  file: ":attribute უნდა იყოს ფაილი.",
  filled: ":attribute აუცილებელია.",
  gt: {
    numeric: ":attribute უნდა იყოს :value-ზე მეტი.",
    file: ":attribute უნდა იყოს :value კილობაიტზე მეტი.",
    string: ":attribute უნდა შეიცავდეს :value სიმბოლოზე მეტს.",
    array: ":attribute უნდა შეიცავლდეს :value ელემენტზე მეტს."
  },
  gte: {
    numeric: ":attribute უნდა იყოს მინიმუმ :value.",
    file: ":attribute უნდა იყოს მინიმუმ :value კილობაიტი.",
    string: ":attribute უნდა შეიცავდეს მინიმუმ :value სიმბოლოს.",
    array: ":attribute უნდა შეიცავდეს მინიმუმ :value ელემენტს."
  },
  hex: "The :attribute field should have hexadecimal format",
  image: ":attribute უნდა იყოს სურათი.",
  in: "მითითებული :attribute არასწორია.",
  in_array: ":attribute უნდა არსებობდეს :other-ში.",
  integer: ":attribute უნდა იყოს მთელი რიცხვი.",
  ip: ":attribute უნდა იყოს IP მისამართი.",
  ipv4: ":attribute უნდა იყოს IPv4 მისამართი.",
  ipv6: ":attribute უნდა იყოს IPv6 მისამართი.",
  json: ":attribute უნდა იყოს JSON ტიპის.",
  lt: {
    numeric: ":attribute უნდა იყოს :value-ზე ნაკლები.",
    file: ":attribute უნდა იყოს :value კილობაიტზე ნაკლები.",
    string: ":attribute უნდა შეიცავდეს :value სიმბოლოზე ნაკლებს.",
    array: ":attribute უნდა შეიცავლდეს :value ელემენტზე ნაკლებს."
  },
  lte: {
    numeric: ":attribute უნდა იყოს მაქსიმუმ :value.",
    file: ":attribute უნდა იყოს მაქსიმუმ :value კილობაიტი.",
    string: ":attribute უნდა შეიცავდეს მაქსიმუმ :value სიმბოლოს.",
    array: ":attribute უნდა შეიცავდეს მაქსიმუმ :value ელემენტს."
  },
  max: {
    numeric: ":attribute არ უნდა აღემატებოდეს :max-ს.",
    file: ":attribute არ უნდა აღემატებოდეს :max კილობაიტს.",
    string: ":attribute არ უნდა აღემატებოდეს :max სიმბოლოს.",
    array: ":attribute-ის რაოდენობა არ უნდა აღემატებოდეს :max-ს."
  },
  mimes: ":attribute უნდა იყოს შემდეგი ტიპის: :values.",
  mimetypes: ":attribute უნდა იყოს შემდეგი ტიპის: :values.",
  min: {
    numeric: ":attribute უნდა იყოს მინიმუმ :min.",
    file: ":attribute უნდა იყოს მინიმუმ :min კილობაიტი.",
    string: ":attribute უნდა შეიცავდეს მინიმუმ :min სიმბოლოს.",
    array: ":attribute უნდა იყოს მინიმუმ :min."
  },
  not_in: "მითითებული :attribute არასწორია.",
  not_regex: ":attribute არასწორ ფორმატშია.",
  numeric: ":attribute უნდა იყოს რიცხვი.",
  present: ":attribute უნდა არსებობდეს, თუნდაც ცარიელი.",
  regex: ":attribute არ ემთხვევა ფორმატს.",
  required: ":attribute აუცილებელია.",
  required_if: ":attribute აუცილებელია, თუ :other-ის მნიშვნელობა ემთხვევა :value-ს.",
  required_unless: ":attribute აუცილებელია, თუ :values არ შეიცავს :other-ს.",
  required_with: ":attribute აუცილებელია, თუ :values მითითებულია.",
  required_with_all: ":attribute აუცილებელია, თუ :values მითითებულია.",
  required_without: ":attribute აუცილებელია, თუ :values არ არის მითითებული.",
  required_without_all: ":attribute აუცილებელია, თუ :values არ არის მითითებული.",
  same: ":attribute და :other უნდა ემთხვეოდეს ერთმანეთს.",
  size: {
    numeric: ":attribute უნდა იყოს :size-ის ტოლი.",
    file: ":attribute უნდა იყოს :size კილობაიტი.",
    string: ":attribute უნდა შედგებოდეს :size სიმბოლოსგან.",
    array: ":attribute უნდა შეიცავდეს :size ელემენტს."
  },
  string: ":attribute უნდა იყოს ტექსტი.",
  timezone: ":attribute უნდა იყოს სასაათო სარტყელი.",
  unique: "ასეთი :attribute უკვე არსებობს.",
  uploaded: ":attribute-ის ატვირთვა ვერ მოხერხდა.",
  url: ":attribute უნდა იყოს URL მისამართი."
};

},{}]},{},[]);
