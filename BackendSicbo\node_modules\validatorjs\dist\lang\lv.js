require=(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({"./lang/lv":[function(require,module,exports){
module.exports = {
  accepted: " :attribute ir jābūt pieņemtam.",
  active_url: " :attribute ir ar nederīgu linku.",
  after: " :attribute ir jābūt ar datumu pēc :datums.",
  after_or_equal: " :attribute ir jābūt ar datumu pēc vai vienādu ar :datums.",
  alpha: " :attribute var saturēt tikai burtus.",
  alpha_dash: " :attribute var saturēt tikai burtus, nummurus un atstarpes.",
  alpha_num: " :attribute var tikai saturēt burtus un nummurus.",
  attributes: {},
  array: " :attribute ir jābūt sakārtotam.",
  before: " :attribute ir jābūt ar datumu pirms :datums.",
  before_or_equal: " :attribute ir jābūt ar datumu pirms vai vienādu ar :datums.",
  between: {
    numeric: " :attribute jābūt starp :min un :max.",
    file: " :attribute jābūt starp :min un :max kilobaiti.",
    string: " :attribute jābūt no :min līdz :max zīmēm.",
    array: " :attribute jābūt no :min līdz :max vienībām."
  },
  boolean: " :attribute laiciņam jābūt atbilstošam vai neatbilstošam.",
  confirmed: " :attribute apstiprinājums neatbilst.",
  date: " :attribute nav derīgs.",
  date_format: " :attribute neatbilst formātam :format.",
  different: " :attribute un :other ir jābūt citiem.",
  digits: " :attribute ir jābūt :digits ciparam.",
  digits_between: " :attribute ir jābūt :min un :max ciparam.",
  dimensions: " :attribute ir nederīgs attēla izmērs.",
  distinct: " :attribute laikam ir dubulta vērtība.",
  email: " :attribute derīgam e-pastam.",
  exists: "Izvēlētais :attribute ir nederīgs.",
  file: " :attribute jābūt failam.",
  filled: ":attribute lauks ir nepieciešams.",
  gt: {
    numeric: "The :attribute must be greater than :value.",
    file: "The :attribute must be greater than :value kilobytes.",
    string: "The :attribute must be greater than :value characters.",
    array: "The :attribute must have more than :value items."
  },
  gte: {
    numeric: "The :attribute must be greater than or equal :value.",
    file: "The :attribute must be greater than or equal :value kilobytes.",
    string: "The :attribute must be greater than or equal :value characters.",
    array: "The :attribute must have :value items or more."
  },
  hex: "The :attribute field should have hexadecimal format",
  image: " :attribute jābūt attēlam.",
  in: "Izvēlētais :attribute ir nederīgs.",
  in_array: " :attribute laiks neeksistē :cits.",
  integer: " :attribute ir jabūt skaitim.",
  ip: " :attribute jābūt derīgai IP adresei.",
  ipv4: "The :attribute must be a valid IPv4 address.",
  ipv6: "The :attribute must be a valid IPv6 address.",
  json: " :attribute jābūt derīgai JSON virknei.",
  lt: {
    numeric: "The :attribute must be less than :value.",
    file: "The :attribute must be less than :value kilobytes.",
    string: "The :attribute must be less than :value characters.",
    array: "The :attribute must have less than :value items."
  },
  lte: {
    numeric: "The :attribute must be less than or equal :value.",
    file: "The :attribute must be less than or equal :value kilobytes.",
    string: "The :attribute must be less than or equal :value characters.",
    array: "The :attribute must not have more than :value items."
  },
  max: {
    numeric: " :attribute nedrīkst pārsniegt :max.",
    file: " :attribute nedrīkst pārsniegt :max kilobaiti.",
    string: " :attribute nedrīkst pārsniegt :max zīmes.",
    array: " :attribute nedrīkst pārsniegt :max vienības."
  },
  mimes: " :attribute jābūt faila tipam: :values",
  mimetypes: " :attribute jābūt faile tipam: :values.",
  min: {
    numeric: " :attribute jābūt vismaz :min.",
    file: " :attribute jābūt vismaz :min kilobaiti.",
    string: " :attribute jābūt vismaz :min zīmes.",
    array: " :attribute jāsatur vismaz :min vienības."
  },
  not_in: " izvēlieties :attribute ir nederīgs.",
  not_regex: "The :attribute format is invalid.",
  numeric: " :attribute jābūt skaitlim.",
  present: " :attribute laikums ir nepieciešams.",
  regex: " :attribute formāts ir nederīgs.",
  required: " :attribute laukums ir nepieciešams.",
  required_if: " :attribute laukums ir nepieciešams, ja vien :other ir :values.",
  required_unless: " :attribute laukums ir nepieciešams, ja vien :other ir :values.",
  required_with: " :attribute laukums ir nepieciešams, kad :values ir pieejama.",
  required_with_all: " :attribute laukums ir nepieciešams, kad :values ir pieejama.",
  required_without: " :attribute laukums ir nepieciešams, kad :values nav pieejama.",
  required_without_all: " :attribute laukums ir nepieciešams, kad neviena no :values nav pieejama.",
  same: " :attribute un :citiem ir jāsakrīt.",
  size: {
    numeric: " :attribute jābūt :size.",
    file: " :attribute jābūt :size kilobaiti.",
    string: " :attribute jābūt :size zīmes.",
    array: " :attribute jāsatur :size vienības."
  },
  string: " :attribute jābūt virknē.",
  timezone: " :attribute jābūt derīgā zonā.",
  unique: " :attribute jau ir aizņemts.",
  uploaded: " :attribute netika augšuplādēts.",
  url: " :attribute formāts ir nederīgs."
};

},{}]},{},[]);
