require=(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({"./lang/pt":[function(require,module,exports){
module.exports = {
  accepted: "O campo :attribute deverá ser aceite.",
  active_url: "O campo :attribute não contém um URL válido.",
  after: "O campo :attribute deverá conter uma data posterior a :date.",
  after_or_equal: "O campo :attribute deverá conter uma data posterior ou igual a :date.",
  alpha: "O campo :attribute deverá conter apenas letras.",
  alpha_dash: "O campo :attribute deverá conter apenas letras, números e traços.",
  alpha_num: "O campo :attribute deverá conter apenas letras e números .",
  attributes: {},
  array: "O campo :attribute deverá conter uma coleção de elementos.",
  before: "O campo :attribute deverá conter uma data anterior a :date.",
  before_or_equal: "O Campo :attribute deverá conter uma data anterior ou igual a :date.",
  between: {
    numeric: "O campo :attribute deverá ter um valor entre :min - :max.",
    file: "O campo :attribute deverá ter um tamanho entre :min - :max kilobytes.",
    string: "O campo :attribute deverá conter entre :min - :max caracteres.",
    array: "O campo :attribute deverá conter entre :min - :max elementos."
  },
  boolean: "O campo :attribute deverá conter o valor verdadeiro ou falso.",
  confirmed: "A confirmação para o campo :attribute não coincide.",
  date: "O campo :attribute não contém uma data válida.",
  date_format: "A data indicada para o campo :attribute não respeita o formato :format.",
  different: "Os campos :attribute e :other deverão conter valores diferentes.",
  digits: "O campo :attribute deverá conter :digits caracteres.",
  digits_between: "O campo :attribute deverá conter entre :min a :max caracteres.",
  dimensions: "O campo :attribute deverá conter uma dimensão de imagem válida.",
  distinct: "O campo :attribute contém um valor duplicado.",
  email: "O campo :attribute não contém um endereço de correio eletrónico válido.",
  exists: "O valor selecionado para o campo :attribute é inválido.",
  file: "O campo :attribute deverá conter um ficheiro.",
  filled: "É obrigatória a indicação de um valor para o campo :attribute.",
  gt: {
    numeric: "The :attribute must be greater than :value.",
    file: "The :attribute must be greater than :value kilobytes.",
    string: "The :attribute must be greater than :value characters.",
    array: "The :attribute must have more than :value items."
  },
  gte: {
    numeric: "The :attribute must be greater than or equal :value.",
    file: "The :attribute must be greater than or equal :value kilobytes.",
    string: "The :attribute must be greater than or equal :value characters.",
    array: "The :attribute must have :value items or more."
  },
  hex: "The :attribute field should have hexadecimal format",
  image: "O campo :attribute deverá conter uma imagem.",
  in: "O campo :attribute não contém um valor válido.",
  in_array: "O campo :attribute não existe em :other.",
  integer: "O campo :attribute deverá conter um número inteiro.",
  ip: "O campo :attribute deverá conter um IP válido.",
  ipv4: "O campo :attribute deverá conter um IPv4 válido.",
  ipv6: "O campo :attribute deverá conter um IPv6 válido.",
  json: "O campo :attribute deverá conter um texto JSON válido.",
  lt: {
    numeric: "The :attribute must be less than :value.",
    file: "The :attribute must be less than :value kilobytes.",
    string: "The :attribute must be less than :value characters.",
    array: "The :attribute must have less than :value items."
  },
  lte: {
    numeric: "The :attribute must be less than or equal :value.",
    file: "The :attribute must be less than or equal :value kilobytes.",
    string: "The :attribute must be less than or equal :value characters.",
    array: "The :attribute must not have more than :value items."
  },
  max: {
    numeric: "O campo :attribute não deverá conter um valor superior a :max.",
    file: "O campo :attribute não deverá ter um tamanho superior a :max kilobytes.",
    string: "O campo :attribute não deverá conter mais de :max caracteres.",
    array: "O campo :attribute não deverá conter mais de :max elementos."
  },
  mimes: "O campo :attribute deverá conter um ficheiro do tipo: :values.",
  mimetypes: "O campo :attribute deverá conter um ficheiro do tipo: :values.",
  min: {
    numeric: "O campo :attribute deverá ter um valor superior ou igual a :min.",
    file: "O campo :attribute deverá ter no mínimo :min kilobytes.",
    string: "O campo :attribute deverá conter no mínimo :min caracteres.",
    array: "O campo :attribute deverá conter no mínimo :min elementos."
  },
  not_in: "O campo :attribute contém um valor inválido.",
  not_regex: "The :attribute format is invalid.",
  numeric: "O campo :attribute deverá conter um valor numérico.",
  present: "O campo :attribute deverá estar presente.",
  regex: "O formato do valor para o campo :attribute é inválido.",
  required: "É obrigatória a indicação de um valor para o campo :attribute.",
  required_if:
    "É obrigatória a indicação de um valor para o campo :attribute quando o valor do campo :other é igual a :value.",
  required_unless:
    "É obrigatória a indicação de um valor para o campo :attribute a menos que :other esteja presente em :values.",
  required_with: "É obrigatória a indicação de um valor para o campo :attribute quando :values está presente.",
  required_with_all:
    "É obrigatória a indicação de um valor para o campo :attribute quando um dos :values está presente.",
  required_without: "É obrigatória a indicação de um valor para o campo :attribute quando :values não está presente.",
  required_without_all:
    "É obrigatória a indicação de um valor para o campo :attribute quando nenhum dos :values está presente.",
  same: "Os campos :attribute e :other deverão conter valores iguais.",
  size: {
    numeric: "O campo :attribute deverá conter o valor :size.",
    file: "O campo :attribute deverá ter o tamanho de :size kilobytes.",
    string: "O campo :attribute deverá conter :size caracteres.",
    array: "O campo :attribute deverá conter :size elementos."
  },
  string: "O campo :attribute deverá conter texto.",
  timezone: "O campo :attribute deverá ter um fuso horário válido.",
  unique: "O valor indicado para o campo :attribute já se encontra registado.",
  uploaded: "O upload do ficheiro :attribute falhou.",
  url: "O formato do URL indicado para o campo :attribute é inválido."
};

},{}]},{},[]);
