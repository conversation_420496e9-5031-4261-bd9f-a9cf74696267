require=(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({"./lang/sq":[function(require,module,exports){
module.exports = {
  accepted: ":attribute duhet të pranohet.",
  active_url: ":attribute nuk është adresë e saktë.",
  after: ":attribute duhet të jetë datë pas :date.",
  after_or_equal: "The :attribute must be a date after or equal to :date.",
  alpha: ":attribute mund të përmbajë vetëm shkronja.",
  alpha_dash: ":attribute mund të përmbajë vetëm shkronja, numra, dhe viza.",
  alpha_num: ":attribute mund të përmbajë vetëm shkronja dhe numra.",
  attributes: {},
  array: ":attribute duhet të jetë një bashkësi (array).",
  before: ":attribute duhet të jetë datë para :date.",
  before_or_equal: "The :attribute must be a date before or equal to :date.",
  between: {
    numeric: ":attribute duhet të jetë midis :min - :max.",
    file: ":attribute duhet të jetë midis :min - :max kilobajtëve.",
    string: ":attribute duhet të jetë midis :min - :max karaktereve.",
    array: ":attribute duhet të jetë midis :min - :max elementëve."
  },
  boolean: "Fusha :attribute duhet të jetë e vërtetë ose e gabuar",
  confirmed: ":attribute konfirmimi nuk përputhet.",
  date: ":attribute nuk është një datë e saktë.",
  date_format: ":attribute nuk i përshtatet formatit :format.",
  different: ":attribute dhe :other duhet të jenë të ndryshme.",
  digits: ":attribute duhet të jetë :digits shifra.",
  digits_between: ":attribute duhet të jetë midis :min dhe :max shifra.",
  dimensions: "The :attribute has invalid image dimensions.",
  distinct: "The :attribute field has a duplicate value.",
  email: ":attribute formati është i pasaktë.",
  exists: ":attribute përzgjedhur është i/e pasaktë.",
  file: "The :attribute must be a file.",
  filled: "Fusha :attribute është e kërkuar.",
  gt: {
    numeric: "The :attribute must be greater than :value.",
    file: "The :attribute must be greater than :value kilobytes.",
    string: "The :attribute must be greater than :value characters.",
    array: "The :attribute must have more than :value items."
  },
  gte: {
    numeric: "The :attribute must be greater than or equal :value.",
    file: "The :attribute must be greater than or equal :value kilobytes.",
    string: "The :attribute must be greater than or equal :value characters.",
    array: "The :attribute must have :value items or more."
  },
  hex: "The :attribute field should have hexadecimal format",
  image: ":attribute duhet të jetë imazh.",
  in: ":attribute përzgjedhur është i/e pasaktë.",
  in_array: "The :attribute field does not exist in :other.",
  integer: ":attribute duhet të jetë numër i plotë.",
  ip: ":attribute duhet të jetë një IP adresë e saktë.",
  ipv4: "The :attribute must be a valid IPv4 address.",
  ipv6: "The :attribute must be a valid IPv6 address.",
  json: "The :attribute must be a valid JSON string.",
  lt: {
    numeric: "The :attribute must be less than :value.",
    file: "The :attribute must be less than :value kilobytes.",
    string: "The :attribute must be less than :value characters.",
    array: "The :attribute must have less than :value items."
  },
  lte: {
    numeric: "The :attribute must be less than or equal :value.",
    file: "The :attribute must be less than or equal :value kilobytes.",
    string: "The :attribute must be less than or equal :value characters.",
    array: "The :attribute must not have more than :value items."
  },
  max: {
    numeric: ":attribute nuk mund të jetë më tepër se :max.",
    file: ":attribute nuk mund të jetë më tepër se :max kilobajtë.",
    string: ":attribute nuk mund të jetë më tepër se :max karaktere.",
    array: ":attribute nuk mund të ketë më tepër se :max elemente."
  },
  mimes: ":attribute duhet të jetë një dokument i tipit: :values.",
  mimetypes: ":attribute duhet të jetë një dokument i tipit: :values.",
  min: {
    numeric: ":attribute nuk mund të jetë më pak se :min.",
    file: ":attribute nuk mund të jetë më pak se :min kilobajtë.",
    string: ":attribute nuk mund të jetë më pak se :min karaktere.",
    array: ":attribute nuk mund të ketë më pak se :min elemente."
  },
  not_in: ":attribute përzgjedhur është i/e pasaktë.",
  not_regex: "The :attribute format is invalid.",
  numeric: ":attribute duhet të jetë një numër.",
  present: "The :attribute field must be present.",
  regex: "Formati i :attribute është i pasaktë.",
  required: "Fusha :attribute është e kërkuar.",
  required_if: "Fusha :attribute është e kërkuar kur :other është :value.",
  required_unless: "The :attribute field is required unless :other is in :values.",
  required_with: "Fusha :attribute është e kërkuar kur :values ekziston.",
  required_with_all: "Fusha :attribute është e kërkuar kur :values ekziston.",
  required_without: "Fusha :attribute është e kërkuar kur :values nuk ekziston.",
  required_without_all: "Fusha :attribute është e kërkuar kur nuk ekziston asnjë nga :values.",
  same: ":attribute dhe :other duhet të përputhen.",
  size: {
    numeric: ":attribute duhet të jetë :size.",
    file: ":attribute duhet të jetë :size kilobajtë.",
    string: ":attribute duhet të jetë :size karaktere.",
    array: ":attribute duhet të ketë :size elemente."
  },
  string: ":attribute duhet të jetë varg.",
  timezone: ":attribute duhet të jetë zonë e saktë.",
  unique: ":attribute është marrë tashmë.",
  uploaded: "The :attribute failed to upload.",
  url: "Formati i :attribute është i pasaktë."
};

},{}]},{},[]);
