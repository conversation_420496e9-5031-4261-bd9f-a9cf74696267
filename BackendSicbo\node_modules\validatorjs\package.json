{"name": "validatorjs", "description": "Validation library inspired by <PERSON><PERSON>'s Validator", "version": "3.22.1", "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "main": "./src/validator.js", "repository": {"type": "git", "url": "https://github.com/mike<PERSON>son/validatorjs"}, "bugs": {"url": "https://github.com/mike<PERSON>son/validatorjs/issues?labels=bug&milestone=1&page=1&state=open"}, "keywords": ["<PERSON><PERSON><PERSON>", "validatorjs", "validator.js", "data validation", "validator", "validate", "validation", "data", "laravel", "laravel-validator-for-js"], "devDependencies": {"browserify": "^13.0.1", "chai": "~3.5.0", "eslint": "5.8.0", "grunt": "1.0.4", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-contrib-concat": "~1.0.1", "grunt-contrib-watch": "~1.0.0", "jit-grunt": "^0.10.0", "karma": "4.3.0", "karma-browserify": "6.1.0", "karma-chai": "~0.1.0", "karma-chrome-launcher": "3.1.0", "karma-coverage": "~1.0.0", "karma-growl-notifications-reporter": "0.0.2", "karma-mocha": "1.3.0", "karma-mocha-reporter": "2.2.5", "mocha": "~2.5.3", "mocha-better-spec-reporter": "github:mike<PERSON><PERSON>/mocha-better-spec-reporter", "pump": "3.0.0", "puppeteer": "1.19.0", "rimraf": "3.0.0"}, "scripts": {"pre-commit": "npm run lint:spec && npm run lint && npm run test", "build:all": "./scripts/build.sh", "build": "rimraf dist && grunt dist", "lint": "eslint \"./src/**/*.js\" --fix", "lint:spec": "eslint \"./spec/**/*.js\" --rule 'no-redeclare: off'", "lint:fix": "eslint \"./src/**/*.js\" --fix", "test:node": "node node_modules/mocha/bin/mocha spec --slow 500 --reporter mocha-better-spec-reporter", "test:watch": "node node_modules/mocha/bin/mocha --watch spec --slow 500 --reporter mocha-better-spec-reporter", "test:browser": "npm run build:all && node node_modules/karma/bin/karma start --single-run", "test": "npm run test:node", "todo": "node scripts/todo.js"}}