{"name": "xml-name-validator", "description": "Validates whether a string matches the production for an XML name or qualified name", "keywords": ["xml", "name", "qname"], "version": "2.0.1", "author": "Domenic Denicola <<EMAIL>> (https://domenic.me/)", "license": "WTFPL", "repository": "jsdom/xml-name-validator", "main": "lib/xml-name-validator.js", "files": ["lib/"], "scripts": {"prepublish": "node scripts/generate-grammar.js < lib/grammar.pegjs > lib/generated-parser.js", "pretest": "npm run prepublish", "test": "mocha", "lint": "jshint lib && jscs lib"}, "devDependencies": {"jscs": "^1.8.1", "jshint": "^2.5.10", "mocha": "^2.0.1", "waka": "0.1.2"}}