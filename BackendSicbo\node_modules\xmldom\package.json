{"name": "xmldom", "version": "0.1.31", "description": "A W3C Standard XML DOM(Level2 CORE) implementation and parser(DOMParser/XMLSerializer).", "keywords": ["w3c", "dom", "xml", "parser", "javascript", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "XMLSerializer"], "author": "jindw <<EMAIL>> (http://www.xidea.org)", "homepage": "https://github.com/xmldom/xmldom", "repository": {"type": "git", "url": "git://github.com/xmldom/xmldom.git"}, "main": "./dom-parser.js", "scripts": {"test": "proof platform win32 && proof test */*/*.t.js || t/test"}, "engines": {"node": ">=0.1"}, "dependencies": {}, "devDependencies": {"proof": "0.0.28"}, "maintainers": [{"name": "jindw", "email": "<EMAIL>", "url": "http://www.xidea.org"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://webservices20.blogspot.com/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON>@gmail.com", "web": "https://github.com/nightwing"}, {"name": "<PERSON>", "email": "<EMAIL>", "web": "http://www.prettyrobots.com/"}], "bugs": {"email": "<EMAIL>", "url": "http://github.com/jindw/xmldom/issues"}, "license": "(LGPL-2.0 or MIT)"}