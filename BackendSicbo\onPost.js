
let User     = require('./User');
let TaiXiu   = require('./TaiXiu');
let Shop     = require('./Shop');
let GiftCode = require('./GiftCode');
let Game     = require('./Game');
let OTP      = require('./OTP');
let Event    = require('./event/index');
let message  = require('./Message');
let IAP = require('./IAP');

// Social Function
let Apple = require('./Apple');
let Facebook = require('./Facebook');
let Friends = require('./Friends');

let DealerTip = require('./DealerTip');
let otp_config = require('./../../config/otp');
var MultiLang = require('./MultiLang');
var wallets = require('./Wallets');
let clientUpdateLang = require('./ClientUpdateLange');
let updateInterruptChipsForUser = require('./UpdateInterruptChipsForUser');

module.exports = function(client, p){		
	if (!!p) {
		if (!!p.authenticationout){
			User.authenticationout(client, p.authenticationout);
		}

		// Connect Facebook For User
		if (!!p.fbconnect) {
			Facebook(client, p.fbconnect);
		}

		// Connect Apple For User
		if (!!p.appleconnect) {
			Apple(client, p.appleconnect);
		}

		// Connect Apple For User
		if (!!p.friendconnect) {
			Friends(client, p.friendconnect);
		}

		if (!!p.change_lang) {
			clientUpdateLang(client, p.change_lang);
		}

		if (!!p.multi_lang) {
			let multi_lang = (p.multi_lang != 'undefined') ? p.multi_lang : client.country.toLowerCase();
			MultiLang(client, multi_lang);
		}

		// Tip for Dealer
		if (!!p.dealertip) {
			DealerTip(client, p.dealertip);
		}

		if (!!p.checkActivePhone){
			User.checkActivePhone(client);
		}

		if (!!p.signName){
			User.signName(client, p.signName);
		}

		if (!!p.user){
			User.onData(client, p.user);
		}

		if (!!p.taixiu){
			TaiXiu(client, p.taixiu);
		}

		if (!!p.shop){
			Shop(client, p.shop);
		}

		if (!!p.giftcode){
			GiftCode(client, p.giftcode);
		}

		if (!!p.iap){
			IAP(client, p.iap);
		}

		if (!!p.g){
			Game(client, p.g);
		}

		if (!!p.scene && typeof p.scene === 'string'){
			client.scene = p.scene;
			User.next_scene(client);
		}

		if (!!p.otp){
			p.otp.type = otp_config.type;
			OTP(client, p.otp);
		}

		if (!!p.event){
			Event(client, p.event);
		}

		if (!!p.message){
			message(client, p.message);
		}

		if (!!p.wallets) {
			wallets(client, p.wallets);
		}

		if (!!p.interrupt) {
			updateInterruptChipsForUser(client, p.interrupt);	
		}
	}
	client = null;
	p = null;
};
