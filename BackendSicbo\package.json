{"name": "xo79", "version": "1.17.2", "main": "server.js", "scripts": {"start": "node server.js", "test": "echo \"Error: no test specified\" && exit 1", "development": "nodemon --inspect=0.0.0.0 -L server.js"}, "nodemonConfig": {"ext": "js"}, "dependencies": {"axios": "^0.20.0", "bcrypt": "5.1.1", "body-parser": "^1.19.0", "canvas": "^3.1.0", "cloudscraper": "^4.1.2", "cron": "^1.7.2", "dotenv": "^8.1.0", "ejs": "^2.7.1", "express": "^4.17.1", "express-ws": "^4.0.0", "iap": "^1.1.1", "is-mobile": "^2.1.0", "md5": "^2.3.0", "moment": "^2.28.0", "mongoose": "^5.7.1", "mongoose-auto-increment-reworked": "^1.2.1", "mongoose-long": "^0.2.1", "morgan": "^1.9.1", "request": "^2.88.0", "saslprep": "^1.0.3", "shortid": "^2.2.15", "svg-captcha": "^1.4.0", "svg2img": "^0.6.1", "validator": "^11.1.0", "validatorjs": "^3.19.2"}, "devDependencies": {"nodemon": "^2.0.4"}, "repository": {"type": "git", "url": ""}, "homepage": "", "author": "", "license": "ISC", "keywords": [], "description": ""}