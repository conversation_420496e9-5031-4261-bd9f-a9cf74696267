
let users = require('./socketUsers');
// let admin = require('./socketAdmin');
// Router Websocket

module.exports = function(app, redT) {
	// User Websocket
	app.ws('/websocket', function(ws, req) {		
		// User country based on IP
		redT.country = req.headers['cf-ipcountry'] ? req.headers['cf-ipcountry'] : 'EN';
		// User device
		redT.user_device_agent = req.headers['user-agent'];
		// User language
		redT.user_language_in_device = req.headers['accept-language'];
		redT.user_ip = req.headers['cf-connecting-ip'] ? req.headers['cf-connecting-ip'] : '';
		
		users(ws, redT);
	});

	// User Websocket for socketAPI
	app.ws('/socketapi', function(ws, req) {		
		// User country based on IP
		redT.country = req.headers['cf-ipcountry'] ? req.headers['cf-ipcountry'] : 'EN';
		// User device
		redT.user_device_agent = req.headers['user-agent'];
		// User language
		redT.user_language_in_device = req.headers['accept-language'];
		redT.user_ip = req.headers['cf-connecting-ip'] ? req.headers['cf-connecting-ip'] : '';

		users(ws, redT);
	});
	
	// // Admin Websocket
	// app.ws('/admin', function(ws, req) {
	// 	// User country based on IP
	// 	redT.country = req.headers['cf-ipcountry'] ? req.headers['cf-ipcountry'] : 'EN';
	// 	// User device
	// 	redT.user_device_agent = req.headers['user-agent'];
	// 	// User language
	// 	redT.user_language_in_device = req.headers['accept-language'];
	// 	redT.user_ip = req.headers['cf-connecting-ip'] ? req.headers['cf-connecting-ip'] : '';

	// 	admin(ws, redT);
	// });
};
