// server.js
require('dotenv').config();

var express = require('express');
var app = express();
var port = 1203;
var expressWs = require('express-ws')(app);
var bodyParser = require('body-parser');
var morgan = require('morgan');

// Setting & Connect to the Database
var configDB = require('./config/database');
var mongoose = require('mongoose');

require('mongoose-long')(mongoose); // INT 64bit
mongoose.set('useFindAndModify', false);
mongoose.set('useCreateIndex', true);
mongoose.connect(configDB.url, configDB.options)
    .then(() => {
        console.log('MongoDB connected successfully');
    })
    .catch(function(error) {
        if (error)
            console.log('Connect to MongoDB failed', error);
        else
            console.log('Connect to MongoDB success');
    });

// Configuration of admin account (default) & default data
// require('./config/admin');

// Read data from
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: false }));
app.use(morgan('combined'));
app.engine('html', require('ejs').renderFile);

// server socket
var redT = expressWs.getWss();

// get server IP
var networkInterfaces = require('./Helpers/networkInterfaces');
redT.server = networkInterfaces.getNetworkInterfaces();
let User      = require('./Models/Users');

redT.users  = [];
let sendAllUser = function(data, noBroadcast = null){
    this.clients.forEach(function(client){
        if (void 0 === client.admin && client.auth === true && noBroadcast !== client) {
            client.red(data);
        }
    });
};
redT.sendAllUser = sendAllUser;
// require('./Helpers/socketUser')(redT); // Add socket functions
require('./routerSocket')(app, redT); // loading of routes WebSocket

app.listen(port, function(){
    console.log("Server " + redT.server + " listen on port", port);
    User.updateMany({'local.is_login':true},{$set:{'local.is_login':false}}).exec();
});


