let User     = require('../Models/Users');
let UserInfo = require('../Models/UserInfo');

// Game User

let validator   = require('validator');
let Helper      = require('../Helpers/Helpers');

 
var lang		= require ('../config/languages');

// let authenticationout = function(client, data){
// 	User.findOne({'local.username':data.username}, function(err, user){
// 		if (!!err) console.warn(err);

// 		if (!!user){
// 			User.updateOne({'local.username':data.username}, {$set: {'local.lastLogout': new Date(), 'local.is_login': false}}).exec();
// 		} 
// 	});
// };

// let checkActivePhone = async function(client){
// 	var checkPhone = await Phone.findOne({uid: client.UID}).exec();
// 	if(!!checkPhone){
// 		var user = await UserInfo.findOne({id: client.UID}).exec();
// 		if(!!user){
// 			client.red({checkActivePhone:{red:user.red,phone:checkPhone.region+checkPhone.phone}});
// 		}
// 	}
// }

let first = function(client, callback, at){
	// Set up language regarding IP Country	

	let d = new Date();
	let hour = (d.getHours()) % 24;
	
	console.warn(new Date() + ' Local Time: ' + hour);

	UserInfo.findOne({id: client.UID}, 'name lastVip redPlay red ketSat UID cmt email security joinedOn iapUser noAdsUser isStore store version lang paid_money cashout_money iap_money avatar trusted giftVipStatus id unlock_join_private_room unlock_private_room facebook apple daily_bonus_perio1 daily_bonus_perio2 daily_bonus_connect', function(err, user) {
		if (!!err) console.log(err);

		if (!!user) {
			user = user._doc;
			
			user.showall = 0;
			user.language = user.lang ? user.lang : client.redT.country.toLowerCase();

			delete user._id;
			delete user.id;

			if (!Helper.isEmpty(user.email)) {
				user.email = Helper.cutEmail(user.email);
			}

			client.profile = {name: user.name};

			if (!!client && !!client.redT)
				addToListOnline(client);

			let data = {
				Authorized: true,
				user:       user
			};

			// receivedRewardTime for Client
			user.receivedRewardTime = 3;
			if(client == null) return;
			client.red(data);
			client.at = at;
			if(!!callback) callback(client);
		}else{	
			client.red({Authorized: false});
		}
	});
};

// let updateCoint = function(client){
// 	UserInfo.findOne({id:client.UID}, 'red', function(err, user){
// 		if (!!user) {
// 			client.red({user: {red: user.red, xu: 0}});
// 		}
// 	});
// };

// let signName = function(client, name){
// 	// Set up language regarding IP Country	
// 	var lang_used = Helper.Language(client.redT.country, lang);

// 	if (!!name) {
// 		name = ''+name+'';
// 		let az09     = new RegExp('^[a-zA-Z0-9]+$');
// 		let testName = az09.test(name);

// 		if (!validator.isLength(name, {min: 3, max: 14})) {
// 			client.red({notice: {title: lang_used.title.inform, text: lang_used.text.registration_signname_text}});
// 		}else if (!testName) {
// 			client.red({notice: {title: lang_used.title.inform, text: lang_used.text.registration_signname_no_special_chars}});
// 		} else{
// 			UserInfo.findOne({id: client.UID}, 'name red ketSat UID phone email cmt security joinedOn isStore store lang paid_money', function(err, d){
// 				if (!!err) console.log(err);

// 				if (!d) {
// 					name = name.toLowerCase();
// 					User.findOne({'_id':client.UID}, function(err, base){
// 						if (!!err) console.log(err);
						
// 						var regex = new RegExp('^' + base.local.username + '$', 'i');
// 						var testBase = regex.test(name);
// 						if (!!testBase) {
// 							client.red({notice: {title: lang_used.title.inform, text: lang_used.text.registration_signame_no_account_name}});
// 						}else{
// 							var version = !!base.local ? base.local.base_version : '';
// 							var base_store = !!base.local ? base.local.base_store : 'android';
// 							var base_isStore = !!base.local ? base.local.base_isStore : false;
// 							UserSetup.UserSetup(client, name, base_isStore, false, '', base_store, version, (!!base.facebook.id || !!base.apple.id), (!!base.facebook.id), !!base.apple.id);
// 						}
// 					});
// 				}else{
// 					first(client);
// 				}
// 			});
// 		}
// 	}
// };

// let changePassword = function(client, data){
// 	// Set up language regarding IP Country
// 	var lang_used = Helper.Language(client.redT.country, lang);

// 	if (!!data && !!data.passOld && !!data.passNew && !!data.passNew2) {
// 		if (!validator.isLength(data.passOld, {min: 6, max: 32})) {
// 			client.red({notice: {title: lang_used.title.inform, text: lang_used.text.pass_length}});
// 		}else if (!validator.isLength(data.passNew, {min: 6, max: 32})) {
// 			client.red({notice: {title: lang_used.title.inform, text: lang_used.text.pass_length}});
// 		}else if (!validator.isLength(data.passNew2, {min: 6, max: 32})) {
// 			client.red({notice: {title: lang_used.title.inform, text: lang_used.text.pass_length}});
// 		} else if (data.passOld == data.passNew){
// 			client.red({notice: {title: lang_used.title.inform, text: lang_used.text.pass_new_old_fail}});
// 		} else if (data.passNew != data.passNew2){
// 			client.red({notice: {title: lang_used.title.inform, text: lang_used.text.pass_2_not_same}});
// 		} else {
// 			User.findOne({'_id': client.UID}, function(err, user){
// 				if (!!user) {
// 					if (user.local.username == data.passNew) {
// 						client.red({notice: {title: lang_used.title.inform, text: lang_used.text.pass_not_same_charname}});
// 					}else{
// 						if (Helper.validPassword(data.passOld, user.local.password)) {
// 							User.updateOne({'_id': client.UID}, {$set:{'local.password': Helper.generateHash(data.passNew)}}).exec();
// 							client.red({notice:{load: 0, title: lang_used.title.success, text: lang_used.text.pass_changed_successful}});
// 						}else{
// 							client.red({notice:{load: 0, title: lang_used.title.fail, text: lang_used.text.pass_old_incorrect}});
// 						}
// 					}
// 				}
// 			});
// 		}
// 	}
// };

// let getLevel = function(client){
// 	UserInfo.findOne({id:client.UID}, 'id lastVip redPlay vip paid_money iap_money giftVipStatus unlock_join_private_room unlock_private_room', function(err, user){
// 		if (user) {
			
// 			var level = _getLevel(user);
// 			let systemLevel = Helper.getSystemLevel(user.redPlay);

// 			client.red({profile:{level: {
// 				config: level_vip.vip,
// 				giftVipStatus: level.giftVipStatus,
// 				level: level.vipLevel, 
// 				vipNext: level.vipNext, 
// 				vipPre: level.vipPre, 
// 				vipTL: user.vip, 
// 				vipHT: level.vipHT,
// 				redPlay: level.redPlay,
// 				nextLvlPercent: systemLevel.percent_nextLevel}}});
// 		}else{
// 			client.close();
// 		}
// 	});
// };

// let _getLevel = function(user)
// {
// 	if (user == null) return null;
// 	var total_paid = parseInt(user.paid_money) + parseInt(user.iap_money) * level_vip.iap_percent;

// 	let vipHT = total_paid; // Current VIP Points
// 	let redPlay = user.redPlay;
// 	// Cấp vip hiện tại
// 	let vipLevel = 0;
// 	let vipPre = 0;   // current VIP Points
// 	let vipNext = level_vip.vip["0"].vipNext; // next VIP Points

// 	var giftVipStatus = {};
// 	if (user.giftVipStatus != '') {
// 		try {
// 			giftVipStatus = JSON.parse(user.giftVipStatus);
// 		} catch (e) {
// 			console.warn(e);
// 		}
// 	}
	
// 	var isUpdate = false;
// 	for (var level in level_vip.vip) {
// 		var levelInt = parseInt(level.toString());
// 		var levelString = parseInt(level.toString());
// 		if (total_paid >= level_vip.vip[levelString].vipPre ) {
// 			if(giftVipStatus[levelString] == null)
// 			{
// 				giftVipStatus[levelString] = false;
// 				isUpdate = true;
// 			}
// 			if(levelInt > vipLevel)
// 			{
// 				vipLevel = levelInt;
// 				vipPre = level_vip.vip[levelString].vipPre;
// 				vipNext = level_vip.vip[levelString].vipNext;
// 			}
// 		}
// 	}
// 	var set = {};
// 	if (giftVipStatus['3'] != null && giftVipStatus['3'] == false && user.unlock_join_private_room == false) {
// 		set.unlock_join_private_room = true;
// 		// giftVipStatus['3'] = true;
// 		isUpdate = true;
// 	}
// 	if (giftVipStatus['4'] != null && giftVipStatus['4'] == false && user.unlock_private_room == false) {
// 		set.unlock_private_room = true;
// 		// giftVipStatus['4'] = true;
// 		isUpdate = true;
// 	}
// 	if (isUpdate) {
// 		set.giftVipStatus = JSON.stringify(giftVipStatus);
// 		UserInfo.findOneAndUpdate({ id: user.id }, { $set: set }, function(err, updated){
// 			if(err) console.warn(err)
// 			if(updated) console.log('updated', updated.giftVipStatus)
// 			else console.warn('cannot update giftvip')
// 		});
// 	}
// 	return {
// 		giftVipStatus: JSON.stringify(giftVipStatus),
// 		vipLevel: vipLevel,
// 		vipNext: vipNext,
// 		vipPre: vipPre,
// 		vipHT: vipHT,
// 		redPlay: redPlay,
// 	}
// }

// let updateAvatar = function(client, data)
// {
// 	UserInfo.findOneAndUpdate({id:client.UID}, {$set: {avatar: data}}, {returnOriginal: false}, function(err, user){
// 		if (!!user) {
// 			client.red({user: {updateAvatar: user.avatar}});
// 		}
// 	});
// };

// let deleteForever = function(client, data)
// {
// 	UserInfo.findOne({type: false, id: client.UID}, {}, {}, function(err, userinfo){
// 		if (!!err) console.log(err);
// 		if (!!userinfo && userinfo.UID == data) {
// 			//Log FireBase
// 			Helper.logFireBaseEvent(client, 'delete_forever', {});
			
// 			// Remove				
// 			TaiXiu_user.deleteOne({uid: userinfo.id}).exec();
// 			LongLan_user.deleteOne({uid: userinfo.id}).exec();
// 			Roulette_user.deleteOne({uid: userinfo.id}).exec();
// 			Phone.deleteOne({uid: userinfo.id}).exec();

// 			User.deleteOne({_id: Object(userinfo.id)}).exec();
// 			userinfo.deleteOne();
// 		}else{
// 			console.warn(new Date() + "Someone try to delete account");
// 		}
// 	});
// };

// let rewardVideo = function(client, data){
// 	var lang_used = Helper.Language(client.redT.country, lang);
// 	UserInfo.findOne({id:client.UID}, 'red', function(err, user){
// 		if (!!err) console.log(err);
// 		if (!!user) {
// 			user.red += parseInt(dailybonus.bonus_connect);
// 			UserInfo.findOneAndUpdate({id: client.UID}, {$set: {red: user.red}}).exec();

// 			//Log FireBase
// 			Helper.logFireBaseEvent(client, 'rewardVideo', {red: user.red});

// 			client.red({notice:{title: lang_used.title.inform, text: lang_used.text.get_bonus_successful}, user:{red: user.red}});
// 		}
// 	});
// };

// let refundVideoPoker = function(client, amount, notice = true){
// 	var lang_used = Helper.Language(client.redT.country, lang);
// 	UserInfo.findOne({id:client.UID}, 'red', function(err, user){
// 		if (!!err) console.log(err);
// 		if (!!user) {
// 			user.red += parseInt(amount);
// 			UserInfo.findOneAndUpdate({id: client.UID}, {$set: {red: user.red}}).exec();

// 			//Log FireBase
// 			Helper.logFireBaseEvent(client, 'refundVideoPoker', {red: user.red});
// 			if(!!notice){
// 				client.red({notice:{title: lang_used.title.inform, text: lang_used.text.refund_coins + amount + " coins"}, user:{red: user.red}});
// 			}else client.red({user:{red: user.red}});
// 		}
// 	});
// };

function addToListOnline(client){
	if (void 0 !== client.redT.users[client.UID]) {
		client.redT.users[client.UID].push(client);
	}else{
		client.redT.users[client.UID] = [client];
	}
}

// function onData(client, data) {
// 	if (!!data) {
// 		if (!!data.doi_pass) {
// 			changePassword(client, data.doi_pass);
// 		}
// 		if (!!data.history) {
// 			onHistory(client, data.history);
// 		}
// 		if (!!data.ket_sat) {
// 			ket_sat(client, data.ket_sat);
// 		}
// 		if (!!data.updateCoint) {
// 			updateCoint(client);
// 		}
// 		if (!!data.getLevel) {
// 			getLevel(client);
// 		}

// 		if (!!data.nhanthuong) {
// 			nhanthuong(client,data);
// 		}
// 		if (!!data.security) {
// 			security(client, data.security);
// 		}

// 		if(!!data.updateAvatar)
// 		{
// 			updateAvatar(client, data.updateAvatar);
// 		}
// 		if(!!data.delete_forever)
// 		{
// 			deleteForever(client, data.delete_forever);
// 		}
// 		if(!!data.rewardVideo)
// 		{
// 			rewardVideo(client, data.rewardVideo);
// 		}
// 		if(!!data.refundVideoPoker)
// 		{
// 			refundVideoPoker(client, data.amount, data.notice);
// 		}
// 	}
// }

module.exports = {
	// authenticationout:authenticationout,
	first:       first,
	// signName:    signName,
	// onData:      onData,
	// next_scene:  next_scene,
	// updateCoint: updateCoint,
	// getLevel:    getLevel,
	// checkActivePhone: checkActivePhone,
	// deleteForever: deleteForever,
	// rewardVideo: rewardVideo,
	// refundVideoPoker: refundVideoPoker,
};