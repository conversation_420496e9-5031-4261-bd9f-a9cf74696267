var UserInfo = require('../Models/UserInfo');
var Counter = require('../Models/Counter')

// Game User
var Roulette_user = require('../Models/Sicbo_user');

var Helper      = require('../Helpers/Helpers.js');
var lang		= require ('../config/languages');

var UserSetup = function(client, name, isStore = false, standardpass = false, standardlogin = '', store = 'ios', version = '', social = false, facebook = false, apple = false, callback){
	// Set up language regarding IP Country
	var user_lang = (!!client.lang) ? client.lang : 'EN';	
	var lang_used = Helper.Language(user_lang, lang);					

	// Start money for User by registration
	var red = 0;

	UserInfo.findOne({'name': name}, function(error, user){
		if (!!error) console.log(error);

		if (!!user) {
			client.red({notice:{load: 0, title: lang_used.title.inform, text: lang_used.text.registration_signame_exist}});
		} else {
			Counter.findOneAndUpdate(
				{"key": 'UID' },
				{$inc:{sequence_value:1}}
				).exec(function (err, lastID) {
				if (!!err) console.log(err);

				let newID = lastID.sequence_value + 1;
				var avatar = Helper.getRandomInt(0, 6) +'';
				name = name.replace(/GUEST_\d{13}/g, 'GUEST_' + newID);


				var maxRefundLevel = 0;
				UserInfo.create({
					'lastLogin':Date.now(),'id':client.UID, 'name':name, 'red': red, 'joinedOn':new Date(), 'isStore': isStore, 
					'store': store, 'version': version, 'facebook': facebook, 'apple': apple,
					UID: newID, 
					lang: user_lang.toLowerCase(), country: client.lang.toLowerCase(), avatar: avatar, refundLevel: maxRefundLevel}, async function(errC, user){
					if (!!errC) {
						console.log(errC);
						client.red({notice:{load: 0, title: lang_used.title.inform, text: lang_used.text.registration_signame_exist}});
					}else{
						user = user._doc;
						user.level   = 1;
						user.vipNext = 100;
						user.vipHT   = 0;
						user.phone   = '';
						user.showall = 0;
						user.redPlay = 0;
						delete user._id;
						delete user.redWin;
						delete user.redLost;
						delete user.thuong;
						delete user.vip;
						delete user.hu;
						delete user.refundLevel;

						let systemLevel = Helper.getSystemLevel(user.redPlay);
						user.systemLevel = systemLevel.level;

						if (!!client && !!client.redT)
							addToListOnline(client);
						
							if(client == null)
							{
								console.log("Client null after get show all");
								return;
							}
							console.warn('Facebook: ' + user.facebook + ' ' + 'Apple: ' + user.apple);
							// var data = {
							// 	Authorized: true,
							// 	user: user,
							// };
							
							// Inform to User after Login
							
							// receivedRewardTime for Client
							user.receivedRewardTime = 3;

							
							client.profile = {name: user.name};

							Roulette_user.create({'uid': client.UID});	
							if (callback) callback();
							// client.red(data);
						
					}
				});	
					
			});
		}
	})
};

function addToListOnline(client){
	if (void 0 !== client.redT.users[client.UID]) {
		client.redT.users[client.UID].push(client);
	}else{
		client.redT.users[client.UID] = [client];
	}
}

let first = function(client){
	// Set up language regarding IP Country	
	var lang_used = Helper.Language(client.redT.country, lang);

	let d = new Date();
	let hour = (d.getHours()) % 24;
	
	console.warn(new Date() + ' Local Time: ' + hour);

	UserInfo.findOne({id: client.UID}, 'name lastVip redPlay red ketSat UID cmt email security joinedOn iapUser noAdsUser isStore store version lang paid_money cashout_money iap_money avatar trusted giftVipStatus id unlock_join_private_room unlock_private_room facebook apple daily_bonus_perio1 daily_bonus_perio2 daily_bonus_connect', function(err, user) {
		if (!!err) console.log(err);

		if (!!user) {
			user = user._doc;
			var level = _getLevel(user);
			var total_paid = parseInt(user.paid_money)+parseInt(user.iap_money) * level_vip.iap_percent;
			
			let vipHT = total_paid; // Current VIP Points
			// Current VIP Level
			let vipLevel = level.vipLevel;
			let vipPre   = level.vipPre;   // current VIP Points
			let vipNext  = level.vipNext;; // next VIP Points

			user.level   = vipLevel;
			user.vipNext = vipNext-vipPre;
			user.vipHT   = vipHT-vipPre;
			user.showall = 0;
			user.language = user.lang ? user.lang : client.redT.country.toLowerCase();

			// System Level
			let systemLevel = Helper.getSystemLevel(user.redPlay);
			user.systemLevel = systemLevel.level;

			delete user._id;
			delete user.lastVip;
			delete user.id;

			if (!Helper.isEmpty(user.email)) {
				user.email = Helper.cutEmail(user.email);
			}

			client.profile = {name: user.name};


			// Daily Bonus
			if (!user.daily_bonus_perio1 && (hour >= dailybonus.perio1.from && hour <= dailybonus.perio1.to) 
				|| !user.daily_bonus_perio2 && (hour >= dailybonus.perio2.from && hour <= dailybonus.perio2.to)) {
				user.red +=  parseInt(dailybonus.bonus);
				let daily_bonus_perio1 = (hour >= dailybonus.perio1.from && hour <= dailybonus.perio1.to) ? true : false;
				let daily_bonus_perio2 = (hour >= dailybonus.perio2.from && hour <= dailybonus.perio2.to) ? true : false;

				console.warn(user.red);
				UserInfo.findOneAndUpdate({id: client.UID}, {$set: {red: user.red, daily_bonus_perio1: daily_bonus_perio1, daily_bonus_perio2: daily_bonus_perio2}}).exec();
			}
			
			// Daily Facebook Connect
			if ((!!user.facebook || !!user.apple) && !user.daily_bonus_connect) {
				user.red += parseInt(dailybonus.bonus_connect);
				UserInfo.findOneAndUpdate({id: client.UID}, {$set: {red: user.red, daily_bonus_connect: true}}).exec();
			}

			Phone.findOne({uid:client.UID}, {}, async function(err2, dataP){
				if (!!err2) console.log(err2);

				user.phone = dataP ? Helper.cutPhone(dataP.region+dataP.phone) : '';
			
				const showall = await showallHelper.set_default_showall(dataP, user.paid_money, client.ip, 
					client.country, user.cashout_money, user.trusted, user.store, user.version, client.carrierName);
					user.showall = showall;
					user.showall_giftcode = showall;
					console.log(new Date() + ' Country: ' + client.country + ' Carrier: ' + client.carrierName + ' with showall:' + user.showall, user.showall_giftcode);					
					console.warn('Facebook: ' + user.facebook + ' ' + 'Apple: ' + user.apple);
					let data = {
						Authorized: true,
						user:       user
					};
					if (user.showall_giftcode > 0 && user.phone == '') 
					{
						if(client == null) return;
						var url = await secutiryHelper.generateTelegramOtpUrl(client.UID);
						if(url !== null)
						{
							data.notice = {
								title: lang_used.title.inform,
								text: lang_used.text.inform_after_login,
								action:{
									text: lang_used.button.activate,
									type: 'url',
									url: url,
									action: "OpenOTP"
								}, 
							}
						}
						else console.log('url null');
					}

					// receivedRewardTime for Client
					user.receivedRewardTime = (!!rewardBundleConfig.receivedRewardTime) ? rewardBundleConfig.receivedRewardTime : 3;

					if(client == null) return;
					Message.countDocuments({uid:client.UID, read: false, type:0}).exec(function(errMess, countMess){
						data.message = {news:countMess};					
						client.red(data);					
						GameState(client);
					});
				
			});
		}else{
			client.red({Authorized: false});
		}
	});
};

module.exports = {
	UserSetup:    UserSetup,
	first: first,
};
