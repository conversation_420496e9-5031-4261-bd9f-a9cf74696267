var Chat 	= require('../Models/Sicbo_chat');
var Helper	= require('../Helpers/Helpers');
var lang	= require('../config/languages');
var validator   = require('validator');

module.exports = function(client, str){
	// Set Language
	var lang_used = Helper.Language(client.lang, lang);

	if (!!str) {
		Helper.logFireBaseEvent(client, 'sicbo_chat', {chat: 1});

		// Phone.findOne({uid:client.UID}, function(err, phone) {
		// 	if (!!err) console.error(new Date() + ' Sicbo Chat ' + err);
			
		// 	if (!phone) {
		// 		var info = { sicbo: { chat: { message: { user: lang_used.title.system, value: lang_used.text.chat_function_not_active } } } };
                // 		client.red(info);
		// 	} else {
				if (!validator.isLength(str, { min: 1, max: 250 })) {
					client.red({ sicbo: { err: lang_used.text.chat_min_chars } });
				} else {
					str = validator.trim(str);
					if (!validator.isLength(str, { min: 1, max: 250 })) {
						client.red({ sicbo: { err: lang_used.text.chat_min_chars } });
					} else {
						Chat.findOne({game: 'sicbo'}, 'uid value', { sort: { '_id': -1 } }, function(err, post) {
							if (!post || post.uid != client.UID || (post.uid == client.UID && post.value != str)) {
								Chat.create({ 'uid': client.UID, 'name': client.profile.name, 'value': str, 'game': 'sicbo' });
								var content = { sicbo: { chat: { message: { user: client.profile.name, value: str } } } };
								Promise.all(Object.values(client.redT.users).map(function(users) {
									Promise.all(users.map(function(member) {
										if (member != client) {
											member.red(content);
										}
									}));
								}));
							}
						});
					}
				}
			// }
		// });
	}
};
