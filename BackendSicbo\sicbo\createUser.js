const Helpers = require('../Helpers/Helpers');
const UserInfo = require('../Models/UserInfo');
const Users = require('../Models/Users');
const { first } = require('./User');
const UserSetup = require('./UserSetup');

module.exports = function(client, data, callback){
    
    let username = '' + data.username + '';
    let password = data.password;
    let register = !!data.register;		
    let language = 'en';

    try {
        username = username.toLowerCase();

        Users.findOne({ 'local.username': username }, function (err, user) {
            if (!!err) console.log(err);

            if (!!user) {
                if (!!user.local.ban_login) {
                    // callback({ title: lang_used.title.login, text: lang_used.text.account_locked }, false);
                } else if (!!user.local.is_login) {
                    if (user.validPassword(password)) {

                        client.UID = user._id.toString();
                        Users.updateOne({ 'local.username': username }, { $set: { 'local.lastLogin': new Date(), 'local.is_login': true } }).exec();

                        UserInfo.findOneAndUpdate({ 'id': user._id.toString() }, { $set: { 'lastLogin': new Date() } }, function (errUInfo, uInfo) {
                            if (!!errUInfo) console.warn(errUInfo);

                            if (!!uInfo && !!uInfo.lang) {
                                // Update selected Language for User
                                client.lang = language = uInfo.lang;
                            }
                        });
                        first(client, callback, data.at);
                        if (client.redT.users[client.UID] != null && client.redT.users[client.UID].length >= 1) {
                            for (var i = 0; i < client.redT.users[client.UID].length; i++) {
                                disconnectUserByOther(client.redT.users[client.UID][i], client.redT, lang_used);
                            }
                        }
                    }
                } else if (user.validPassword(password)) {
                    client.UID = user._id.toString();
                    Users.updateOne({ 'local.username': username }, { $set: { 'local.lastLogin': new Date(), 'local.is_login': true } }).exec();

                    UserInfo.findOneAndUpdate({ 'id': user._id.toString() }, { $set: { 'lastLogin': new Date()} }, function (errUInfo, uInfo) {
                        if (!!errUInfo) console.warn(errUInfo);

                        if (!!uInfo && !!uInfo.lang) {
                            // Update selected Language for User
                            client.lang = language = uInfo.lang;
                        }
                    });
                    first(client, callback, data.at);
                } else {
                    // callback({ title: lang_used.title.login, text: lang_used.text.password_incorrect }, false);
                }
            } else {
                Users.findOne({ 'local.username': username }).exec(function (err, check) {
                    if (!!err) console.log(err);

                    if (!!check) {
                        client.c_captcha('signUp');
                        // callback({ title: lang_used.title.registration, text: lang_used.text.account_name_exists }, false);
                    } else {
                        Users.create({
                            'local.username': username,
                            'local.password': Helpers.generateHash(password),
                            'local.regDate': new Date(),
                            'local.lastLogin': new Date(),
                            'local.is_login': true,
                        }, function (err, user) {
                            if (!!err) console.log(err);

                            if (!!user) {
                                client.UID = user._id.toString();
                                UserSetup.UserSetup(client, username, true, true, '','',1,false,false,false,()=>{
                                    first(client, callback, data.at);
                                });
                                // callback(false, true);
                            } else {
                                client.c_captcha('signUp');
                                // callback({ title: lang_used.title.registration, text: lang_used.text.account_name_exists }, false);
                            }
                        });
                    }
                });
            }
        });
    }catch (error) {
        console.log(new Date() + " Begin error");
        console.error(error);
    }
}
function addToListOnline(client){
    if (void 0 !== client.redT.users[client.UID]) {
        client.redT.users[client.UID].push(client);
    }else{
        client.redT.users[client.UID] = [client];
    }
}
