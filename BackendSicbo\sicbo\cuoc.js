
var Sicbo_bet = require('../Models/Sicbo/Sicbo_bet');
var UserInfo    = require('../Models/UserInfo');
var lang		= require('../config/languages');
var helpers		= require('../Helpers/Helpers');
var gamestimes	= require('../config/gamestimes');
var Sicbo_user_bet = require('../Models/Sicbo_user_bet');

module.exports = function(client, data){
	if (!!data && !!data.cuoc) {
		// Set up language regarding IP Country
		var lang_used = helpers.Language(client.lang, lang);
		var cuoc    = parseInt(data.cuoc);
		var linhVat = data.select;

		if (client.redT.Sicbo_time < gamestimes.sicbo.wait_time || client.redT.Sicbo_time > gamestimes.sicbo.play_time) {
			client.red({sicbo:{notice: lang_used.text.taixiu_next_session}});
			return;
		}

		if (cuoc < 100) {
			client.red({sicbo:{notice: lang_used.title.fail}});
		}else{
			UserInfo.findOne({id: client.UID}, 'red hanRut liveuser redPlay', function(err, user){
				if (!!err) console.log(err);

				if (!user || user.red <= 0 || user.red < cuoc ) {
					client.red({sicbo:{notice: lang_used.text.account_not_enough + lang_used.currency + lang_used.text.account_to_bet}});
				} else {
					// Log Firebase
					helpers.logFireBaseEvent(client, 'sicbo_cuoc', {cuoc: cuoc});

					// Deduction user's money
					let deduction = cuoc * (-1);
					let hanRut = (user.hanRut - cuoc > 0) ? (user.hanRut - cuoc) : 0;
					UserInfo.findOneAndUpdate({id:client.UID}, {$inc: {red: deduction, redPlay: cuoc},$set: {hanRut:hanRut}}, {new: true}).exec(function (err, user) {
						if (!!err) console.log(err);

						let userRed = parseInt(user.red) > 0 ? parseInt(user.red) : 0;
						if (!!user && user.red >= 0) {
							deduction = 0;

							Promise.all(client.redT.sicbo.viewgame.map(function(uOld){
								if (uOld.uid == client.UID) {
									uOld.red = user.red;
								}
							}));
							
							var dataRed = [
								"dai", "xiu", "even", "odd",					
								"double1", "double2", "double3", "double4", "double5", "double6",
								"triple1", "triple2", "triple3", "triple4", "triple5", "triple6",
								"point04", "point05", "point06", "point07", "point08", "point09", 
								"point10", "point11", "point12", "point13", "point14", "point15", "point16", "point17",
								"one2", "one3", "one4", "one5", "one6", "two3", "two4", "two5", "two6", "three4", "three5", "three6", "four5", "four6", "five6",
								"one", "two", "three", "four", "five", "six"
							];
							var tab = dataRed;

							Sicbo_bet.findOne({uid: client.UID, session: client.redT.Sicbo_session, server: client.redT.server}, function(err, checkOne) {
								if (!!err) console.log(err);
								
								var io = client.redT;
								if (linhVat == "dai") {
									io.sicbo.info.dai += cuoc;
								}else if (linhVat == "xiu") {
									io.sicbo.info.xiu += cuoc;
								}else if (linhVat == "even") {
									io.sicbo.info.even += cuoc;
								}else if (linhVat == "odd") {
									io.sicbo.info.odd += cuoc;
		
								}else if (linhVat == "double1") {
									io.sicbo.info.double1 += cuoc;
								}else if (linhVat == "double2") {
									io.sicbo.info.double2 += cuoc;
								}else if (linhVat == "double3") {
									io.sicbo.info.double3 += cuoc;
								}else if (linhVat == "double4") {
									io.sicbo.info.double4 += cuoc;
								}else if (linhVat == "double5") {
									io.sicbo.info.double5 += cuoc;
								}else if (linhVat == "double6") {
									io.sicbo.info.double6 += cuoc;
		
								}else if (linhVat == "triple1") {
									io.sicbo.info.triple1 += cuoc;
								}else if (linhVat == "triple2") {
									io.sicbo.info.triple2 += cuoc;
								}else if (linhVat == "triple3") {
									io.sicbo.info.triple3 += cuoc;
								}else if (linhVat == "triple4") {
									io.sicbo.info.triple4 += cuoc;
								}else if (linhVat == "triple5") {
									io.sicbo.info.triple5 += cuoc;
								}else if (linhVat == "triple6") {
									io.sicbo.info.triple6 += cuoc;
									
								}else if (linhVat == "point04") {
									io.sicbo.info.point04 += cuoc;
								}else if (linhVat == "point05") {
									io.sicbo.info.point05 += cuoc;
								}else if (linhVat == "point06") {
									io.sicbo.info.point06 += cuoc;
								}else if (linhVat == "point07") {
									io.sicbo.info.point07 += cuoc;
								}else if (linhVat == "point08") {
									io.sicbo.info.point08 += cuoc;
								}else if (linhVat == "point09") {
									io.sicbo.info.point09 += cuoc;
								}else if (linhVat == "point10") {
									io.sicbo.info.point10 += cuoc;
								}else if (linhVat == "point11") {
									io.sicbo.info.point11 += cuoc;
								}else if (linhVat == "point12") {
									io.sicbo.info.point12 += cuoc;
								}else if (linhVat == "point13") {
									io.sicbo.info.point13 += cuoc;
								}else if (linhVat == "point14") {
									io.sicbo.info.point14 += cuoc;
								}else if (linhVat == "point15") {
									io.sicbo.info.point15 += cuoc;
								}else if (linhVat == "point16") {
									io.sicbo.info.point16 += cuoc;
								}else if (linhVat == "point17") {
									io.sicbo.info.point17 += cuoc;
		
								//one2, one3, one4, one5, one6, two3, two4, two5, two6, three4, three5, three6, four5, four6, five6,
								}else if (linhVat == "one2") {
									io.sicbo.info.one2 += cuoc;
								}else if (linhVat == "one3") {
									io.sicbo.info.one3 += cuoc;
								}else if (linhVat == "one4") {
									io.sicbo.info.one4 += cuoc;
								}else if (linhVat == "one5") {
									io.sicbo.info.one5 += cuoc;
								}else if (linhVat == "one6") {
									io.sicbo.info.one6 += cuoc;
								}else if (linhVat == "two3") {
									io.sicbo.info.two3 += cuoc;
								}else if (linhVat == "two4") {
									io.sicbo.info.two4 += cuoc;
								}else if (linhVat == "two5") {
									io.sicbo.info.two5 += cuoc;							
								}else if (linhVat == "two6") {
									io.sicbo.info.two6 += cuoc;
								}else if (linhVat == "three4") {
									io.sicbo.info.three4 += cuoc;
								}else if (linhVat == "three5") {
									io.sicbo.info.three5 += cuoc;
								}else if (linhVat == "three6") {
									io.sicbo.info.three6 += cuoc;
								}else if (linhVat == "four5") {
									io.sicbo.info.four5 += cuoc;
								}else if (linhVat == "four6") {
									io.sicbo.info.four6 += cuoc;
								}else if (linhVat == "five6") {
									io.sicbo.info.five6 += cuoc;
								//one, two, three, four, five, six	
								}else if (linhVat == "one") {
									io.sicbo.info.one += cuoc;
								}else if (linhVat == "two") {
									io.sicbo.info.two += cuoc;
								}else if (linhVat == "three") {
									io.sicbo.info.three += cuoc;
								}else if (linhVat == "four") {
									io.sicbo.info.four += cuoc;
								}else if (linhVat == "five") {
									io.sicbo.info.five += cuoc;
								}else if (linhVat == "six") {
									io.sicbo.info.six += cuoc;
								}
		
								var dataCuoc = {};
								dataCuoc[linhVat] = cuoc;

								// Calculation of old Level & new Level before/after Spin
								let newLevel = helpers.getSystemLevel(user.redPlay);
								let oldLevel = helpers.getSystemLevel(user.redPlay*1 - cuoc);

								if (!!checkOne){
									var userMe = null;
									Promise.all(io.sicbo.ingame.map(function(uOld){
										if (uOld.uid == client.UID) {
											if(!uOld.cuoc) uOld.cuoc = {};
		
											if(uOld.cuoc[linhVat] == null) uOld.cuoc[linhVat] = 0;
											uOld.cuoc[linhVat] += cuoc;
											userMe =uOld;
											dataCuoc[linhVat] = cuoc;
										}
									}));
									Sicbo_bet.findOneAndUpdate({uid: client.UID, session: client.redT.Sicbo_session, server: client.redT.server}, {$inc:dataCuoc}, function (err, cat){
										if(!!err) console.log(err);
		
										if(!!cat)
										Promise.all(tab.map(function(o, i){
											data[o] = cat[i] + (i == linhVat ? cuoc : 0);
											return (data[o] = cat[i] + (i == linhVat ? cuoc : 0));
										}))
										.then(result => {	
											var dataT = {sicbo:{data: dataCuoc,dataMe:userMe}, user:{red: userRed, oldLevel: oldLevel.level, newLevel: newLevel.level, bonus: newLevel.bonus}};
											Promise.all(client.redT.users[client.UID].map(function(obj){
												obj.red(dataT);
											}));
										});
									});
		
									
								}else{
									var addList = {
										uid:client.UID, name:client.profile.name, 
										cuoc:{
											dai: 0, xiu: 0, even: 0, odd: 0,
											double1: 0, double2: 0, double3: 0, double4: 0, double5: 0, double6: 0,
											triple1: 0, triple2: 0, triple3: 0, triple4: 0, triple5: 0, triple6: 0,
											point04: 0, point05: 0, point06: 0, point07: 0, point08: 0, point09: 0, 
											point10: 0, point11: 0, point12: 0, point13: 0, point14: 0, point15: 0, point16: 0, point17: 0,
											one2: 0, one3: 0, one4: 0, one5: 0, one6: 0, two3: 0, two4: 0, two5: 0, two6: 0, three4: 0, 
											three5: 0, three6: 0, four5: 0, four6: 0, five6: 0,
											one: 0, two: 0, three: 0, four: 0, five: 0, six: 0
										}
									};
								
									io.sicbo.ingame.unshift(addList);
									var create = {uid: client.UID, name: client.profile.name, session: client.redT.Sicbo_session, server: client.redT.server, bot: user.liveuser, time: new Date()};
									create[linhVat] = cuoc;	
									var userMe = null;
									Promise.all(io.sicbo.ingame.map(function(uOld){
										if (uOld.uid == client.UID) {
											if(!uOld.cuoc) uOld.cuoc = {};
											if(uOld.cuoc[linhVat] == null) uOld.cuoc[linhVat] = 0;
											uOld.cuoc[linhVat] += cuoc;
											userMe = uOld;
										}
									}));
									Sicbo_bet.create(create);
									var dataT = {sicbo:{data: dataCuoc,dataMe:userMe}, user:{red: userRed, oldLevel: oldLevel.level, newLevel: newLevel.level, bonus: newLevel.bonus}};
									if (!!client.redT.users && !!client.redT.users[client.UID])
									Promise.all(client.redT.users[client.UID].map(function(obj){
										obj.red(dataT);
									}));
		
									
								}
							});

							Sicbo_user_bet.create({uid: client.UID, name: data.name, moneyBet: cuoc, gate: linhVat});
						}
					});
				}
			});
		}
	}
};
