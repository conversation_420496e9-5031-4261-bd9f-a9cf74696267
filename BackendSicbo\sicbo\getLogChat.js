var Chat  = require('../Models/Sicbo_chat');
var helpers		= require('../Helpers/Helpers');

module.exports = function(client){
	let lang = helpers.LanguageSupport(client.lang.toLowerCase());

	Chat.find({game: 'sicbo', lang: {$in: [lang, '']}},'name value', {sort:{'_id':-1}, limit: 20}, function(err, post) {
		if (!!err) console.log(err);

		if (!!post && !!post.length){
			Promise.all(post.map(function(obj)
				{return {'user':obj.name, 'value':obj.value};
			}))
			.then(function(arrayOfResults) {
				client.red({sicbo:{chat:{logs: arrayOfResults.reverse()}}});
			});
		}
	});
};
