
module.exports = function(client){
	var sicbo = client.redT.game.sicbo;
	if (sicbo.clients[client.UID] === client) {
		delete sicbo.clients[client.UID];
		for(var i=0;i<client.redT.sicbo.viewgame.length;i++){
			if (client.redT.sicbo.viewgame[i].uid == client.UID) {
				client.redT.sicbo.viewgame.splice(i,1);
				break;
			}
		}
		
		var clients = Object.keys(sicbo.clients).length;
		Object.values(sicbo.clients).forEach(function(users){
			if (client !== users) {
				users.red({sicbo:{ingame:{client:clients}}});
			}
		});
	}

	// sicbo.clients = {} --> Close Game Sicbo
	sicbo = null;
	client = null;
};
