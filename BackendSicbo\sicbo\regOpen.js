var Sicbo_session 	= require('../Models/Sicbo/Sicbo_session');
var Sicbo_history  	= require('../Models/Sicbo/Sicbo_history');
var Sicbo_init		= require('../Cron/sicbo');
var gamestimes	= require('../config/gamestimes');
var gameFunds	= require('../Models/GameFunds');
const UserInfo = require('../Models/UserInfo');
const Helpers = require('../Helpers/Helpers');

module.exports = function(client){
	if(!client.redT.sicbo) {
		Sicbo_init(client.redT);
	}
	client.gameEvent = {};
	client.gameEvent.viewSicbo = true;

	UserInfo.findOne({id: client.UID}, 'red avatar', function(err, user){
		if (!!err) console.log(err);

		if (!client.redT.sicbo.viewgame) client.redT.sicbo.viewgame = [];
		if (!user) {
			user = {};
			user.red = 0;
		}
		Helpers.addElementInArrayNotDuplicateByUID(client.redT.sicbo.viewgame,client.UID,{uid:client.UID, name:client.profile.name,red:user.red, avatar: user.avatar});
	});

	// Start Sicbo for the first time
	// if(!client.redT.sicbo) {
	// 	Sicbo_init(client.redT);
	// }
	var LinhVat = {};
	var data    = JSON.parse(JSON.stringify(client.redT.sicbo.info));
	var dataMeRed = [
		"dai", "xiu", "even", "odd",					
		"double1", "double2", "double3", "double4", "double5", "double6",
		"triple1", "triple2", "triple3", "triple4", "triple5", "triple6",
		"point04", "point05", "point06", "point07", "point08", "point09", 
		"point10", "point11", "point12", "point13", "point14", "point15", "point16", "point17",
		"one2", "one3", "one4", "one5", "one6", "two3", "two4", "two5", "two6", "three4", "three5", "three6", "four5", "four6", "five6",
		"one", "two", "three", "four", "five", "six"
	];
	// List of players
	var active1 = Promise.all(client.redT.sicbo.ingame.map(function(user){
		if (user.uid == client.UID) {
			if (!!user.red) {
				return Promise.all(dataMeRed.map(function(tab, i){
					return (data[tab] = user[i]);
				}));
			}
		}
	}));
	// Histories & Results
	var active2 = new Promise((a, b)=>{
		Sicbo_history.findOne({server: client.redT.server}, {}, function(err, temp){
			if (!!err) console.warn(err);

			Promise.all(dataMeRed.map(function(tab, i){
				if (!!i && !!temp && !!temp[i]) return (LinhVat[i] = temp[i]);
			}))
			.then(Results => {
				a(Results);
			});
		});
	});
	var active3 = new Promise((resolve, reject) => {
		Sicbo_session.find({server: client.redT.server}, {}, {sort:{'_id':-1}, limit: 10}, function(err, post) {
			if (!!err) console.warn(err);

			if (!!post)
			Promise.all(post.map(function(obj){
				return [obj.dice1, obj.dice2, obj.dice3];
			}))
			.then(function(arrayOfResults) {
				resolve(arrayOfResults);
			});
		});
	});
	
	Promise.all([active1, active2, active3])
	.then(result => {
		//Put user into ViewList
		var userMe = null;
		Promise.all(client.redT.sicbo.ingame.map(function(uOld){
			if (uOld.uid == client.UID) {
				userMe = uOld;
			}
		}));
		client.red({sicbo:{dataMe:userMe,regOpen: true,gamestimes:gamestimes.sicbo, time_remain: client.redT.Sicbo_time, session: client.redT.Sicbo_session, server: client.redT.server,viewUsers:client.redT.sicbo.viewgame, users: client.redT.sicbo.ingame,data: data, logLV: LinhVat, logs: result[2]}});
	});

	gameFunds.findOne({game:'sicbo'},{}, function(err, funds){
		if(err || funds == null || funds.mode == 0)
		{
			if(funds == null)
			{
				funds = {game:'sicbo', funds: 1000, funds_percent: 0.8, mode: 1};
				gameFunds.create(funds, function(err, funds) {
					
				});
			}
		}
	})
};
