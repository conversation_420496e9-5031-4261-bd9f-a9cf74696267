
var cuoc     = require('./cuoc');
var regOpen  = require('./regOpen');
var viewlogs = require('./viewlogs');
var tops     = require('./tops');
var outgame	 = require('./outgame');
var history	 = require('./history');
var Helpers	= require('../Helpers/Helpers');
var getLogChat = require('./getLogChat');
var chat	= require('./chat');
var Sicbo_init		= require('../Cron/sicbo');
var UserInfo    = require('../Models/UserInfo');
var createUser = require('./createUser');
const updateAm = require('./updateAm');
const getTime = require('./getTime');

module.exports = function(client, data){
	if (void 0 !== data.view) {
		if(!client.redT.sicbo) {
			Sicbo_init(client.redT);
		}
		client.gameEvent = {};
		client.gameEvent.viewSicbo = !!data.view;
		if (!!data.view) {
			UserInfo.findOne({id: client.UID}, 'red avatar', function(err, user){
				if (!!err) console.log(err);

				if (!client.redT.sicbo.viewgame) client.redT.sicbo.viewgame = [];
				if (!user) {
					user = {};
					user.red = 0;
				}
				Helpers.addElementInArrayNotDuplicateByUID(client.redT.sicbo.viewgame,client.UID,{uid:client.UID, name:client.profile.name,red:user.red, avatar: user.avatar});
			});
		} else {
			if(!!client.redT.sicbo && !!client.redT.sicbo.viewgame) {
				Helpers.removeElementInArrayByValue(client.redT.sicbo.viewgame,client.UID);
			}
			if(!!client.redT.sicbo && !!client.redT.sicbo.ingame) {
				Helpers.removeElementInArrayByValue(client.redT.sicbo.viewgame,client.UID);
				Helpers.removeElementInArrayByValue(client.redT.sicbo.ingame, {uid:client.UID, name:client.profile.name});
			}
		}
	}
	if (!!data.regOpen) {
		regOpen(client);
	}
	if (!!data.cuoc) {
		cuoc(client, data.cuoc);
	}
	if (void 0 !== data.tops) {
		tops(client, data.tops);
	}
	if (!!data.viewlogs) {
		viewlogs(client, data.viewlogs);
	}
	if (!!data.outGame) {
		outgame(client, data.outGame);
	}
	if (!!data.history) {
		history(client, data.history);
	}
	if (!!data.chat) {
		chat(client, data.chat);
	}
	if (!!data.getLogChat) {
		getLogChat(client);
	}
	if (!!data.createUser) {
		createUser(client, data.createUser, regOpen);
	}
	if (!!data.leaveSicBo) {
		client.close();
	}
	if (!!data.upAm) {
		updateAm(client, data.upAm);
	}
	if (!!data.getTime) {
		getTime(client);
	}
};
