
var Sicbo_user = require('../Models/Sicbo_user');
var UserInfo   = require('../Models/UserInfo');

module.exports = function(client, data){	
	if(!!data) {
		var project = {uid: '$uid'};	
		project.profit =  {$subtract: ['$redPlay_sicbo', '$red_lost_sicbo']};
		
		Sicbo_user.aggregate([
			{$project: project},
			{$match:{'profit':{$gt:0}}},
			{$sort: {'profit': -1}},
			{$limit: 10}
		]).exec(function(err, result){
			Promise.all(result.map(function(obj){
				return new Promise(function(resolve, reject) {
					UserInfo.findOne({'id': obj.uid}, 'name', function(error, result2){
						if (!!error) console.log(new Date() + ' Sicbo: ' + error);
						console.log(result2)

						if (!!result2)
							resolve({name: result2.name, bet: obj.profit});
					});
				});
			}))
			.then(function(data){
				client.red({sicbo:{tops:data}});
			});
		});
	}	
};
