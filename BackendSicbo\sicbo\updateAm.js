var Chat  = require('../Models/Sicbo_chat');
var helpers		= require('../Helpers/Helpers');
const languages = require('../config/languages');
const UserInfo = require('../Models/UserInfo');

module.exports = function(client, data){
    var lang_used = helpers.Language(client.lang, languages);

    let dataAm = data.am;
    if (data.type == 0){

    }else if (data.type == 1){
        dataAm *= -1;
    }

    UserInfo.findOne({id:client.UID}, 'red', function (err, user) {
        if (!!err) console.log(err);

        if (!!user){
            if (data.type == 1 && user.red < data.am){
                client.red({user:{up: true, notice: "Bạn không đủ số dư"}});
                return
            }
            if (data.am > 1000000000){
                client.red({user:{up: true, notice: "Số dư của bạn hiện tại quá nhiề<PERSON>"}});
                return
            }
            if (client.at == data.at){
                UserInfo.findOneAndUpdate({id:client.UID}, {$inc: {red: dataAm}}, {new: true}).exec(function (err, user) {
                    let text = '';
                    if (data.type == 0){
                        text = "Bạn đã nạp tiền thành công";
                    }else if (data.type == 1){
                        text = "Bạn đã rút tiền thành công";
                    }
                    client.red({user:{red: user.red, up: true, notice: text}});
                })
            }
        }
    })
};
