
var Sicbo_bet  = require('../Models/Sicbo/Sicbo_bet');
var Sicbo_session = require('../Models/Sicbo/Sicbo_session');

module.exports = function(client, data){
	if (!!data && !!data.page) {
		var page = data.page>>0; // Page
		
		if (page < 1) {
			client.red({notice:{text:'Data on Error.', title:'UNSUCCESSFUL'}});
		} else {
			var kmess = 8;
			Sicbo_bet.countDocuments({uid:client.UID, thanhtoan:true}).exec(function(err, total){
				if (!!err) console.log(err);

				Sicbo_bet.find({uid:client.UID, thanhtoan:true}, {}, {sort:{'_id':-1}, skip:(page-1)*kmess, limit:kmess}, function(err1, result) {
					if (!!err1) console.log(err1);

					if (!!result && !!result.length) {
						Promise.all(result.map(function(obj){
							obj = obj._doc;
							var getSession = Sicbo_session.findOne({id:obj.session, server:obj.server}).exec();
							return Promise.all([getSession]).then(values => {
								obj.kq = [values[0].dice1, values[0].dice2, values[0].dice3];
								delete obj.__v;
								delete obj._id;
								delete obj.thanhtoan;
								delete obj.uid;
								delete obj.red;
								return obj;
							});
						}))
						.then(function(arrayOfResults) {
							client.red({sicbo:{viewlogs:{data:arrayOfResults, page:page, kmess:kmess, total:total}}});
						});
					}else{
						client.red({sicbo:{viewlogs:{data:[], page:page, kmess:kmess, total:0}}});
					}
				});
			});
		}
	}
};
