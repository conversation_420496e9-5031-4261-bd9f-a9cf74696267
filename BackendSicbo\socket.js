
let first  = require('./Controllers/User.js').first;
let onPost = require('./Controllers/onPost.js');

let auth = function(client) {
	client.gameEvent = {};
	client.scene = 'home';
	first(client);
	client = null;
};

let signMethod = function(client) {
	client.TTClear = function(){
		if (!!this.caothap) {
			clearTimeout(this.caothap.time);
			this.caothap.time = null;
			this.caothap = null;
		}

		if (!!this.fish) {
			this.fish.outGame();
		}
		
		this.TTClear = null;
	};
	client = null;
};

module.exports = {
	auth:       auth,
	message:    onPost,
	signMethod: signMethod,
};
