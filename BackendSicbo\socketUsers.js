
let validator = require('validator');
let User = require('./Models/Users');
// let UserInfo = require('./app/Models/UserInfo');
let helpers = require('./Helpers/Helpers');
// let Phone = require('./app/Models/Phone');
// let OTPOnly = require('./app/Controllers/user/OTPOnly');
// let OTP = require('./app/Models/OTP');
// let lang = require('./config/languages');
// let UserSetup = require('./app/Controllers/UserSetup');
// var social = require('./config/social');

// var frontendlanguages = require('./config/frontendlanguages');
let createBot = require('./Helpers/createBot');

// var Blacklist = require('./app/Models/Blacklist');
let first  = require('./sicbo/User').first;
let events = require('events');
let sicbo = require('./sicbo/sicboMessage');
// Authenticate!
// let authenticate = function (client, redT, data, callback, user_lang) {
// 	// Set up language regarding IP Country
// 	data.lang = client.lang = user_lang;
// 	var lang_used = helpers.Language(client.lang, lang);
// 	var isStore = false;
// 	var language = (!!client.lang) ? client.lang.toLowerCase() : 'en';
// 	var store = '';
// 	var version = '';

// 	// Only Login with Username Password, Facebook & Apple SignIn
// 	if (!!data && !!data.username && !!data.password) {
// 		let username = '' + data.username + '';
// 		let password = data.password;
// 		let register = !!data.register;		
// 		let az09 = new RegExp('^[a-zA-Z0-9]+$');
// 		let testName = az09.test(username);
// 		isStore = (!!data.isStore) ? data.isStore : true;
// 		if (!!data.lang) language = data.lang.toLowerCase();

// 		if (!validator.isLength(username, { min: 3, max: 32 })) {
// 			register && client.c_captcha('signUp');
// 			callback({ title: register ? lang_used.title.registration : lang_used.title.login, text: lang_used.text.registration_account_text }, false);
// 		} else if (!validator.isLength(password, { min: 6, max: 32 })) {
// 			register && client.c_captcha('signUp');
// 			callback({ title: register ? lang_used.title.registration : lang_used.title.login, text: lang_used.text.registration_pass_text }, false);
// 		} else if (!testName) {
// 			register && client.c_captcha('signUp');
// 			callback({ title: register ? lang_used.title.registration : lang_used.title.login, text: lang_used.text.registration_login_name_text }, false);
// 		} else if (username == password) {
// 			register && client.c_captcha('signUp');
// 			callback({ title: register ? lang_used.title.registration : lang_used.title.login, text: lang_used.text.registration_login_name_pass_text }, false);
// 		} else {
// 			try {
// 				username = username.toLowerCase();
// 				store = (!!data.store) ? data.store.toLowerCase() : '';
// 				version = (!!data.version) ? data.version : '55';
// 				let carrierName = (!!data.country && data.country == client.country) ? data.country : (data.store == 'android'? 'US' : client.country);
// 				// Registration
// 				if (register) {
// 					if (!data.captcha || !client.c_captcha || !validator.isLength(data.captcha, { min: 4, max: 4 })) {
// 						client.c_captcha('signUp');
// 						callback({ title: lang_used.title.registration, text: lang_used.text.captcha_not_exist }, false);
// 					} else {
// 						let checkCaptcha = new RegExp('^' + client.captcha + '$', 'i');
// 						checkCaptcha = checkCaptcha.test(data.captcha);
// 						if (checkCaptcha) {
// 							// Check Blacklist
// 							Blacklist.findOne({ ip: client.ip }, function (error, ip) {
// 								if (!!error) console.log(error);

// 								// Check Blacklist
// 								if (!!ip) {
// 									callback({ title: lang_used.title.registration, text: lang_used.text.account_locked }, false);
// 								} else {
// 									User.findOne({ 'local.username': username }).exec(function (err, check) {
// 										if (!!err) console.log(err);

// 										if (!!check) {
// 											client.c_captcha('signUp');
// 											callback({ title: lang_used.title.registration, text: lang_used.text.account_name_exists }, false);
// 										} else {
// 											User.create({
// 												'local.username': username,
// 												'local.password': helpers.generateHash(password),
// 												'local.regDate': new Date(),
// 												'local.lastLogin': new Date(),
// 												'local.is_login': true,
// 												'local.base_version': version,
// 												'local.base_store': store,
// 												'local.base_isStore': isStore
// 											}, function (err, user) {
// 												if (!!err) console.log(err);

// 												if (!!user) {
// 													client.UID = user._id.toString();
// 													client.carrierName = carrierName;
// 													callback(false, true);
// 												} else {
// 													client.c_captcha('signUp');
// 													callback({ title: lang_used.title.registration, text: lang_used.text.account_name_exists }, false);
// 												}
// 											});
// 										}
// 									});
// 								}
// 							});
// 						} else {
// 							client.c_captcha('signUp');
// 							callback({ title: lang_used.title.registration, text: lang_used.text.captcha_not_correct }, false);
// 						}
// 					}
// 				} else {
// 					// Login
// 					User.findOne({ 'local.username': username }, function (err, user) {
// 						if (!!err) console.log(err);

// 						if (!!user) {
// 							if (!!user.local.ban_login) {
// 								callback({ title: lang_used.title.login, text: lang_used.text.account_locked }, false);
// 							} else if (!!user.local.is_login) {
// 								if (user.validPassword(password)) {

// 									client.UID = user._id.toString();
// 									client.carrierName = carrierName;	
// 									User.updateOne({ 'local.username': username }, { $set: { 'local.lastLogin': new Date(), 'local.is_login': true } }).exec();

// 									UserInfo.findOneAndUpdate({ 'id': user._id.toString() }, { $set: { 'lastLogin': new Date(), 'isStore': isStore, 'store': store, 'version': version } }, function (errUInfo, uInfo) {
// 										if (!!errUInfo) console.warn(errUInfo);

// 										if (!!uInfo && !!uInfo.lang) {
// 											// Update selected Language for User
// 											client.lang = language = uInfo.lang;
// 										}
// 									});

// 									callback(false, true);

// 									if (redT.users[client.UID] != null && redT.users[client.UID].length >= 1) {
// 										for (var i = 0; i < redT.users[client.UID].length; i++) {
// 											disconnectUserByOther(redT.users[client.UID][i], redT, lang_used);
// 										}
// 									}


// 								}
// 							} else if (user.validPassword(password)) {
// 								client.UID = user._id.toString();
// 								client.carrierName = carrierName;
// 								User.updateOne({ 'local.username': username }, { $set: { 'local.lastLogin': new Date(), 'local.is_login': true } }).exec();

// 								UserInfo.findOneAndUpdate({ 'id': user._id.toString() }, { $set: { 'lastLogin': new Date(), 'isStore': isStore, 'store': store, 'version': version } }, function (errUInfo, uInfo) {
// 									if (!!errUInfo) console.warn(errUInfo);

// 									if (!!uInfo && !!uInfo.lang) {
// 										// Update selected Language for User
// 										client.lang = language = uInfo.lang;
// 									}
// 								});
// 								callback(false, true);
// 							} else {
// 								callback({ title: lang_used.title.login, text: lang_used.text.password_incorrect }, false);
// 							}
// 						} else {
// 							callback({ title: lang_used.title.login, text: lang_used.text.account_not_exist }, false);
// 						}
// 					});
// 				}
// 			} catch (error) {
// 				callback({ title: lang_used.title.inform, text: lang_used.text.registration_error }, false);
// 			}
// 		}
// 	} else if (!!data && !!data.fb && !!data.fb.accessToken && !!data.fb.userID) {
// 		var facebook_userID = (!!data.userID) ? data.userID : data.fb.userID;
// 		var facebook_token = (!!data.accessToken) ? data.accessToken : data.fb.accessToken;		
// 		isStore = !!data.fb.isStore ? data.fb.isStore : true;
// 		if (!!data.fb.lang) language = data.fb.lang.toLowerCase();
// 		store = (!!data.fb.store) ? data.fb.store.toLowerCase() : '';
// 		version = (!!data.fb.version) ? data.fb.version : '55';
// 		let carrierName = (!!data.fb.country && data.fb.country == client.country) ? data.fb.country : (data.store == 'android'? 'US' : client.country);
// 		//Facebook Login
// 		User.findOne({ 'facebook.id': facebook_userID }, function (err, fbuser) {
// 			if (!!err) console.log(err);

// 			//If exist, Login
// 			if (!!fbuser) {
// 				if (!!fbuser.local.ban_login) {
// 					callback({ title: lang_used.title.login, text: lang_used.text.account_locked }, false);
// 				} else {
// 					client.UID = fbuser._id.toString();
// 					client.carrierName = carrierName;
// 					User.updateOne({ 'facebook.id': facebook_userID }, { $set: { 'local.lastLogin': new Date(), 'local.is_login': true } }).exec();

// 					UserInfo.findOneAndUpdate({ 'id': fbuser._id.toString() }, { $set: { 'lastLogin': new Date(), 'isStore': isStore, 'store': store, 'version': version, 'facebook': true } }, function (errUInfo, uInfo) {
// 						if (!!errUInfo) console.warn(new Date() + ' Error Fb Login: ' + errUInfo);
						
// 						if (!!uInfo && !!uInfo.lang) {
// 							// Update selected Language for User
// 							client.lang = language = uInfo.lang;
// 						}
// 					});
// 					callback(false, true);

// 					if (!!fbuser.local.is_login) {
// 						if (redT.users[client.UID] != null && redT.users[client.UID].length >= 1) {
// 							for (var i = 0; i < redT.users[client.UID].length; i++) {
// 								disconnectUserByOther(redT.users[client.UID][i], redT, lang_used);
// 							}
// 						}
// 					}
// 				}
// 			} else {
// 				//Registration & Mapping new user
// 				User.create({
// 					'local.username': facebook_userID,
// 					'local.password': helpers.generateHash('123456'),
// 					'local.regDate': new Date(),
// 					'local.lastLogin': new Date(),
// 					'local.is_login': true,
// 					'local.base_version': version,
// 					'local.base_store': store,
// 					'local.base_isStore': isStore,

// 					'facebook.token': facebook_token,
// 					'facebook.id': facebook_userID

// 				}, function (err, newfbuser) {
// 					if (!!err) console.log('Create new Facebook User' + err);

// 					if (!!newfbuser) {
// 						client.UID = newfbuser._id.toString();
// 						client.carrierName = carrierName;
// 						callback(false, true);
// 					} else {
// 						callback({ title: lang_used.title.registration, text: lang_used.text.account_name_exists }, false);
// 					}
// 				});
// 			}
// 		});
// 	} else if (!!data && !!data.apple && !!data.apple.id) {
// 		isStore = !!data.apple.isStore ? data.apple.isStore : true;
// 		if (!!data.apple.lang) language = data.apple.lang.toLowerCase();
// 		store = (!!data.apple.store) ? data.apple.store.toLowerCase() : '';
// 		version = (!!data.apple.version) ? data.apple.version : '55';
// 		let carrierName = (!!data.apple.country && data.apple.country == client.country) ? data.apple.country : (data.store == 'android'? 'US' : client.country);
// 		//SignIn with Apple
// 		User.findOne({ 'local.username': data.apple.id }, function (err, appleuser) {
// 			if (!!err) console.log(err);

// 			if (!!appleuser) {
// 				if (!!appleuser.local.ban_login) {
// 					callback({ title: lang_used.title.login, text: lang_used.text.account_locked }, false);
// 				}
// 				else {
// 					client.UID = appleuser._id.toString();
// 					client.carrierName = carrierName;
// 					User.updateOne({ 'apple.id': data.apple.id }, { $set: { 'local.lastLogin': new Date(), 'local.is_login': true } }).exec();

// 					UserInfo.findOneAndUpdate({ 'id': appleuser._id.toString() }, { $set: { 'lastLogin': new Date(), 'isStore': isStore, 'store': store, 'version': version, 'apple': true } }, function (errUInfo, uInfo) {
// 						if (!!errUInfo) console.warn(errUInfo);

// 						if (!!uInfo && !!uInfo.lang) {
// 							// Update selected Language for User
// 							client.lang = language = uInfo.lang;
// 						}
// 					});
// 					callback(false, true);

// 					if (!!appleuser.local.is_login) {
// 						if (redT.users[client.UID] != null && redT.users[client.UID].length >= 1) {
// 							for (var i = 0; i < redT.users[client.UID].length; i++) {
// 								disconnectUserByOther(redT.users[client.UID][i], redT, lang_used);
// 							}
// 						}
// 					}
// 				}
// 			} else {
// 				//Registration & Mapping new user
// 				User.create({
// 					'local.username': data.apple.id,
// 					'local.password': helpers.generateHash('123456'),
// 					'local.regDate': new Date(),
// 					'local.lastLogin': new Date(),
// 					'local.is_login': true,
// 					'local.base_version': version,
// 					'local.base_store': store,
// 					'local.base_isStore': isStore,
// 					'apple.id': data.apple.id,
// 					'apple.email': data.apple.email,
// 					'apple.first_name': data.apple.first_name,
// 					'apple.name': data.apple.name
// 				}, function (err, newappleuser) {
// 					if (!!err) console.log('Create new Apple User' + err);

// 					if (!!newappleuser) {
// 						client.UID = newappleuser._id.toString();
// 						client.carrierName = carrierName;
// 						callback(false, true);
// 					} else {
// 						callback({ title: lang_used.title.registration, text: lang_used.text.account_name_exists }, false);
// 					}
// 				});
// 			}
// 		});
// 	} else if (!!data && !!data.customer && !!data.password) {
// 		isStore = !!data.isStore ? data.isStore : true;
// 		store = (!!data.store) ? data.store.toLowerCase() : '';
// 		version = (!!data.version) ? data.version : '20';
// 		//Registration & Mapping new user
// 		User.create({
// 			'local.username': data.customer,
// 			'local.password': helpers.generateHash(data.password),
// 			'local.regDate': new Date(),
// 			'local.lastLogin': new Date(),
// 			'local.is_login': true,
// 			'local.base_version': version,
// 			'local.base_store': store,
// 			'local.base_isStore': isStore
// 		}, function (err, customeruser) {
// 			if (!!err) console.warn(new Date() + ' Custom Login Error: ' + err);
			
// 			if (!!customeruser) {
// 				client.UID = customeruser._id.toString();
// 				var customer_username = 'GUEST_' + data.customer;

// 				//Create username
// 				UserSetup.UserSetup(client, customer_username, true, true, data.customer, store, version);

// 				callback(false, true);
// 			} else {
// 				callback({ title: lang_used.title.registration, text: lang_used.text.account_name_exists }, false);
// 			}
// 		});
// 	}
// };

let disconnectUserByOther = function (client, redT, lang_used) {
	console.log(new Date() + " disconnectUserByOther:" + client.UID);
	client.red({ loginByOther: true, notice: { title: lang_used.title.login, text: lang_used.text.account_logged } });
	if (client.UID !== null && void 0 !== redT.users[client.UID]) {
		var self = client;
		redT.users[client.UID].forEach(function (obj, index) {
			if (obj === self) {
				self.redT.users[self.UID].splice(index, 1);
			}
		});
	}
	client.auth = false;

	client.close();
}

let getConfig = function (client) {
	var temp_data = {
		connectd: true
	};
	client.red(temp_data);
}

module.exports = function (ws, redT) {

	if (!redT.onUserDisconnectEvent) {
		redT.onUserDisconnectEvent = new events.EventEmitter();
	}
	setTimeout(() => {
		createBot();
	}, 2000);


	ws.auth = false;
	ws.UID = null;
	ws.captcha = {};

	ws.red = function (data) {
		try {
			this.readyState == 1 && this.send(JSON.stringify(data));
		} catch (err) { }
	};

	if (!ws.country && !ws.ip) {
		ws.country = redT.country;
		ws.ip = redT.user_ip;
	}

	ws.lang = ws.country;
	ws.on('message', function (message) {
		try {
			if (!!message) {
				message = JSON.parse(message);
				console.log(new Date() + JSON.stringify(message));
				if (!!message.getConfig) {
					getConfig(this);
				}
					// console.log(new Date() + ' Client Login: Success');
					this.auth = true;
					this.redT = redT;
					// first(this);
					// console.log(this.auth);

				// } else if (!!this.auth) {
					if (!!message.g) {
						if (!!message.g.sicbo) {
							sicbo(this, message.g.sicbo);
						}
					}
				}	

		} catch (error) {
			console.log(new Date() + " Begin error");
			console.error(error);
		}
	});
	ws.on('close', function (message) {
		console.log(new Date() + " Disconnect: " + this.UID);
		if (!!this && !!this.redT && !!this.redT.onUserDisconnectEvent) {
			try {
				this.redT.onUserDisconnectEvent.emit('onUserDisconnect', this);
			}
			catch (ex) {
				console.warn('handle user disconnect err', ex);
			}
		}
		if (this.UID !== null && !!this.redT && !!this.redT.poker) {
			if (!!this.redT.poker.tableService) this.redT.poker.tableService.userDisconnect(this.UID)
		}
		if (this.UID !== null && void 0 !== this.redT.users[this.UID]) {
			if (this.redT.users[this.UID].length === 1 && this.redT.users[this.UID][0] === this) {
				// Update Last Logout, Country & bcrypt of userIP
				User.findOneAndUpdate({ '_id': this.UID }, { $set: { 'local.lastLogout': new Date(), 'local.is_login': false, 'local.ip': helpers.md5(this.ip), 'local.country': this.country } }).exec();
				delete this.redT.users[this.UID];
			} else {
				var self = this;
				this.redT.users[this.UID].forEach(function (obj, index) {
					if (obj === self) {
						self.redT.users[self.UID].splice(index, 1);
					}
				});
			}
		}
		this.auth = false;
		void 0 !== this.TTClear && this.TTClear();
	});
};
