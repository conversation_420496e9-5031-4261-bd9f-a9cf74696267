# Hệ Thống Duyệt Tiền Thủ Công (Manual Payment Approval System)

## Tổng Quan

Hệ thống duyệt tiền thủ công cho phép admin duyệt hoặc từ chối các giao dịch nạp tiền và rút tiền một cách thủ công thông qua giao diện web. Hệ thống tự động phát hiện tên miền và tích hợp với các dịch vụ approval backend hiện có.

## Tính Năng Chính

### 1. <PERSON>y<PERSON>t Nạp Tiền (Deposit Approval)
- **Trang**: `www/adminx/NapRut_DuyetNapTien.php`
- **Chức năng**:
  - Hiển thị danh sách giao dịch nạp tiền đang chờ duyệt
  - Lọc theo ngày và trạng thái
  - Duyệt giao dịch với một click
  - <PERSON>ừ chối giao dịch với lý do tùy chỉnh
  - <PERSON><PERSON> động refresh mỗi 30 giây

### 2. Duyệt Rú<PERSON> (Withdrawal Approval)
- **Trang**: `www/adminx/NapRut_DuyetRutTien.php`
- **Chức năng**:
  - Hiển thị danh sách giao dịch rút tiền đang chờ duyệt
  - Hiển thị số tiền gốc, số tiền thực nhận và phí
  - Duyệt giao dịch với xác nhận
  - Từ chối giao dịch với lý do tùy chỉnh
  - Tự động refresh mỗi 30 giây

### 3. API Backend
- **Servlet**: `TransactionApprovalServlet.java`
- **Endpoints**:
  - `c=3062`: Lấy danh sách giao dịch nạp tiền
  - `c=3063`: Lấy danh sách giao dịch rút tiền
  - `type=deposit&action=approve/reject`: Duyệt/từ chối nạp tiền
  - `type=withdrawal&action=approve/reject`: Duyệt/từ chối rút tiền

## Cấu Trúc File

```
www/adminx/
├── NapRut_DuyetNapTien.php      # Giao diện duyệt nạp tiền
├── NapRut_DuyetRutTien.php      # Giao diện duyệt rút tiền
└── menu.php                     # Menu đã được cập nhật

BackendMaster/api/VinPlayPortal/src/main/java/com/vinplay/api/payment/
└── TransactionApprovalServlet.java  # API servlet xử lý duyệt tiền
```

## Cách Sử Dụng

### 1. Truy Cập Giao Diện
1. Đăng nhập vào admin panel
2. Vào menu "Nạp Rút" → "Duyệt Nạp Tiền" hoặc "Duyệt Rút Tiền"

### 2. Duyệt Nạp Tiền
1. Chọn khoảng thời gian và trạng thái cần lọc
2. Xem danh sách giao dịch đang chờ duyệt
3. Click nút "✓" để duyệt hoặc "✗" để từ chối
4. Với từ chối: nhập lý do trong popup và xác nhận

### 3. Duyệt Rút Tiền
1. Chọn khoảng thời gian và trạng thái cần lọc
2. Xem danh sách giao dịch đang chờ duyệt (có hiển thị phí)
3. Click nút "✓" để duyệt (có popup xác nhận)
4. Click nút "✗" để từ chối với lý do

## Trạng Thái Giao Dịch

### Nạp Tiền (Deposit)
- `0`: PENDING - Đang chờ duyệt
- `1`: RECEIVED - Đang xem xét
- `2`: SUCCESS - Đã duyệt
- `3`: FAILED - Đã từ chối

### Rút Tiền (Withdrawal)
- `0`: PENDING - Đang chờ duyệt
- `12`: REQUEST - Yêu cầu rút tiền
- `1`: RECEIVED - Đang xem xét
- `4`: COMPLETED - Đã duyệt
- `3`: FAILED - Đã từ chối

## Tích Hợp Backend

### Services Được Sử Dụng
1. **PaymentManualService**: Xử lý duyệt nạp tiền
   - `depositConfirm()`: Duyệt/từ chối nạp tiền

2. **WithDrawManualBankService**: Xử lý duyệt rút tiền
   - `Approved()`: Duyệt rút tiền
   - `Reject()`: Từ chối rút tiền

3. **RechargePaygateDao & WithDrawPaygateDao**: Truy vấn dữ liệu
   - `getDepositsByDateAndStatus()`: Lấy danh sách nạp tiền
   - `getWithdrawalsByDateAndStatus()`: Lấy danh sách rút tiền

### API Parameters

#### Duyệt Nạp Tiền
```
POST /api/transaction-approval
type=deposit
action=approve|reject
order_id=CART001
admin_name=admin
reason=Lý do từ chối (chỉ khi reject)
key=BopVuEmVo123
```

#### Duyệt Rút Tiền
```
POST /api/transaction-approval
type=withdrawal
action=approve|reject
transaction_id=WD001
cart_id=CART_WD001
bank_number=**********
admin_name=admin
reason=Lý do từ chối (chỉ khi reject)
key=BopVuEmVo123
```

#### Lấy Danh Sách Giao Dịch
```
GET /api/transaction-approval
c=3062 (nạp tiền) | 3063 (rút tiền)
from_date=2024-01-01
to_date=2024-01-31
status=0
```

## Bảo Mật

1. **API Key**: Tất cả request approval cần API key `BopVuEmVo123`
2. **Session**: Kiểm tra đăng nhập admin trước khi truy cập
3. **Domain Detection**: Tự động phát hiện domain từ request
4. **CORS**: Hỗ trợ cross-origin requests

## Tự Động Hóa

1. **Auto Refresh**: Trang tự động refresh mỗi 30 giây
2. **Domain Detection**: Tự động phát hiện domain và tạo API URL
3. **Fallback Data**: Sử dụng dữ liệu demo nếu API không khả dụng

## Ghi Chú Kỹ Thuật

1. **Bootstrap 5**: Sử dụng cho giao diện responsive
2. **Font Awesome**: Icons cho buttons và status
3. **CURL**: Giao tiếp với Java backend
4. **JSON**: Format dữ liệu trao đổi
5. **Gson**: Parse JSON trong Java servlet

## Troubleshooting

### Lỗi Thường Gặp

1. **"Invalid API key"**: Kiểm tra key trong request
2. **"Unknown error"**: Kiểm tra log backend servlet
3. **Không load được dữ liệu**: Kiểm tra kết nối API và database
4. **Domain không đúng**: Kiểm tra logic detect domain

### Debug

1. Kiểm tra network tab trong browser để xem API calls
2. Xem log trong Java application server
3. Kiểm tra database connection và queries
4. Verify session và authentication

## Mở Rộng

Hệ thống có thể được mở rộng để:
1. Thêm notification real-time
2. Tích hợp với hệ thống báo cáo
3. Thêm workflow approval nhiều cấp
4. Export dữ liệu ra Excel/PDF
5. Tích hợp với hệ thống audit log

## Liên Hệ

Để hỗ trợ kỹ thuật hoặc báo lỗi, vui lòng liên hệ team phát triển với thông tin chi tiết về lỗi và môi trường sử dụng.
