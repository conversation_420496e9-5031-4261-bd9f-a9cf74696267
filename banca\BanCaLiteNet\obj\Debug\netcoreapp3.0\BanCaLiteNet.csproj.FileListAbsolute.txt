/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/cashinhigh.json
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/cashinlow.json
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/cashinmid.json
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/cer.pfx
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/config.json
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/data.json
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/default.json
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/high.json
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/low.json
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/mid.json
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/now.sh
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/stop.js
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/BanCaLiteNet
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/BanCaLiteNet.deps.json
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/BanCaLiteNet.runtimeconfig.json
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/BanCaLiteNet.runtimeconfig.dev.json
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/BanCaLiteNet.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/BanCaLiteNet.pdb
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/BouncyCastle.Crypto.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Google.Protobuf.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/HtmlAgilityPack.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/JWT.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/K4os.Compression.LZ4.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Microsoft.Diagnostics.Runtime.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Microsoft.DotNet.PlatformAbstractions.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Microsoft.Extensions.DependencyModel.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Microsoft.Extensions.PlatformAbstractions.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/MySql.Data.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Nancy.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Nancy.Hosting.Self.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/NCrontab.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Newtonsoft.Json.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Pipelines.Sockets.Unofficial.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Serilog.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Serilog.Sinks.Console.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Serilog.Sinks.File.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Renci.SshNet.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/SshNet.Security.Cryptography.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/StackExchange.Redis.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/System.Configuration.ConfigurationManager.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/System.Diagnostics.PerformanceCounter.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/System.IO.Pipelines.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/System.Resources.Extensions.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/System.Security.Cryptography.ProtectedData.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/System.Security.Permissions.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/System.Xml.XPath.XmlDocument.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/runtimes/win/lib/netcoreapp2.0/System.Diagnostics.PerformanceCounter.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Core.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Fleck.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/websocket-sharp.dll
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Core.pdb
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Fleck.pdb
/root/AllBackend/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/websocket-sharp.pdb
/root/AllBackend/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.csprojAssemblyReference.cache
/root/AllBackend/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.AssemblyInfoInputs.cache
/root/AllBackend/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.AssemblyInfo.cs
/root/AllBackend/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.csproj.CoreCompileInputs.cache
/root/AllBackend/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.csproj.CopyComplete
/root/AllBackend/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.dll
/root/AllBackend/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.pdb
/root/AllBackend/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.genruntimeconfig.cache
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/cashinhigh.json
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/cashinlow.json
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/cashinmid.json
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/cer.pfx
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/data.json
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/default.json
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/high.json
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/low.json
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/mid.json
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/now.sh
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/stop.js
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/config.json
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/BanCaLiteNet
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/BanCaLiteNet.deps.json
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/BanCaLiteNet.runtimeconfig.json
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/BanCaLiteNet.runtimeconfig.dev.json
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/BanCaLiteNet.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/BanCaLiteNet.pdb
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/BouncyCastle.Crypto.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Google.Protobuf.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/HtmlAgilityPack.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/JWT.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/K4os.Compression.LZ4.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Microsoft.Diagnostics.Runtime.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Microsoft.DotNet.PlatformAbstractions.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Microsoft.Extensions.DependencyModel.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Microsoft.Extensions.PlatformAbstractions.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/MySql.Data.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Nancy.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Nancy.Hosting.Self.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/NCrontab.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Newtonsoft.Json.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Pipelines.Sockets.Unofficial.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Serilog.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Serilog.Sinks.Console.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Serilog.Sinks.File.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Renci.SshNet.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/SshNet.Security.Cryptography.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/StackExchange.Redis.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/System.Configuration.ConfigurationManager.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/System.Diagnostics.PerformanceCounter.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/System.IO.Pipelines.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/System.Resources.Extensions.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/System.Security.Cryptography.ProtectedData.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/System.Security.Permissions.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/System.Xml.XPath.XmlDocument.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/runtimes/win/lib/netcoreapp2.0/System.Diagnostics.PerformanceCounter.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Core.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Fleck.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/websocket-sharp.dll
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Core.pdb
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/Fleck.pdb
/var/app/banca/BanCaLiteNet/bin/Debug/netcoreapp3.0/websocket-sharp.pdb
/var/app/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.csprojAssemblyReference.cache
/var/app/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.AssemblyInfoInputs.cache
/var/app/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.AssemblyInfo.cs
/var/app/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.csproj.CoreCompileInputs.cache
/var/app/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.csproj.CopyComplete
/var/app/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.dll
/var/app/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.pdb
/var/app/banca/BanCaLiteNet/obj/Debug/netcoreapp3.0/BanCaLiteNet.genruntimeconfig.cache
