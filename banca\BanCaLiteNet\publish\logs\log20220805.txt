00:00:02.487 [DBG] Sent 184 bytes
no ex
00:00:02.931 [DBG] 32 bytes read
no ex
00:00:02.931 [DBG] Sent 31 bytes
no ex
00:00:05.939 [DBG] 32 bytes read
no ex
00:00:05.940 [DBG] Sent 31 bytes
no ex
00:00:07.480 [DBG] Sent 184 bytes
no ex
00:00:08.945 [DBG] 32 bytes read
no ex
00:00:08.945 [DBG] Sent 31 bytes
no ex
00:00:11.938 [DBG] 32 bytes read
no ex
00:00:11.939 [DBG] Sent 31 bytes
no ex
00:00:12.487 [DBG] Sent 184 bytes
no ex
00:00:12.487 [DBG] Sent 71 bytes
no ex
00:00:14.941 [DBG] 32 bytes read
no ex
00:00:14.941 [DBG] Sent 31 bytes
no ex
00:00:17.496 [DBG] Sent 184 bytes
no ex
00:00:17.936 [DBG] 32 bytes read
no ex
00:00:17.936 [DBG] Sent 31 bytes
no ex
00:00:20.941 [DBG] 32 bytes read
no ex
00:00:20.941 [DBG] Sent 31 bytes
no ex
00:00:22.493 [DBG] Sent 184 bytes
no ex
00:00:23.935 [DBG] 32 bytes read
no ex
00:00:23.935 [DBG] Sent 31 bytes
no ex
00:00:26.940 [DBG] 32 bytes read
no ex
00:00:26.940 [DBG] Sent 31 bytes
no ex
00:00:27.491 [DBG] Sent 184 bytes
no ex
00:00:29.942 [DBG] 32 bytes read
no ex
00:00:29.942 [DBG] Sent 31 bytes
no ex
00:00:32.497 [DBG] Sent 184 bytes
no ex
00:00:32.938 [DBG] 32 bytes read
no ex
00:00:32.938 [DBG] Sent 31 bytes
no ex
00:00:35.941 [DBG] 32 bytes read
no ex
00:00:35.942 [DBG] Sent 31 bytes
no ex
00:00:37.486 [DBG] Sent 184 bytes
no ex
00:00:38.956 [DBG] 33 bytes read
no ex
00:00:38.956 [DBG] Sent 32 bytes
no ex
00:00:41.961 [DBG] 33 bytes read
no ex
00:00:41.961 [DBG] Sent 32 bytes
no ex
00:00:42.491 [DBG] Sent 184 bytes
no ex
00:00:44.943 [DBG] 33 bytes read
no ex
00:00:44.943 [DBG] Sent 32 bytes
no ex
00:00:47.497 [DBG] Sent 184 bytes
no ex
00:00:47.941 [DBG] 33 bytes read
no ex
00:00:47.941 [DBG] Sent 32 bytes
no ex
00:00:50.938 [DBG] 33 bytes read
no ex
00:00:50.938 [DBG] Sent 32 bytes
no ex
00:00:52.496 [DBG] Sent 184 bytes
no ex
00:00:53.942 [DBG] 33 bytes read
no ex
00:00:53.943 [DBG] Sent 32 bytes
no ex
00:00:56.946 [DBG] 33 bytes read
no ex
00:00:56.946 [DBG] Sent 32 bytes
no ex
00:00:57.500 [DBG] Sent 184 bytes
no ex
00:00:59.948 [DBG] 33 bytes read
no ex
00:00:59.948 [DBG] Sent 32 bytes
no ex
00:01:02.492 [DBG] Sent 184 bytes
no ex
00:01:02.944 [DBG] 33 bytes read
no ex
00:01:02.944 [DBG] Sent 32 bytes
no ex
00:01:05.949 [DBG] 33 bytes read
no ex
00:01:05.950 [DBG] Sent 32 bytes
no ex
00:01:07.488 [DBG] Sent 184 bytes
no ex
00:01:08.949 [DBG] 33 bytes read
no ex
00:01:08.950 [DBG] Sent 32 bytes
no ex
00:01:11.944 [DBG] 33 bytes read
no ex
00:01:11.944 [DBG] Sent 32 bytes
no ex
00:01:12.493 [DBG] Sent 184 bytes
no ex
00:01:14.949 [DBG] 33 bytes read
no ex
00:01:14.949 [DBG] Sent 32 bytes
no ex
00:01:17.503 [DBG] Sent 184 bytes
no ex
00:01:17.946 [DBG] 33 bytes read
no ex
00:01:17.946 [DBG] Sent 32 bytes
no ex
00:01:20.950 [DBG] 33 bytes read
no ex
00:01:20.950 [DBG] Sent 32 bytes
no ex
00:01:22.500 [DBG] Sent 184 bytes
no ex
00:01:23.947 [DBG] 33 bytes read
no ex
00:01:23.947 [DBG] Sent 32 bytes
no ex
00:01:26.952 [DBG] 33 bytes read
no ex
00:01:26.953 [DBG] Sent 32 bytes
no ex
00:01:27.493 [DBG] Sent 184 bytes
no ex
00:01:29.950 [DBG] 33 bytes read
no ex
00:01:29.950 [DBG] Sent 32 bytes
no ex
00:01:32.497 [DBG] Sent 184 bytes
no ex
00:01:32.949 [DBG] 33 bytes read
no ex
00:01:32.949 [DBG] Sent 32 bytes
no ex
00:01:35.109 [INF] RunScan: 0
00:01:35.109 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:1)","Data":null,"DataObj":null}
00:01:35.957 [DBG] 33 bytes read
no ex
00:01:35.957 [DBG] Sent 32 bytes
no ex
00:01:37.501 [DBG] Sent 184 bytes
no ex
00:01:38.957 [DBG] 33 bytes read
no ex
00:01:38.957 [DBG] Sent 32 bytes
no ex
00:01:41.973 [DBG] 33 bytes read
no ex
00:01:41.974 [DBG] Sent 32 bytes
no ex
00:01:42.502 [DBG] Sent 184 bytes
no ex
00:01:44.948 [DBG] 33 bytes read
no ex
00:01:44.948 [DBG] Sent 32 bytes
no ex
00:01:47.498 [DBG] Sent 184 bytes
no ex
00:01:47.954 [DBG] 33 bytes read
no ex
00:01:47.954 [DBG] Sent 32 bytes
no ex
00:01:50.952 [DBG] 33 bytes read
no ex
00:01:50.953 [DBG] Sent 32 bytes
no ex
00:01:52.505 [DBG] Sent 184 bytes
no ex
00:01:53.949 [DBG] 33 bytes read
no ex
00:01:53.949 [DBG] Sent 32 bytes
no ex
00:01:56.955 [DBG] 33 bytes read
no ex
00:01:56.955 [DBG] Sent 32 bytes
no ex
00:01:57.505 [DBG] Sent 184 bytes
no ex
00:01:57.505 [DBG] Sent 71 bytes
no ex
00:01:59.946 [DBG] 33 bytes read
no ex
00:01:59.947 [DBG] Sent 32 bytes
no ex
00:02:02.496 [DBG] Sent 184 bytes
no ex
00:02:02.953 [DBG] 33 bytes read
no ex
00:02:02.953 [DBG] Sent 32 bytes
no ex
00:02:05.966 [DBG] 33 bytes read
no ex
00:02:05.966 [DBG] Sent 32 bytes
no ex
00:02:07.502 [DBG] Sent 184 bytes
no ex
00:02:08.955 [DBG] 33 bytes read
no ex
00:02:08.955 [DBG] Sent 32 bytes
no ex
00:02:11.953 [DBG] 33 bytes read
no ex
00:02:11.954 [DBG] Sent 32 bytes
no ex
00:02:12.505 [DBG] Sent 184 bytes
no ex
00:02:14.954 [DBG] 33 bytes read
no ex
00:02:14.954 [DBG] Sent 32 bytes
no ex
00:02:17.510 [DBG] Sent 184 bytes
no ex
00:02:17.953 [DBG] 33 bytes read
no ex
00:02:17.953 [DBG] Sent 32 bytes
no ex
00:02:20.965 [DBG] 33 bytes read
no ex
00:02:20.965 [DBG] Sent 32 bytes
no ex
00:02:22.495 [DBG] Sent 184 bytes
no ex
00:02:23.964 [DBG] 33 bytes read
no ex
00:02:23.964 [DBG] Sent 32 bytes
no ex
00:02:26.962 [DBG] 33 bytes read
no ex
00:02:26.962 [DBG] Sent 32 bytes
no ex
00:02:27.496 [DBG] Sent 184 bytes
no ex
00:02:29.965 [DBG] 33 bytes read
no ex
00:02:29.965 [DBG] Sent 32 bytes
no ex
00:02:32.502 [DBG] Sent 184 bytes
no ex
00:02:32.958 [DBG] 33 bytes read
no ex
00:02:32.958 [DBG] Sent 32 bytes
no ex
00:02:35.970 [DBG] 33 bytes read
no ex
00:02:35.971 [DBG] Sent 32 bytes
no ex
00:02:36.083 [INF] RunHandleWinLoss: 0
00:02:36.083 [INF] CalculateResult: 20220805
00:02:36.083 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
00:02:37.500 [DBG] Sent 184 bytes
no ex
00:02:38.965 [DBG] 33 bytes read
no ex
00:02:38.965 [DBG] Sent 32 bytes
no ex
00:02:41.979 [DBG] 33 bytes read
no ex
00:02:41.980 [DBG] Sent 32 bytes
no ex
00:02:42.500 [DBG] Sent 184 bytes
no ex
00:02:44.959 [DBG] 33 bytes read
no ex
00:02:44.960 [DBG] Sent 32 bytes
no ex
00:02:47.506 [DBG] Sent 184 bytes
no ex
00:02:47.969 [DBG] 33 bytes read
no ex
00:02:47.970 [DBG] Sent 32 bytes
no ex
00:02:51.104 [DBG] 33 bytes read
no ex
00:02:51.104 [DBG] Sent 32 bytes
no ex
00:02:52.508 [DBG] Sent 184 bytes
no ex
00:02:54.009 [DBG] 33 bytes read
no ex
00:02:54.009 [DBG] Sent 32 bytes
no ex
00:02:57.009 [DBG] 33 bytes read
no ex
00:02:57.009 [DBG] Sent 32 bytes
no ex
00:02:57.510 [DBG] Sent 184 bytes
no ex
00:03:00.009 [DBG] 33 bytes read
no ex
00:03:00.009 [DBG] Sent 32 bytes
no ex
00:03:02.504 [DBG] Sent 184 bytes
no ex
00:03:03.005 [DBG] 33 bytes read
no ex
00:03:03.005 [DBG] Sent 32 bytes
no ex
00:03:06.012 [DBG] 33 bytes read
no ex
00:03:06.012 [DBG] Sent 32 bytes
no ex
00:03:07.509 [DBG] Sent 184 bytes
no ex
00:03:09.003 [DBG] 33 bytes read
no ex
00:03:09.003 [DBG] Sent 32 bytes
no ex
00:03:12.005 [DBG] 33 bytes read
no ex
00:03:12.005 [DBG] Sent 32 bytes
no ex
00:03:12.513 [DBG] Sent 184 bytes
no ex
00:03:15.005 [DBG] 33 bytes read
no ex
00:03:15.005 [DBG] Sent 32 bytes
no ex
00:03:17.503 [DBG] Sent 184 bytes
no ex
00:03:18.019 [DBG] 33 bytes read
no ex
00:03:18.020 [DBG] Sent 32 bytes
no ex
00:03:21.006 [DBG] 33 bytes read
no ex
00:03:21.007 [DBG] Sent 32 bytes
no ex
00:03:22.503 [DBG] Sent 184 bytes
no ex
00:03:24.011 [DBG] 33 bytes read
no ex
00:03:24.011 [DBG] Sent 32 bytes
no ex
00:03:27.017 [DBG] 33 bytes read
no ex
00:03:27.017 [DBG] Sent 32 bytes
no ex
00:03:27.510 [DBG] Sent 184 bytes
no ex
00:03:30.008 [DBG] 33 bytes read
no ex
00:03:30.009 [DBG] Sent 32 bytes
no ex
00:03:32.507 [DBG] Sent 184 bytes
no ex
00:03:33.005 [DBG] 33 bytes read
no ex
00:03:33.005 [DBG] Sent 32 bytes
no ex
00:03:36.009 [DBG] 33 bytes read
no ex
00:03:36.009 [DBG] Sent 32 bytes
no ex
00:03:37.510 [DBG] Sent 184 bytes
no ex
00:03:39.017 [DBG] 33 bytes read
no ex
00:03:39.017 [DBG] Sent 32 bytes
no ex
00:03:42.012 [DBG] 33 bytes read
no ex
00:03:42.013 [DBG] Sent 32 bytes
no ex
00:03:42.505 [DBG] Sent 184 bytes
no ex
00:03:45.018 [DBG] 33 bytes read
no ex
00:03:45.020 [DBG] Sent 32 bytes
no ex
00:03:47.505 [DBG] Sent 184 bytes
no ex
00:03:48.017 [DBG] 33 bytes read
no ex
00:03:48.018 [DBG] Sent 32 bytes
no ex
00:03:51.022 [DBG] 33 bytes read
no ex
00:03:51.022 [DBG] Sent 32 bytes
no ex
00:03:52.515 [DBG] Sent 184 bytes
no ex
00:03:54.019 [DBG] 33 bytes read
no ex
00:03:54.019 [DBG] Sent 32 bytes
no ex
00:03:57.022 [DBG] 33 bytes read
no ex
00:03:57.022 [DBG] Sent 32 bytes
no ex
00:03:57.505 [DBG] Sent 184 bytes
no ex
00:04:00.020 [DBG] 33 bytes read
no ex
00:04:00.020 [DBG] Sent 32 bytes
no ex
00:04:02.510 [DBG] Sent 184 bytes
no ex
00:04:03.021 [DBG] 33 bytes read
no ex
00:04:03.021 [DBG] Sent 32 bytes
no ex
00:04:06.020 [DBG] 33 bytes read
no ex
00:04:06.020 [DBG] Sent 32 bytes
no ex
00:04:07.513 [DBG] Sent 184 bytes
no ex
00:04:09.021 [DBG] 33 bytes read
no ex
00:04:09.021 [DBG] Sent 32 bytes
no ex
00:04:12.020 [DBG] 33 bytes read
no ex
00:04:12.020 [DBG] Sent 32 bytes
no ex
00:04:12.512 [DBG] Sent 184 bytes
no ex
00:04:15.018 [DBG] 33 bytes read
no ex
00:04:15.018 [DBG] Sent 32 bytes
no ex
00:04:17.507 [DBG] Sent 184 bytes
no ex
00:04:18.014 [DBG] 33 bytes read
no ex
00:04:18.014 [DBG] Sent 32 bytes
no ex
00:04:21.025 [DBG] 33 bytes read
no ex
00:04:21.026 [DBG] Sent 32 bytes
no ex
00:04:22.512 [DBG] Sent 184 bytes
no ex
00:04:24.022 [DBG] 33 bytes read
no ex
00:04:24.022 [DBG] Sent 32 bytes
no ex
00:04:27.022 [DBG] 33 bytes read
no ex
00:04:27.022 [DBG] Sent 32 bytes
no ex
00:04:27.521 [DBG] Sent 184 bytes
no ex
00:04:30.024 [DBG] 33 bytes read
no ex
00:04:30.024 [DBG] Sent 32 bytes
no ex
00:04:32.523 [DBG] Sent 184 bytes
no ex
00:04:33.033 [DBG] 33 bytes read
no ex
00:04:33.033 [DBG] Sent 32 bytes
no ex
00:04:36.023 [DBG] 33 bytes read
no ex
00:04:36.023 [DBG] Sent 32 bytes
no ex
00:04:37.514 [DBG] Sent 184 bytes
no ex
00:04:39.025 [DBG] 33 bytes read
no ex
00:04:39.025 [DBG] Sent 32 bytes
no ex
00:04:42.021 [DBG] 33 bytes read
no ex
00:04:42.021 [DBG] Sent 32 bytes
no ex
00:04:42.523 [DBG] Sent 184 bytes
no ex
00:04:45.033 [DBG] 33 bytes read
no ex
00:04:45.033 [DBG] Sent 32 bytes
no ex
00:04:46.525 [DBG] Sent 75 bytes
no ex
00:04:47.520 [DBG] Sent 184 bytes
no ex
00:04:48.018 [DBG] 33 bytes read
no ex
00:04:48.018 [DBG] Sent 32 bytes
no ex
00:04:51.020 [DBG] 33 bytes read
no ex
00:04:51.020 [DBG] Sent 32 bytes
no ex
00:04:52.527 [DBG] Sent 184 bytes
no ex
00:04:54.027 [DBG] 33 bytes read
no ex
00:04:54.027 [DBG] Sent 32 bytes
no ex
00:04:57.026 [DBG] 33 bytes read
no ex
00:04:57.026 [DBG] Sent 32 bytes
no ex
00:04:57.517 [DBG] Sent 184 bytes
no ex
00:05:00.019 [DBG] 33 bytes read
no ex
00:05:00.019 [DBG] Sent 32 bytes
no ex
00:05:02.522 [DBG] Sent 184 bytes
no ex
00:05:03.032 [DBG] 33 bytes read
no ex
00:05:03.033 [DBG] Sent 32 bytes
no ex
00:05:06.025 [DBG] 33 bytes read
no ex
00:05:06.026 [DBG] Sent 32 bytes
no ex
00:05:07.524 [DBG] Sent 184 bytes
no ex
00:05:09.030 [DBG] 33 bytes read
no ex
00:05:09.030 [DBG] Sent 32 bytes
no ex
00:05:12.031 [DBG] 33 bytes read
no ex
00:05:12.031 [DBG] Sent 32 bytes
no ex
00:05:12.526 [DBG] Sent 184 bytes
no ex
00:05:15.030 [DBG] 33 bytes read
no ex
00:05:15.030 [DBG] Sent 32 bytes
no ex
00:05:17.520 [DBG] Sent 184 bytes
no ex
00:05:18.029 [DBG] 33 bytes read
no ex
00:05:18.029 [DBG] Sent 32 bytes
no ex
00:05:21.036 [DBG] 33 bytes read
no ex
00:05:21.036 [DBG] Sent 32 bytes
no ex
00:05:22.536 [DBG] Sent 184 bytes
no ex
00:05:24.026 [DBG] 33 bytes read
no ex
00:05:24.027 [DBG] Sent 32 bytes
no ex
00:05:27.037 [DBG] 33 bytes read
no ex
00:05:27.037 [DBG] Sent 32 bytes
no ex
00:05:27.525 [DBG] Sent 184 bytes
no ex
00:05:30.040 [DBG] 33 bytes read
no ex
00:05:30.040 [DBG] Sent 32 bytes
no ex
00:05:32.525 [DBG] Sent 184 bytes
no ex
00:05:33.032 [DBG] 33 bytes read
no ex
00:05:33.032 [DBG] Sent 32 bytes
no ex
00:05:36.084 [DBG] 33 bytes read
no ex
00:05:36.084 [DBG] Sent 32 bytes
no ex
00:05:37.536 [DBG] Sent 184 bytes
no ex
00:05:40.077 [DBG] 33 bytes read
no ex
00:05:40.078 [DBG] Sent 32 bytes
no ex
00:05:42.524 [DBG] Sent 184 bytes
no ex
00:05:44.162 [DBG] 33 bytes read
no ex
00:05:44.162 [DBG] Sent 32 bytes
no ex
00:05:45.897 [DBG] 0 bytes read. Closing.
no ex
00:06:35.109 [INF] RunScan: 0
00:06:35.109 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:6)","Data":null,"DataObj":null}
00:07:36.085 [INF] RunHandleWinLoss: 0
00:07:36.085 [INF] CalculateResult: 20220805
00:07:36.085 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
00:11:35.109 [INF] RunScan: 0
00:11:35.109 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:11)","Data":null,"DataObj":null}
00:12:36.087 [INF] RunHandleWinLoss: 0
00:12:36.087 [INF] CalculateResult: 20220805
00:12:36.087 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
00:16:35.110 [INF] RunScan: 0
00:16:35.110 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:16)","Data":null,"DataObj":null}
00:17:36.088 [INF] RunHandleWinLoss: 0
00:17:36.088 [INF] CalculateResult: 20220805
00:17:36.088 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
00:21:35.110 [INF] RunScan: 0
00:21:35.110 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:21)","Data":null,"DataObj":null}
00:22:36.089 [INF] RunHandleWinLoss: 0
00:22:36.089 [INF] CalculateResult: 20220805
00:22:36.089 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
00:26:35.111 [INF] RunScan: 0
00:26:35.112 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:26)","Data":null,"DataObj":null}
00:27:36.090 [INF] RunHandleWinLoss: 0
00:27:36.090 [INF] CalculateResult: 20220805
00:27:36.091 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
00:31:35.112 [INF] RunScan: 0
00:31:35.112 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:31)","Data":null,"DataObj":null}
00:31:38.935 [DBG] Client connected from 206.189.158.188:53020
no ex
00:31:38.935 [DBG] 19 bytes read
no ex
00:31:40.935 [DBG] 0 bytes read. Closing.
no ex
00:32:36.092 [INF] RunHandleWinLoss: 0
00:32:36.092 [INF] CalculateResult: 20220805
00:32:36.092 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
00:36:35.112 [INF] RunScan: 0
00:36:35.112 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:36)","Data":null,"DataObj":null}
00:37:36.093 [INF] RunHandleWinLoss: 0
00:37:36.093 [INF] CalculateResult: 20220805
00:37:36.093 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
00:41:35.113 [INF] RunScan: 0
00:41:35.113 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:41)","Data":null,"DataObj":null}
00:42:36.094 [INF] RunHandleWinLoss: 0
00:42:36.094 [INF] CalculateResult: 20220805
00:42:36.094 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
00:46:35.113 [INF] RunScan: 0
00:46:35.113 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:46)","Data":null,"DataObj":null}
00:47:36.096 [INF] RunHandleWinLoss: 0
00:47:36.096 [INF] CalculateResult: 20220805
00:47:36.096 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
00:51:35.113 [INF] RunScan: 0
00:51:35.113 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:51)","Data":null,"DataObj":null}
00:52:36.099 [INF] RunHandleWinLoss: 0
00:52:36.099 [INF] CalculateResult: 20220805
00:52:36.099 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
00:56:35.114 [INF] RunScan: 0
00:56:35.114 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:56)","Data":null,"DataObj":null}
00:57:36.101 [INF] RunHandleWinLoss: 0
00:57:36.101 [INF] CalculateResult: 20220805
00:57:36.101 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
01:01:35.114 [INF] RunScan: 0
01:01:35.115 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:1)","Data":null,"DataObj":null}
01:02:36.102 [INF] RunHandleWinLoss: 0
01:02:36.102 [INF] CalculateResult: 20220805
01:02:36.102 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
01:06:35.115 [INF] RunScan: 0
01:06:35.115 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:6)","Data":null,"DataObj":null}
01:07:36.104 [INF] RunHandleWinLoss: 0
01:07:36.104 [INF] CalculateResult: 20220805
01:07:36.104 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
01:11:35.115 [INF] RunScan: 0
01:11:35.115 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:11)","Data":null,"DataObj":null}
01:12:36.105 [INF] RunHandleWinLoss: 0
01:12:36.105 [INF] CalculateResult: 20220805
01:12:36.105 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
01:16:35.115 [INF] RunScan: 0
01:16:35.115 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:16)","Data":null,"DataObj":null}
01:17:36.107 [INF] RunHandleWinLoss: 0
01:17:36.107 [INF] CalculateResult: 20220805
01:17:36.107 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
01:21:35.115 [INF] RunScan: 0
01:21:35.115 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:21)","Data":null,"DataObj":null}
01:22:36.108 [INF] RunHandleWinLoss: 0
01:22:36.108 [INF] CalculateResult: 20220805
01:22:36.108 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
01:26:35.115 [INF] RunScan: 0
01:26:35.116 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:26)","Data":null,"DataObj":null}
01:27:36.110 [INF] RunHandleWinLoss: 0
01:27:36.110 [INF] CalculateResult: 20220805
01:27:36.110 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
01:31:35.116 [INF] RunScan: 0
01:31:35.116 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:31)","Data":null,"DataObj":null}
01:32:36.111 [INF] RunHandleWinLoss: 0
01:32:36.112 [INF] CalculateResult: 20220805
01:32:36.112 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
01:36:35.116 [INF] RunScan: 0
01:36:35.117 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:36)","Data":null,"DataObj":null}
01:37:36.113 [INF] RunHandleWinLoss: 0
01:37:36.113 [INF] CalculateResult: 20220805
01:37:36.113 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
01:41:35.117 [INF] RunScan: 0
01:41:35.117 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:41)","Data":null,"DataObj":null}
01:42:36.115 [INF] RunHandleWinLoss: 0
01:42:36.115 [INF] CalculateResult: 20220805
01:42:36.115 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
01:46:35.117 [INF] RunScan: 0
01:46:35.117 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:46)","Data":null,"DataObj":null}
01:47:36.117 [INF] RunHandleWinLoss: 0
01:47:36.117 [INF] CalculateResult: 20220805
01:47:36.117 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
01:51:35.117 [INF] RunScan: 0
01:51:35.117 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:51)","Data":null,"DataObj":null}
01:52:36.121 [INF] RunHandleWinLoss: 0
01:52:36.122 [INF] CalculateResult: 20220805
01:52:36.122 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
01:56:35.117 [INF] RunScan: 0
01:56:35.117 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:56)","Data":null,"DataObj":null}
01:57:36.123 [INF] RunHandleWinLoss: 0
01:57:36.123 [INF] CalculateResult: 20220805
01:57:36.123 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
02:01:35.117 [INF] RunScan: 0
02:01:35.118 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:1)","Data":null,"DataObj":null}
02:02:36.125 [INF] RunHandleWinLoss: 0
02:02:36.125 [INF] CalculateResult: 20220805
02:02:36.125 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
02:06:35.118 [INF] RunScan: 0
02:06:35.118 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:6)","Data":null,"DataObj":null}
02:07:36.127 [INF] RunHandleWinLoss: 0
02:07:36.127 [INF] CalculateResult: 20220805
02:07:36.127 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
02:11:35.118 [INF] RunScan: 0
02:11:35.118 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:11)","Data":null,"DataObj":null}
02:12:36.128 [INF] RunHandleWinLoss: 0
02:12:36.128 [INF] CalculateResult: 20220805
02:12:36.128 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
02:16:35.118 [INF] RunScan: 0
02:16:35.118 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:16)","Data":null,"DataObj":null}
02:17:36.129 [INF] RunHandleWinLoss: 0
02:17:36.129 [INF] CalculateResult: 20220805
02:17:36.129 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
02:21:35.118 [INF] RunScan: 0
02:21:35.118 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:21)","Data":null,"DataObj":null}
02:22:36.130 [INF] RunHandleWinLoss: 0
02:22:36.130 [INF] CalculateResult: 20220805
02:22:36.130 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
02:26:35.118 [INF] RunScan: 0
02:26:35.119 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:26)","Data":null,"DataObj":null}
02:27:36.132 [INF] RunHandleWinLoss: 0
02:27:36.132 [INF] CalculateResult: 20220805
02:27:36.132 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
02:31:35.119 [INF] RunScan: 0
02:31:35.119 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:31)","Data":null,"DataObj":null}
02:32:36.134 [INF] RunHandleWinLoss: 0
02:32:36.134 [INF] CalculateResult: 20220805
02:32:36.134 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
02:36:35.120 [INF] RunScan: 0
02:36:35.120 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:36)","Data":null,"DataObj":null}
02:37:36.135 [INF] RunHandleWinLoss: 0
02:37:36.136 [INF] CalculateResult: 20220805
02:37:36.136 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
02:41:35.120 [INF] RunScan: 0
02:41:35.120 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:41)","Data":null,"DataObj":null}
02:42:36.138 [INF] RunHandleWinLoss: 0
02:42:36.138 [INF] CalculateResult: 20220805
02:42:36.138 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
02:46:35.121 [INF] RunScan: 0
02:46:35.121 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:46)","Data":null,"DataObj":null}
02:47:36.140 [INF] RunHandleWinLoss: 0
02:47:36.140 [INF] CalculateResult: 20220805
02:47:36.140 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
02:51:35.121 [INF] RunScan: 0
02:51:35.121 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:51)","Data":null,"DataObj":null}
02:52:36.141 [INF] RunHandleWinLoss: 0
02:52:36.141 [INF] CalculateResult: 20220805
02:52:36.141 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
02:56:35.121 [INF] RunScan: 0
02:56:35.121 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:56)","Data":null,"DataObj":null}
02:57:36.143 [INF] RunHandleWinLoss: 0
02:57:36.143 [INF] CalculateResult: 20220805
02:57:36.143 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
03:01:35.121 [INF] RunScan: 0
03:01:35.121 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:1)","Data":null,"DataObj":null}
03:02:36.144 [INF] RunHandleWinLoss: 0
03:02:36.145 [INF] CalculateResult: 20220805
03:02:36.145 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
03:06:35.121 [INF] RunScan: 0
03:06:35.121 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:6)","Data":null,"DataObj":null}
03:07:36.146 [INF] RunHandleWinLoss: 0
03:07:36.146 [INF] CalculateResult: 20220805
03:07:36.146 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
03:11:35.122 [INF] RunScan: 0
03:11:35.122 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:11)","Data":null,"DataObj":null}
03:12:36.148 [INF] RunHandleWinLoss: 0
03:12:36.148 [INF] CalculateResult: 20220805
03:12:36.148 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
03:16:35.122 [INF] RunScan: 0
03:16:35.122 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:16)","Data":null,"DataObj":null}
03:17:36.150 [INF] RunHandleWinLoss: 0
03:17:36.150 [INF] CalculateResult: 20220805
03:17:36.150 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
03:21:35.122 [INF] RunScan: 0
03:21:35.122 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:21)","Data":null,"DataObj":null}
03:22:36.151 [INF] RunHandleWinLoss: 0
03:22:36.151 [INF] CalculateResult: 20220805
03:22:36.151 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
03:26:35.122 [INF] RunScan: 0
03:26:35.122 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:26)","Data":null,"DataObj":null}
03:27:36.152 [INF] RunHandleWinLoss: 0
03:27:36.152 [INF] CalculateResult: 20220805
03:27:36.152 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
03:31:35.123 [INF] RunScan: 0
03:31:35.123 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:31)","Data":null,"DataObj":null}
03:32:36.154 [INF] RunHandleWinLoss: 0
03:32:36.154 [INF] CalculateResult: 20220805
03:32:36.154 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
03:36:35.123 [INF] RunScan: 0
03:36:35.123 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:36)","Data":null,"DataObj":null}
03:37:36.156 [INF] RunHandleWinLoss: 0
03:37:36.156 [INF] CalculateResult: 20220805
03:37:36.156 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
03:41:35.123 [INF] RunScan: 0
03:41:35.123 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:41)","Data":null,"DataObj":null}
03:42:36.157 [INF] RunHandleWinLoss: 0
03:42:36.158 [INF] CalculateResult: 20220805
03:42:36.158 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
03:46:35.123 [INF] RunScan: 0
03:46:35.123 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:46)","Data":null,"DataObj":null}
03:47:36.159 [INF] RunHandleWinLoss: 0
03:47:36.159 [INF] CalculateResult: 20220805
03:47:36.159 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
03:51:35.123 [INF] RunScan: 0
03:51:35.123 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:51)","Data":null,"DataObj":null}
03:52:36.161 [INF] RunHandleWinLoss: 0
03:52:36.161 [INF] CalculateResult: 20220805
03:52:36.161 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
03:56:35.124 [INF] RunScan: 0
03:56:35.124 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:56)","Data":null,"DataObj":null}
03:57:36.162 [INF] RunHandleWinLoss: 0
03:57:36.162 [INF] CalculateResult: 20220805
03:57:36.162 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
04:01:35.124 [INF] RunScan: 0
04:01:35.124 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:1)","Data":null,"DataObj":null}
04:02:36.164 [INF] RunHandleWinLoss: 0
04:02:36.164 [INF] CalculateResult: 20220805
04:02:36.164 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
04:06:35.124 [INF] RunScan: 0
04:06:35.124 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:6)","Data":null,"DataObj":null}
04:07:36.165 [INF] RunHandleWinLoss: 0
04:07:36.165 [INF] CalculateResult: 20220805
04:07:36.165 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
04:11:35.124 [INF] RunScan: 0
04:11:35.124 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:11)","Data":null,"DataObj":null}
04:12:36.166 [INF] RunHandleWinLoss: 0
04:12:36.166 [INF] CalculateResult: 20220805
04:12:36.166 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
04:16:35.124 [INF] RunScan: 0
04:16:35.125 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:16)","Data":null,"DataObj":null}
04:17:36.168 [INF] RunHandleWinLoss: 0
04:17:36.168 [INF] CalculateResult: 20220805
04:17:36.168 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
04:21:35.125 [INF] RunScan: 0
04:21:35.125 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:21)","Data":null,"DataObj":null}
04:22:36.169 [INF] RunHandleWinLoss: 0
04:22:36.169 [INF] CalculateResult: 20220805
04:22:36.169 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
04:26:35.125 [INF] RunScan: 0
04:26:35.125 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:26)","Data":null,"DataObj":null}
04:27:36.170 [INF] RunHandleWinLoss: 0
04:27:36.171 [INF] CalculateResult: 20220805
04:27:36.171 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
04:31:35.126 [INF] RunScan: 0
04:31:35.126 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:31)","Data":null,"DataObj":null}
04:32:36.172 [INF] RunHandleWinLoss: 0
04:32:36.172 [INF] CalculateResult: 20220805
04:32:36.172 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
04:36:35.126 [INF] RunScan: 0
04:36:35.126 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:36)","Data":null,"DataObj":null}
04:37:36.173 [INF] RunHandleWinLoss: 0
04:37:36.173 [INF] CalculateResult: 20220805
04:37:36.173 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
04:41:35.126 [INF] RunScan: 0
04:41:35.126 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:41)","Data":null,"DataObj":null}
04:42:36.175 [INF] RunHandleWinLoss: 0
04:42:36.175 [INF] CalculateResult: 20220805
04:42:36.175 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
04:46:35.126 [INF] RunScan: 0
04:46:35.126 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:46)","Data":null,"DataObj":null}
04:47:36.176 [INF] RunHandleWinLoss: 0
04:47:36.177 [INF] CalculateResult: 20220805
04:47:36.177 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
04:51:35.126 [INF] RunScan: 0
04:51:35.127 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:51)","Data":null,"DataObj":null}
04:52:36.178 [INF] RunHandleWinLoss: 0
04:52:36.178 [INF] CalculateResult: 20220805
04:52:36.178 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
04:56:35.127 [INF] RunScan: 0
04:56:35.127 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:56)","Data":null,"DataObj":null}
04:57:02.126 [DBG] Client connected from 127.0.0.1:55484
no ex
04:57:02.126 [DBG] 817 bytes read
no ex
04:57:02.126 [DBG] Building Hybi-14 Response
no ex
04:57:02.126 [DBG] Sent 129 bytes
no ex
04:57:02.284 [DBG] 31 bytes read
no ex
04:57:02.284 [DBG] Sent 30 bytes
no ex
04:57:02.295 [DBG] 114 bytes read
no ex
04:57:02.295 [INF] GET: http://127.0.0.1:8081/api?c=3&un=phungcu2000&pw=d35477e9a3f221592594cbfbd647d3dc&pf=web&at=
04:57:02.310 [INF] GET response: {"success":true,"message":null,"errorCode":"0","data":null,"sessionKey":"eyJuaWNrbmFtZSI6IlBodW5nY3UyMDAwNzkiLCJhdmF0YXIiOiIwIiwidmluVG90YWwiOjIzNDAsInh1VG90YWwiOjUwMDAwMCwidmlwcG9pbnQiOjAsInZpcHBvaW50U2F2ZSI6MCwiY3JlYXRlVGltZSI6IjA1LTA4LTIwMjIiLCJpcEFkZHJlc3MiOiIxMjcuMC4wLjEiLCJjZXJ0aWZpY2F0ZSI6ZmFsc2UsImx1Y2t5Um90YXRlIjowLCJkYWlMeSI6MCwibW9iaWxlU2VjdXJlIjowLCJiaXJ0aGRheSI6IiIsImFwcFNlY3VyZSI6MCwidXNlcm5hbWUiOiJQaHVuZ2N1MjAwMCIsImVtYWlsIjowLCJhZGRyZXNzIjpudWxsLCJ2ZXJpZnlNb2JpbGUiOnRydWV9","accessToken":"b9535898cd390e5b9d0925078a17f730"}

04:57:02.322 [DBG] Sent 5730 bytes
no ex
04:57:04.933 [DBG] Sent 184 bytes
no ex
04:57:05.323 [DBG] 31 bytes read
no ex
04:57:05.323 [DBG] Sent 30 bytes
no ex
04:57:08.265 [DBG] 31 bytes read
no ex
04:57:08.266 [DBG] Sent 30 bytes
no ex
04:57:09.937 [DBG] Sent 184 bytes
no ex
04:57:11.263 [DBG] 31 bytes read
no ex
04:57:11.263 [DBG] Sent 30 bytes
no ex
04:57:14.278 [DBG] 31 bytes read
no ex
04:57:14.278 [DBG] Sent 30 bytes
no ex
04:57:14.940 [DBG] Sent 184 bytes
no ex
04:57:17.312 [DBG] 31 bytes read
no ex
04:57:17.312 [DBG] Sent 30 bytes
no ex
04:57:19.938 [DBG] Sent 184 bytes
no ex
04:57:20.348 [DBG] 31 bytes read
no ex
04:57:20.348 [DBG] Sent 30 bytes
no ex
04:57:23.419 [DBG] 31 bytes read
no ex
04:57:23.419 [DBG] Sent 30 bytes
no ex
04:57:24.926 [DBG] Sent 184 bytes
no ex
04:57:26.290 [DBG] 31 bytes read
no ex
04:57:26.290 [DBG] Sent 30 bytes
no ex
04:57:29.395 [DBG] 31 bytes read
no ex
04:57:29.395 [DBG] Sent 30 bytes
no ex
04:57:29.931 [DBG] Sent 184 bytes
no ex
04:57:32.266 [DBG] 31 bytes read
no ex
04:57:32.266 [DBG] Sent 30 bytes
no ex
04:57:34.944 [DBG] Sent 184 bytes
no ex
04:57:35.280 [DBG] 31 bytes read
no ex
04:57:35.280 [DBG] Sent 30 bytes
no ex
04:57:36.180 [INF] RunHandleWinLoss: 0
04:57:36.180 [INF] CalculateResult: 20220805
04:57:36.180 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
04:57:38.269 [DBG] 31 bytes read
no ex
04:57:38.270 [DBG] Sent 30 bytes
no ex
04:57:39.942 [DBG] Sent 184 bytes
no ex
04:57:41.278 [DBG] 31 bytes read
no ex
04:57:41.278 [DBG] Sent 30 bytes
no ex
04:57:44.347 [DBG] 31 bytes read
no ex
04:57:44.347 [DBG] Sent 30 bytes
no ex
04:57:44.943 [DBG] Sent 184 bytes
no ex
04:57:47.378 [DBG] 31 bytes read
no ex
04:57:47.378 [DBG] Sent 30 bytes
no ex
04:57:49.933 [DBG] Sent 184 bytes
no ex
04:57:50.318 [DBG] 31 bytes read
no ex
04:57:50.319 [DBG] Sent 30 bytes
no ex
04:57:53.389 [DBG] 31 bytes read
no ex
04:57:53.389 [DBG] Sent 30 bytes
no ex
04:57:54.940 [DBG] Sent 184 bytes
no ex
04:57:56.285 [DBG] 31 bytes read
no ex
04:57:56.286 [DBG] Sent 30 bytes
no ex
04:57:59.264 [DBG] 31 bytes read
no ex
04:57:59.265 [DBG] Sent 30 bytes
no ex
04:57:59.932 [DBG] Sent 184 bytes
no ex
04:58:02.282 [DBG] 31 bytes read
no ex
04:58:02.283 [DBG] Sent 30 bytes
no ex
04:58:04.936 [DBG] Sent 184 bytes
no ex
04:58:05.268 [DBG] 31 bytes read
no ex
04:58:05.268 [DBG] Sent 30 bytes
no ex
04:58:08.265 [DBG] 31 bytes read
no ex
04:58:08.265 [DBG] Sent 30 bytes
no ex
04:58:09.938 [DBG] Sent 184 bytes
no ex
04:58:11.271 [DBG] 31 bytes read
no ex
04:58:11.271 [DBG] Sent 30 bytes
no ex
04:58:14.295 [DBG] 31 bytes read
no ex
04:58:14.295 [DBG] Sent 30 bytes
no ex
04:58:14.939 [DBG] Sent 184 bytes
no ex
04:58:17.302 [DBG] 31 bytes read
no ex
04:58:17.303 [DBG] Sent 30 bytes
no ex
04:58:19.938 [DBG] Sent 184 bytes
no ex
04:58:20.540 [DBG] 31 bytes read
no ex
04:58:20.540 [DBG] Sent 30 bytes
no ex
04:58:23.418 [DBG] 31 bytes read
no ex
04:58:23.418 [DBG] Sent 30 bytes
no ex
04:58:24.946 [DBG] Sent 184 bytes
no ex
04:58:26.526 [DBG] 31 bytes read
no ex
04:58:26.526 [DBG] Sent 30 bytes
no ex
04:58:29.289 [DBG] 31 bytes read
no ex
04:58:29.289 [DBG] Sent 30 bytes
no ex
04:58:29.949 [DBG] Sent 184 bytes
no ex
04:58:32.265 [DBG] 31 bytes read
no ex
04:58:32.265 [DBG] Sent 30 bytes
no ex
04:58:34.952 [DBG] Sent 184 bytes
no ex
04:58:35.331 [DBG] 31 bytes read
no ex
04:58:35.332 [DBG] Sent 30 bytes
no ex
04:58:38.337 [DBG] 31 bytes read
no ex
04:58:38.337 [DBG] Sent 30 bytes
no ex
04:58:39.937 [DBG] Sent 184 bytes
no ex
04:58:41.299 [DBG] 31 bytes read
no ex
04:58:41.300 [DBG] Sent 30 bytes
no ex
04:58:44.288 [DBG] 31 bytes read
no ex
04:58:44.288 [DBG] Sent 30 bytes
no ex
04:58:44.953 [DBG] Sent 184 bytes
no ex
04:58:46.943 [DBG] 47 bytes read
no ex
04:58:46.943 [INF] GET: http://127.0.0.1:19082/api_backend?c=8797&nn=Phungcu200079&mn=-2340&h=6c4d59a532616eec275d3ebbb66c81cb
04:58:47.392 [DBG] 31 bytes read
no ex
04:58:47.393 [DBG] Sent 30 bytes
no ex
04:58:49.865 [INF] GET response: {"success":true,"message":null,"errorCode":"0","data":"Không có nickname: Phungcu200079 hoặc tiền hoàn trả bằng 0: -2340"}

04:58:49.866 [DBG] Sent 31 bytes
no ex
04:58:49.940 [DBG] Sent 184 bytes
no ex
04:58:50.512 [DBG] 31 bytes read
no ex
04:58:50.512 [DBG] Sent 30 bytes
no ex
04:58:53.265 [DBG] 31 bytes read
no ex
04:58:53.265 [DBG] Sent 30 bytes
no ex
04:58:54.941 [DBG] Sent 184 bytes
no ex
04:58:56.267 [DBG] 31 bytes read
no ex
04:58:56.267 [DBG] Sent 30 bytes
no ex
04:58:56.757 [DBG] 31 bytes read
no ex
04:58:56.757 [DBG] Sent 30 bytes
no ex
04:58:56.893 [DBG] 31 bytes read
no ex
04:58:56.894 [DBG] Sent 30 bytes
no ex
04:58:57.021 [DBG] 31 bytes read
no ex
04:58:57.021 [DBG] Sent 30 bytes
no ex
04:58:57.187 [DBG] 124 bytes read
no ex
04:58:57.194 [DBG] Sent 35 bytes
no ex
04:58:59.266 [DBG] 31 bytes read
no ex
04:58:59.266 [DBG] Sent 30 bytes
no ex
04:58:59.950 [DBG] Sent 184 bytes
no ex
04:59:02.281 [DBG] 31 bytes read
no ex
04:59:02.281 [DBG] Sent 30 bytes
no ex
04:59:04.957 [DBG] Sent 184 bytes
no ex
04:59:05.266 [DBG] 31 bytes read
no ex
04:59:05.266 [DBG] Sent 30 bytes
no ex
04:59:08.573 [DBG] 31 bytes read
no ex
04:59:08.573 [DBG] Sent 30 bytes
no ex
04:59:09.955 [DBG] Sent 184 bytes
no ex
04:59:11.431 [DBG] 31 bytes read
no ex
04:59:11.432 [DBG] Sent 30 bytes
no ex
04:59:14.610 [DBG] 31 bytes read
no ex
04:59:14.611 [DBG] Sent 30 bytes
no ex
04:59:14.945 [DBG] Sent 184 bytes
no ex
04:59:17.448 [DBG] 31 bytes read
no ex
04:59:17.448 [DBG] Sent 30 bytes
no ex
04:59:19.944 [DBG] Sent 184 bytes
no ex
04:59:20.516 [DBG] 31 bytes read
no ex
04:59:20.516 [DBG] Sent 30 bytes
no ex
04:59:23.435 [DBG] 31 bytes read
no ex
04:59:23.435 [DBG] Sent 30 bytes
no ex
04:59:24.951 [DBG] Sent 184 bytes
no ex
04:59:26.386 [DBG] 31 bytes read
no ex
04:59:26.387 [DBG] Sent 30 bytes
no ex
04:59:29.272 [DBG] 31 bytes read
no ex
04:59:29.272 [DBG] Sent 30 bytes
no ex
04:59:29.958 [DBG] Sent 184 bytes
no ex
04:59:32.264 [DBG] 31 bytes read
no ex
04:59:32.264 [DBG] Sent 30 bytes
no ex
04:59:34.959 [DBG] Sent 184 bytes
no ex
04:59:35.270 [DBG] 31 bytes read
no ex
04:59:35.270 [DBG] Sent 30 bytes
no ex
04:59:38.264 [DBG] 31 bytes read
no ex
04:59:38.264 [DBG] Sent 30 bytes
no ex
04:59:39.951 [DBG] Sent 184 bytes
no ex
04:59:41.266 [DBG] 31 bytes read
no ex
04:59:41.266 [DBG] Sent 30 bytes
no ex
04:59:44.281 [DBG] 31 bytes read
no ex
04:59:44.281 [DBG] Sent 30 bytes
no ex
04:59:44.957 [DBG] Sent 184 bytes
no ex
04:59:47.264 [DBG] 31 bytes read
no ex
04:59:47.265 [DBG] Sent 30 bytes
no ex
04:59:48.404 [DBG] 47 bytes read
no ex
04:59:48.405 [INF] GET: http://127.0.0.1:19082/api_backend?c=8797&nn=Phungcu200079&mn=2340&h=fe32874f5670a505b4b97bc04c6fde77
04:59:49.468 [INF] GET response: {"success":true,"message":null,"errorCode":"0","data":"Không có nickname: Phungcu200079 hoặc tiền hoàn trả bằng 0: 2340"}

04:59:49.468 [DBG] Sent 29 bytes
no ex
04:59:49.962 [DBG] Sent 184 bytes
no ex
04:59:50.267 [DBG] 31 bytes read
no ex
04:59:50.267 [DBG] Sent 30 bytes
no ex
04:59:53.264 [DBG] 31 bytes read
no ex
04:59:53.265 [DBG] Sent 30 bytes
no ex
04:59:54.948 [DBG] Sent 184 bytes
no ex
04:59:56.299 [DBG] 31 bytes read
no ex
04:59:56.299 [DBG] Sent 30 bytes
no ex
04:59:59.265 [DBG] 31 bytes read
no ex
04:59:59.265 [DBG] Sent 30 bytes
no ex
04:59:59.951 [DBG] Sent 184 bytes
no ex
05:00:02.316 [DBG] 31 bytes read
no ex
05:00:02.316 [DBG] Sent 30 bytes
no ex
05:00:04.960 [DBG] Sent 184 bytes
no ex
05:00:05.428 [DBG] 31 bytes read
no ex
05:00:05.428 [DBG] Sent 30 bytes
no ex
05:00:08.518 [DBG] 31 bytes read
no ex
05:00:08.518 [DBG] Sent 30 bytes
no ex
05:00:09.948 [DBG] Sent 184 bytes
no ex
05:00:11.641 [DBG] 31 bytes read
no ex
05:00:11.641 [DBG] Sent 30 bytes
no ex
05:00:14.372 [DBG] 31 bytes read
no ex
05:00:14.372 [DBG] Sent 30 bytes
no ex
05:00:14.959 [DBG] Sent 184 bytes
no ex
05:00:17.372 [DBG] 31 bytes read
no ex
05:00:17.372 [DBG] Sent 30 bytes
no ex
05:00:19.948 [DBG] Sent 184 bytes
no ex
05:00:20.393 [DBG] 31 bytes read
no ex
05:00:20.393 [DBG] Sent 30 bytes
no ex
05:00:23.350 [DBG] 31 bytes read
no ex
05:00:23.351 [DBG] Sent 30 bytes
no ex
05:00:24.954 [DBG] Sent 184 bytes
no ex
05:00:26.410 [DBG] 31 bytes read
no ex
05:00:26.411 [DBG] Sent 30 bytes
no ex
05:00:29.395 [DBG] 31 bytes read
no ex
05:00:29.396 [DBG] Sent 30 bytes
no ex
05:00:29.961 [DBG] Sent 184 bytes
no ex
05:00:32.411 [DBG] 31 bytes read
no ex
05:00:32.412 [DBG] Sent 30 bytes
no ex
05:00:34.969 [DBG] Sent 184 bytes
no ex
05:00:35.441 [DBG] 31 bytes read
no ex
05:00:35.442 [DBG] Sent 30 bytes
no ex
05:00:38.642 [DBG] 31 bytes read
no ex
05:00:38.642 [DBG] Sent 30 bytes
no ex
05:00:39.967 [DBG] Sent 184 bytes
no ex
05:00:41.631 [DBG] 31 bytes read
no ex
05:00:41.631 [DBG] Sent 30 bytes
no ex
05:00:44.328 [DBG] 31 bytes read
no ex
05:00:44.328 [DBG] Sent 30 bytes
no ex
05:00:44.960 [DBG] Sent 184 bytes
no ex
05:00:47.448 [DBG] 31 bytes read
no ex
05:00:47.448 [DBG] Sent 30 bytes
no ex
05:00:49.961 [DBG] Sent 184 bytes
no ex
05:00:50.343 [DBG] 31 bytes read
no ex
05:00:50.343 [DBG] Sent 30 bytes
no ex
05:00:53.334 [DBG] 31 bytes read
no ex
05:00:53.334 [DBG] Sent 30 bytes
no ex
05:00:54.964 [DBG] Sent 184 bytes
no ex
05:00:56.338 [DBG] 31 bytes read
no ex
05:00:56.339 [DBG] Sent 30 bytes
no ex
05:00:59.299 [DBG] 31 bytes read
no ex
05:00:59.300 [DBG] Sent 30 bytes
no ex
05:00:59.964 [DBG] Sent 184 bytes
no ex
05:01:02.406 [DBG] 31 bytes read
no ex
05:01:02.406 [DBG] Sent 30 bytes
no ex
05:01:04.961 [DBG] Sent 184 bytes
no ex
05:01:05.317 [DBG] 31 bytes read
no ex
05:01:05.317 [DBG] Sent 30 bytes
no ex
05:01:08.395 [DBG] 31 bytes read
no ex
05:01:08.395 [DBG] Sent 30 bytes
no ex
05:01:09.967 [DBG] Sent 184 bytes
no ex
05:01:11.333 [DBG] 31 bytes read
no ex
05:01:11.333 [DBG] Sent 30 bytes
no ex
05:01:14.309 [DBG] 31 bytes read
no ex
05:01:14.310 [DBG] Sent 30 bytes
no ex
05:01:14.975 [DBG] Sent 184 bytes
no ex
05:01:17.587 [DBG] 31 bytes read
no ex
05:01:17.587 [DBG] Sent 30 bytes
no ex
05:01:19.964 [DBG] Sent 184 bytes
no ex
05:01:20.371 [DBG] 31 bytes read
no ex
05:01:20.372 [DBG] Sent 30 bytes
no ex
05:01:23.392 [DBG] 31 bytes read
no ex
05:01:23.392 [DBG] Sent 30 bytes
no ex
05:01:24.965 [DBG] Sent 184 bytes
no ex
05:01:26.518 [DBG] 31 bytes read
no ex
05:01:26.519 [DBG] Sent 30 bytes
no ex
05:01:29.305 [DBG] 31 bytes read
no ex
05:01:29.306 [DBG] Sent 30 bytes
no ex
05:01:29.970 [DBG] Sent 184 bytes
no ex
05:01:32.328 [DBG] 31 bytes read
no ex
05:01:32.329 [DBG] Sent 30 bytes
no ex
05:01:34.973 [DBG] Sent 184 bytes
no ex
05:01:35.127 [INF] RunScan: 0
05:01:35.127 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:1)","Data":null,"DataObj":null}
05:01:35.332 [DBG] 31 bytes read
no ex
05:01:35.334 [DBG] Sent 30 bytes
no ex
05:01:38.278 [DBG] 31 bytes read
no ex
05:01:38.279 [DBG] Sent 30 bytes
no ex
05:01:39.978 [DBG] Sent 184 bytes
no ex
05:01:41.335 [DBG] 31 bytes read
no ex
05:01:41.335 [DBG] Sent 30 bytes
no ex
05:01:44.336 [DBG] 31 bytes read
no ex
05:01:44.336 [DBG] Sent 30 bytes
no ex
05:01:44.975 [DBG] Sent 184 bytes
no ex
05:01:47.313 [DBG] 31 bytes read
no ex
05:01:47.313 [DBG] Sent 30 bytes
no ex
05:01:49.969 [DBG] Sent 184 bytes
no ex
05:01:50.316 [DBG] 31 bytes read
no ex
05:01:50.316 [DBG] Sent 30 bytes
no ex
05:01:53.311 [DBG] 31 bytes read
no ex
05:01:53.311 [DBG] Sent 30 bytes
no ex
05:01:54.975 [DBG] Sent 184 bytes
no ex
05:01:56.333 [DBG] 31 bytes read
no ex
05:01:56.334 [DBG] Sent 30 bytes
no ex
05:01:59.303 [DBG] 31 bytes read
no ex
05:01:59.303 [DBG] Sent 30 bytes
no ex
05:01:59.980 [DBG] Sent 184 bytes
no ex
05:02:02.460 [DBG] 31 bytes read
no ex
05:02:02.460 [DBG] Sent 30 bytes
no ex
05:02:04.969 [DBG] Sent 184 bytes
no ex
05:02:05.527 [DBG] 31 bytes read
no ex
05:02:05.527 [DBG] Sent 30 bytes
no ex
05:02:08.321 [DBG] 31 bytes read
no ex
05:02:08.321 [DBG] Sent 30 bytes
no ex
05:02:09.981 [DBG] Sent 184 bytes
no ex
05:02:11.329 [DBG] 31 bytes read
no ex
05:02:11.329 [DBG] Sent 30 bytes
no ex
05:02:14.352 [DBG] 31 bytes read
no ex
05:02:14.353 [DBG] Sent 30 bytes
no ex
05:02:14.969 [DBG] Sent 184 bytes
no ex
05:02:17.328 [DBG] 31 bytes read
no ex
05:02:17.329 [DBG] Sent 30 bytes
no ex
05:02:19.983 [DBG] Sent 184 bytes
no ex
05:02:20.321 [DBG] 31 bytes read
no ex
05:02:20.321 [DBG] Sent 30 bytes
no ex
05:02:23.324 [DBG] 31 bytes read
no ex
05:02:23.324 [DBG] Sent 30 bytes
no ex
05:02:24.969 [DBG] Sent 184 bytes
no ex
05:02:26.322 [DBG] 31 bytes read
no ex
05:02:26.322 [DBG] Sent 30 bytes
no ex
05:02:29.416 [DBG] 31 bytes read
no ex
05:02:29.416 [DBG] Sent 30 bytes
no ex
05:02:29.980 [DBG] Sent 184 bytes
no ex
05:02:32.353 [DBG] 31 bytes read
no ex
05:02:32.353 [DBG] Sent 30 bytes
no ex
05:02:34.984 [DBG] Sent 184 bytes
no ex
05:02:35.390 [DBG] 31 bytes read
no ex
05:02:35.390 [DBG] Sent 30 bytes
no ex
05:02:36.181 [INF] RunHandleWinLoss: 0
05:02:36.181 [INF] CalculateResult: 20220805
05:02:36.181 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
05:02:38.901 [DBG] 31 bytes read
no ex
05:02:38.901 [DBG] Sent 30 bytes
no ex
05:02:39.988 [DBG] Sent 184 bytes
no ex
05:02:41.395 [DBG] 31 bytes read
no ex
05:02:41.396 [DBG] Sent 30 bytes
no ex
05:02:44.525 [DBG] 31 bytes read
no ex
05:02:44.526 [DBG] Sent 30 bytes
no ex
05:02:44.982 [DBG] Sent 184 bytes
no ex
05:02:47.429 [DBG] 31 bytes read
no ex
05:02:47.430 [DBG] Sent 30 bytes
no ex
05:02:49.987 [DBG] Sent 184 bytes
no ex
05:02:50.863 [DBG] 31 bytes read
no ex
05:02:50.864 [DBG] Sent 30 bytes
no ex
05:02:53.401 [DBG] 31 bytes read
no ex
05:02:53.402 [DBG] Sent 30 bytes
no ex
05:02:54.975 [DBG] Sent 184 bytes
no ex
05:02:56.355 [DBG] 31 bytes read
no ex
05:02:56.355 [DBG] Sent 30 bytes
no ex
05:02:59.350 [DBG] 31 bytes read
no ex
05:02:59.351 [DBG] Sent 30 bytes
no ex
05:02:59.978 [DBG] Sent 184 bytes
no ex
05:03:02.467 [DBG] 32 bytes read
no ex
05:03:02.467 [DBG] Sent 31 bytes
no ex
05:03:04.989 [DBG] Sent 184 bytes
no ex
05:03:05.364 [DBG] 32 bytes read
no ex
05:03:05.364 [DBG] Sent 31 bytes
no ex
05:03:08.329 [DBG] 32 bytes read
no ex
05:03:08.330 [DBG] Sent 31 bytes
no ex
05:03:09.980 [DBG] Sent 184 bytes
no ex
05:03:11.324 [DBG] 32 bytes read
no ex
05:03:11.324 [DBG] Sent 31 bytes
no ex
05:03:14.354 [DBG] 32 bytes read
no ex
05:03:14.355 [DBG] Sent 31 bytes
no ex
05:03:14.989 [DBG] Sent 184 bytes
no ex
05:03:17.264 [DBG] 32 bytes read
no ex
05:03:17.265 [DBG] Sent 31 bytes
no ex
05:03:19.992 [DBG] Sent 184 bytes
no ex
05:03:20.273 [DBG] 32 bytes read
no ex
05:03:20.273 [DBG] Sent 31 bytes
no ex
05:03:23.315 [DBG] 32 bytes read
no ex
05:03:23.316 [DBG] Sent 31 bytes
no ex
05:03:24.979 [DBG] Sent 184 bytes
no ex
05:03:26.327 [DBG] 32 bytes read
no ex
05:03:26.327 [DBG] Sent 31 bytes
no ex
05:03:29.305 [DBG] 32 bytes read
no ex
05:03:29.306 [DBG] Sent 31 bytes
no ex
05:03:29.990 [DBG] Sent 184 bytes
no ex
05:03:32.391 [DBG] 32 bytes read
no ex
05:03:32.392 [DBG] Sent 31 bytes
no ex
05:03:34.998 [DBG] Sent 184 bytes
no ex
05:03:35.524 [DBG] 32 bytes read
no ex
05:03:35.525 [DBG] Sent 31 bytes
no ex
05:03:38.390 [DBG] 32 bytes read
no ex
05:03:38.390 [DBG] Sent 31 bytes
no ex
05:03:40.000 [DBG] Sent 184 bytes
no ex
05:03:41.359 [DBG] 32 bytes read
no ex
05:03:41.359 [DBG] Sent 31 bytes
no ex
05:03:44.358 [DBG] 32 bytes read
no ex
05:03:44.358 [DBG] Sent 31 bytes
no ex
05:03:45.004 [DBG] Sent 184 bytes
no ex
05:03:47.422 [DBG] 32 bytes read
no ex
05:03:47.422 [DBG] Sent 31 bytes
no ex
05:03:49.990 [DBG] Sent 184 bytes
no ex
05:03:50.440 [DBG] 32 bytes read
no ex
05:03:50.440 [DBG] Sent 31 bytes
no ex
05:03:53.379 [DBG] 32 bytes read
no ex
05:03:53.379 [DBG] Sent 31 bytes
no ex
05:03:54.995 [DBG] Sent 184 bytes
no ex
05:03:56.331 [DBG] 32 bytes read
no ex
05:03:56.331 [DBG] Sent 31 bytes
no ex
05:03:59.459 [DBG] 32 bytes read
no ex
05:03:59.459 [DBG] Sent 31 bytes
no ex
05:03:59.998 [DBG] Sent 184 bytes
no ex
05:04:02.515 [DBG] 32 bytes read
no ex
05:04:02.515 [DBG] Sent 31 bytes
no ex
05:04:04.993 [DBG] Sent 184 bytes
no ex
05:04:05.334 [DBG] 32 bytes read
no ex
05:04:05.335 [DBG] Sent 31 bytes
no ex
05:04:08.322 [DBG] 32 bytes read
no ex
05:04:08.322 [DBG] Sent 31 bytes
no ex
05:04:09.995 [DBG] Sent 184 bytes
no ex
05:04:11.287 [DBG] 32 bytes read
no ex
05:04:11.287 [DBG] Sent 31 bytes
no ex
05:04:14.340 [DBG] 32 bytes read
no ex
05:04:14.341 [DBG] Sent 31 bytes
no ex
05:04:14.992 [DBG] Sent 184 bytes
no ex
05:04:17.331 [DBG] 32 bytes read
no ex
05:04:17.331 [DBG] Sent 31 bytes
no ex
05:04:19.998 [DBG] Sent 184 bytes
no ex
05:04:20.309 [DBG] 32 bytes read
no ex
05:04:20.309 [DBG] Sent 31 bytes
no ex
05:04:23.338 [DBG] 32 bytes read
no ex
05:04:23.338 [DBG] Sent 31 bytes
no ex
05:04:25.003 [DBG] Sent 184 bytes
no ex
05:04:26.352 [DBG] 32 bytes read
no ex
05:04:26.352 [DBG] Sent 31 bytes
no ex
05:04:29.282 [DBG] 32 bytes read
no ex
05:04:29.282 [DBG] Sent 31 bytes
no ex
05:04:30.002 [DBG] Sent 184 bytes
no ex
05:04:32.316 [DBG] 32 bytes read
no ex
05:04:32.316 [DBG] Sent 31 bytes
no ex
05:04:35.007 [DBG] Sent 184 bytes
no ex
05:04:35.263 [DBG] 32 bytes read
no ex
05:04:35.263 [DBG] Sent 31 bytes
no ex
05:04:38.281 [DBG] 32 bytes read
no ex
05:04:38.282 [DBG] Sent 31 bytes
no ex
05:04:39.996 [DBG] Sent 184 bytes
no ex
05:04:41.316 [DBG] 32 bytes read
no ex
05:04:41.317 [DBG] Sent 31 bytes
no ex
05:04:44.322 [DBG] 32 bytes read
no ex
05:04:44.323 [DBG] Sent 31 bytes
no ex
05:04:45.001 [DBG] Sent 184 bytes
no ex
05:04:47.301 [DBG] 32 bytes read
no ex
05:04:47.302 [DBG] Sent 31 bytes
no ex
05:04:50.008 [DBG] Sent 184 bytes
no ex
05:04:50.351 [DBG] 32 bytes read
no ex
05:04:50.351 [DBG] Sent 31 bytes
no ex
05:04:53.313 [DBG] 32 bytes read
no ex
05:04:53.314 [DBG] Sent 31 bytes
no ex
05:04:54.997 [DBG] Sent 184 bytes
no ex
05:04:56.339 [DBG] 32 bytes read
no ex
05:04:56.339 [DBG] Sent 31 bytes
no ex
05:04:59.310 [DBG] 32 bytes read
no ex
05:04:59.310 [DBG] Sent 31 bytes
no ex
05:05:00.013 [DBG] Sent 184 bytes
no ex
05:05:02.339 [DBG] 32 bytes read
no ex
05:05:02.339 [DBG] Sent 31 bytes
no ex
05:05:04.009 [DBG] Sent 70 bytes
no ex
05:05:05.001 [DBG] Sent 184 bytes
no ex
05:05:05.379 [DBG] 32 bytes read
no ex
05:05:05.379 [DBG] Sent 31 bytes
no ex
05:05:08.285 [DBG] 32 bytes read
no ex
05:05:08.285 [DBG] Sent 31 bytes
no ex
05:05:10.011 [DBG] Sent 184 bytes
no ex
05:05:11.318 [DBG] 32 bytes read
no ex
05:05:11.318 [DBG] Sent 31 bytes
no ex
05:05:14.282 [DBG] 32 bytes read
no ex
05:05:14.282 [DBG] Sent 31 bytes
no ex
05:05:15.000 [DBG] Sent 184 bytes
no ex
05:05:17.402 [DBG] 32 bytes read
no ex
05:05:17.403 [DBG] Sent 31 bytes
no ex
05:05:20.012 [DBG] Sent 184 bytes
no ex
05:05:20.517 [DBG] 32 bytes read
no ex
05:05:20.517 [DBG] Sent 31 bytes
no ex
05:05:23.315 [DBG] 32 bytes read
no ex
05:05:23.316 [DBG] Sent 31 bytes
no ex
05:05:25.001 [DBG] Sent 184 bytes
no ex
05:05:26.383 [DBG] 32 bytes read
no ex
05:05:26.383 [DBG] Sent 31 bytes
no ex
05:05:29.267 [DBG] 32 bytes read
no ex
05:05:29.267 [DBG] Sent 31 bytes
no ex
05:05:30.004 [DBG] Sent 184 bytes
no ex
05:05:32.275 [DBG] 32 bytes read
no ex
05:05:32.275 [DBG] Sent 31 bytes
no ex
05:05:35.005 [DBG] Sent 184 bytes
no ex
05:05:35.263 [DBG] 32 bytes read
no ex
05:05:35.263 [DBG] Sent 31 bytes
no ex
05:05:38.362 [DBG] 32 bytes read
no ex
05:05:38.362 [DBG] Sent 31 bytes
no ex
05:05:40.011 [DBG] Sent 184 bytes
no ex
05:05:41.455 [DBG] 32 bytes read
no ex
05:05:41.455 [DBG] Sent 31 bytes
no ex
05:05:44.498 [DBG] 32 bytes read
no ex
05:05:44.498 [DBG] Sent 31 bytes
no ex
05:05:45.014 [DBG] Sent 184 bytes
no ex
05:05:47.498 [DBG] 32 bytes read
no ex
05:05:47.498 [DBG] Sent 31 bytes
no ex
05:05:50.015 [DBG] Sent 184 bytes
no ex
05:05:50.557 [DBG] 32 bytes read
no ex
05:05:50.558 [DBG] Sent 31 bytes
no ex
05:05:53.406 [DBG] 32 bytes read
no ex
05:05:53.406 [DBG] Sent 31 bytes
no ex
05:05:55.020 [DBG] Sent 184 bytes
no ex
05:05:56.403 [DBG] 32 bytes read
no ex
05:05:56.403 [DBG] Sent 31 bytes
no ex
05:05:59.369 [DBG] 32 bytes read
no ex
05:05:59.370 [DBG] Sent 31 bytes
no ex
05:06:00.013 [DBG] Sent 184 bytes
no ex
05:06:02.650 [DBG] 32 bytes read
no ex
05:06:02.650 [DBG] Sent 31 bytes
no ex
05:06:05.022 [DBG] Sent 184 bytes
no ex
05:06:05.528 [DBG] 32 bytes read
no ex
05:06:05.528 [DBG] Sent 31 bytes
no ex
05:06:08.282 [DBG] 32 bytes read
no ex
05:06:08.282 [DBG] Sent 31 bytes
no ex
05:06:10.011 [DBG] Sent 184 bytes
no ex
05:06:11.304 [DBG] 32 bytes read
no ex
05:06:11.304 [DBG] Sent 31 bytes
no ex
05:06:14.358 [DBG] 32 bytes read
no ex
05:06:14.358 [DBG] Sent 31 bytes
no ex
05:06:15.022 [DBG] Sent 184 bytes
no ex
05:06:17.266 [DBG] 32 bytes read
no ex
05:06:17.266 [DBG] Sent 31 bytes
no ex
05:06:20.015 [DBG] Sent 184 bytes
no ex
05:06:20.306 [DBG] 32 bytes read
no ex
05:06:20.307 [DBG] Sent 31 bytes
no ex
05:06:23.401 [DBG] 32 bytes read
no ex
05:06:23.401 [DBG] Sent 31 bytes
no ex
05:06:25.020 [DBG] Sent 184 bytes
no ex
05:06:26.400 [DBG] 32 bytes read
no ex
05:06:26.400 [DBG] Sent 31 bytes
no ex
05:06:29.394 [DBG] 32 bytes read
no ex
05:06:29.394 [DBG] Sent 31 bytes
no ex
05:06:30.024 [DBG] Sent 184 bytes
no ex
05:06:32.428 [DBG] 32 bytes read
no ex
05:06:32.429 [DBG] Sent 31 bytes
no ex
05:06:35.024 [DBG] Sent 184 bytes
no ex
05:06:35.127 [INF] RunScan: 0
05:06:35.127 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:6)","Data":null,"DataObj":null}
05:06:35.484 [DBG] 32 bytes read
no ex
05:06:35.486 [DBG] Sent 31 bytes
no ex
05:06:38.536 [DBG] 32 bytes read
no ex
05:06:38.537 [DBG] Sent 31 bytes
no ex
05:06:40.015 [DBG] Sent 184 bytes
no ex
05:06:41.317 [DBG] 32 bytes read
no ex
05:06:41.318 [DBG] Sent 31 bytes
no ex
05:06:44.270 [DBG] 32 bytes read
no ex
05:06:44.270 [DBG] Sent 31 bytes
no ex
05:06:45.020 [DBG] Sent 184 bytes
no ex
05:06:47.331 [DBG] 32 bytes read
no ex
05:06:47.331 [DBG] Sent 31 bytes
no ex
05:06:50.022 [DBG] Sent 184 bytes
no ex
05:06:50.328 [DBG] 32 bytes read
no ex
05:06:50.328 [DBG] Sent 31 bytes
no ex
05:06:53.321 [DBG] 32 bytes read
no ex
05:06:53.321 [DBG] Sent 31 bytes
no ex
05:06:55.024 [DBG] Sent 184 bytes
no ex
05:06:56.341 [DBG] 32 bytes read
no ex
05:06:56.341 [DBG] Sent 31 bytes
no ex
05:06:59.481 [DBG] 32 bytes read
no ex
05:06:59.481 [DBG] Sent 31 bytes
no ex
05:07:00.027 [DBG] Sent 184 bytes
no ex
05:07:02.402 [DBG] 32 bytes read
no ex
05:07:02.402 [DBG] Sent 31 bytes
no ex
05:07:05.030 [DBG] Sent 184 bytes
no ex
05:07:05.637 [DBG] 32 bytes read
no ex
05:07:05.638 [DBG] Sent 31 bytes
no ex
05:07:08.387 [DBG] 32 bytes read
no ex
05:07:08.388 [DBG] Sent 31 bytes
no ex
05:07:10.025 [DBG] Sent 184 bytes
no ex
05:07:10.025 [DBG] Sent 74 bytes
no ex
05:07:11.016 [DBG] Sent 69 bytes
no ex
05:07:11.287 [DBG] 32 bytes read
no ex
05:07:11.288 [DBG] Sent 31 bytes
no ex
05:07:14.321 [DBG] 32 bytes read
no ex
05:07:14.322 [DBG] Sent 31 bytes
no ex
05:07:15.016 [DBG] Sent 184 bytes
no ex
05:07:15.016 [DBG] Sent 75 bytes
no ex
05:07:17.334 [DBG] 32 bytes read
no ex
05:07:17.334 [DBG] Sent 31 bytes
no ex
05:07:20.017 [DBG] Sent 184 bytes
no ex
05:07:20.361 [DBG] 32 bytes read
no ex
05:07:20.361 [DBG] Sent 31 bytes
no ex
05:07:23.466 [DBG] 32 bytes read
no ex
05:07:23.467 [DBG] Sent 31 bytes
no ex
05:07:25.028 [DBG] Sent 184 bytes
no ex
05:07:26.774 [DBG] 32 bytes read
no ex
05:07:26.774 [DBG] Sent 31 bytes
no ex
05:07:29.306 [DBG] 32 bytes read
no ex
05:07:29.306 [DBG] Sent 31 bytes
no ex
05:07:30.031 [DBG] Sent 184 bytes
no ex
05:07:32.326 [DBG] 32 bytes read
no ex
05:07:32.328 [DBG] Sent 31 bytes
no ex
05:07:35.021 [DBG] Sent 184 bytes
no ex
05:07:35.264 [DBG] 32 bytes read
no ex
05:07:35.264 [DBG] Sent 31 bytes
no ex
05:07:36.182 [INF] RunHandleWinLoss: 0
05:07:36.182 [INF] CalculateResult: 20220805
05:07:36.182 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
05:07:38.341 [DBG] 32 bytes read
no ex
05:07:38.341 [DBG] Sent 31 bytes
no ex
05:07:40.032 [DBG] Sent 184 bytes
no ex
05:07:41.340 [DBG] 32 bytes read
no ex
05:07:41.340 [DBG] Sent 31 bytes
no ex
05:07:44.291 [DBG] 32 bytes read
no ex
05:07:44.291 [DBG] Sent 31 bytes
no ex
05:07:45.036 [DBG] Sent 184 bytes
no ex
05:07:47.326 [DBG] 32 bytes read
no ex
05:07:47.326 [DBG] Sent 31 bytes
no ex
05:07:50.022 [DBG] Sent 184 bytes
no ex
05:07:50.351 [DBG] 32 bytes read
no ex
05:07:50.352 [DBG] Sent 31 bytes
no ex
05:07:53.397 [DBG] 32 bytes read
no ex
05:07:53.397 [DBG] Sent 31 bytes
no ex
05:07:55.028 [DBG] Sent 184 bytes
no ex
05:07:56.517 [DBG] 32 bytes read
no ex
05:07:56.517 [DBG] Sent 31 bytes
no ex
05:07:59.336 [DBG] 32 bytes read
no ex
05:07:59.336 [DBG] Sent 31 bytes
no ex
05:08:00.030 [DBG] Sent 184 bytes
no ex
05:08:02.335 [DBG] 32 bytes read
no ex
05:08:02.335 [DBG] Sent 31 bytes
no ex
05:08:05.033 [DBG] Sent 184 bytes
no ex
05:08:05.269 [DBG] 32 bytes read
no ex
05:08:05.269 [DBG] Sent 31 bytes
no ex
05:08:08.341 [DBG] 32 bytes read
no ex
05:08:08.342 [DBG] Sent 31 bytes
no ex
05:08:10.036 [DBG] Sent 184 bytes
no ex
05:08:11.306 [DBG] 32 bytes read
no ex
05:08:11.306 [DBG] Sent 31 bytes
no ex
05:08:14.311 [DBG] 32 bytes read
no ex
05:08:14.311 [DBG] Sent 31 bytes
no ex
05:08:15.027 [DBG] Sent 184 bytes
no ex
05:08:17.340 [DBG] 32 bytes read
no ex
05:08:17.341 [DBG] Sent 31 bytes
no ex
05:08:20.036 [DBG] Sent 184 bytes
no ex
05:08:20.370 [DBG] 32 bytes read
no ex
05:08:20.370 [DBG] Sent 31 bytes
no ex
05:08:23.329 [DBG] 32 bytes read
no ex
05:08:23.329 [DBG] Sent 31 bytes
no ex
05:08:25.038 [DBG] Sent 184 bytes
no ex
05:08:26.337 [DBG] 32 bytes read
no ex
05:08:26.337 [DBG] Sent 31 bytes
no ex
05:08:29.350 [DBG] 32 bytes read
no ex
05:08:29.351 [DBG] Sent 31 bytes
no ex
05:08:30.036 [DBG] Sent 184 bytes
no ex
05:08:32.328 [DBG] 32 bytes read
no ex
05:08:32.328 [DBG] Sent 31 bytes
no ex
05:08:35.043 [DBG] Sent 184 bytes
no ex
05:08:35.351 [DBG] 32 bytes read
no ex
05:08:35.352 [DBG] Sent 31 bytes
no ex
05:08:38.346 [DBG] 32 bytes read
no ex
05:08:38.346 [DBG] Sent 31 bytes
no ex
05:08:40.031 [DBG] Sent 184 bytes
no ex
05:08:41.443 [DBG] 32 bytes read
no ex
05:08:41.443 [DBG] Sent 31 bytes
no ex
05:08:44.302 [DBG] 32 bytes read
no ex
05:08:44.302 [DBG] Sent 31 bytes
no ex
05:08:45.034 [DBG] Sent 184 bytes
no ex
05:08:47.364 [DBG] 32 bytes read
no ex
05:08:47.364 [DBG] Sent 31 bytes
no ex
05:08:50.036 [DBG] Sent 184 bytes
no ex
05:08:50.392 [DBG] 32 bytes read
no ex
05:08:50.393 [DBG] Sent 31 bytes
no ex
05:08:53.371 [DBG] 32 bytes read
no ex
05:08:53.371 [DBG] Sent 31 bytes
no ex
05:08:55.042 [DBG] Sent 184 bytes
no ex
05:08:56.302 [DBG] 32 bytes read
no ex
05:08:56.302 [DBG] Sent 31 bytes
no ex
05:08:59.267 [DBG] 32 bytes read
no ex
05:08:59.267 [DBG] Sent 31 bytes
no ex
05:09:00.030 [DBG] Sent 184 bytes
no ex
05:09:02.365 [DBG] 32 bytes read
no ex
05:09:02.365 [DBG] Sent 31 bytes
no ex
05:09:05.043 [DBG] Sent 184 bytes
no ex
05:09:05.353 [DBG] 32 bytes read
no ex
05:09:05.353 [DBG] Sent 31 bytes
no ex
05:09:08.267 [DBG] 32 bytes read
no ex
05:09:08.267 [DBG] Sent 31 bytes
no ex
05:09:10.031 [DBG] Sent 184 bytes
no ex
05:09:11.333 [DBG] 32 bytes read
no ex
05:09:11.333 [DBG] Sent 31 bytes
no ex
05:09:14.267 [DBG] 32 bytes read
no ex
05:09:14.267 [DBG] Sent 31 bytes
no ex
05:09:15.041 [DBG] Sent 184 bytes
no ex
05:09:17.334 [DBG] 32 bytes read
no ex
05:09:17.334 [DBG] Sent 31 bytes
no ex
05:09:20.050 [DBG] Sent 184 bytes
no ex
05:09:20.333 [DBG] 32 bytes read
no ex
05:09:20.334 [DBG] Sent 31 bytes
no ex
05:09:23.365 [DBG] 32 bytes read
no ex
05:09:23.365 [DBG] Sent 31 bytes
no ex
05:09:25.033 [DBG] Sent 184 bytes
no ex
05:09:26.359 [DBG] 33 bytes read
no ex
05:09:26.360 [DBG] Sent 32 bytes
no ex
05:09:29.336 [DBG] 33 bytes read
no ex
05:09:29.336 [DBG] Sent 32 bytes
no ex
05:09:30.035 [DBG] Sent 184 bytes
no ex
05:09:32.326 [DBG] 33 bytes read
no ex
05:09:32.326 [DBG] Sent 32 bytes
no ex
05:09:35.042 [DBG] Sent 184 bytes
no ex
05:09:35.301 [DBG] 33 bytes read
no ex
05:09:35.301 [DBG] Sent 32 bytes
no ex
05:09:38.342 [DBG] 33 bytes read
no ex
05:09:38.343 [DBG] Sent 32 bytes
no ex
05:09:40.046 [DBG] Sent 184 bytes
no ex
05:09:41.353 [DBG] 33 bytes read
no ex
05:09:41.353 [DBG] Sent 32 bytes
no ex
05:09:44.272 [DBG] 33 bytes read
no ex
05:09:44.273 [DBG] Sent 32 bytes
no ex
05:09:45.038 [DBG] Sent 184 bytes
no ex
05:09:47.309 [DBG] 33 bytes read
no ex
05:09:47.310 [DBG] Sent 32 bytes
no ex
05:09:50.039 [DBG] Sent 184 bytes
no ex
05:09:50.328 [DBG] 33 bytes read
no ex
05:09:50.329 [DBG] Sent 32 bytes
no ex
05:09:53.316 [DBG] 33 bytes read
no ex
05:09:53.316 [DBG] Sent 32 bytes
no ex
05:09:55.037 [DBG] Sent 184 bytes
no ex
05:09:56.318 [DBG] 33 bytes read
no ex
05:09:56.318 [DBG] Sent 32 bytes
no ex
05:09:59.444 [DBG] 33 bytes read
no ex
05:09:59.444 [DBG] Sent 32 bytes
no ex
05:10:00.043 [DBG] Sent 184 bytes
no ex
05:10:03.122 [DBG] 33 bytes read
no ex
05:10:03.122 [DBG] Sent 32 bytes
no ex
05:10:05.049 [DBG] Sent 184 bytes
no ex
05:10:05.356 [DBG] 33 bytes read
no ex
05:10:05.356 [DBG] Sent 32 bytes
no ex
05:10:08.332 [DBG] 33 bytes read
no ex
05:10:08.333 [DBG] Sent 32 bytes
no ex
05:10:10.039 [DBG] Sent 184 bytes
no ex
05:10:11.265 [DBG] 33 bytes read
no ex
05:10:11.265 [DBG] Sent 32 bytes
no ex
05:10:14.282 [DBG] 33 bytes read
no ex
05:10:14.282 [DBG] Sent 32 bytes
no ex
05:10:15.040 [DBG] Sent 184 bytes
no ex
05:10:17.265 [DBG] 33 bytes read
no ex
05:10:17.265 [DBG] Sent 32 bytes
no ex
05:10:20.048 [DBG] Sent 184 bytes
no ex
05:10:20.326 [DBG] 33 bytes read
no ex
05:10:20.326 [DBG] Sent 32 bytes
no ex
05:10:23.291 [DBG] 33 bytes read
no ex
05:10:23.291 [DBG] Sent 32 bytes
no ex
05:10:25.042 [DBG] Sent 184 bytes
no ex
05:10:26.267 [DBG] 33 bytes read
no ex
05:10:26.267 [DBG] Sent 32 bytes
no ex
05:10:29.280 [DBG] 33 bytes read
no ex
05:10:29.280 [DBG] Sent 32 bytes
no ex
05:10:30.042 [DBG] Sent 184 bytes
no ex
05:10:32.345 [DBG] 33 bytes read
no ex
05:10:32.346 [DBG] Sent 32 bytes
no ex
05:10:35.046 [DBG] Sent 184 bytes
no ex
05:10:35.274 [DBG] 33 bytes read
no ex
05:10:35.274 [DBG] Sent 32 bytes
no ex
05:10:38.265 [DBG] 33 bytes read
no ex
05:10:38.265 [DBG] Sent 32 bytes
no ex
05:10:40.055 [DBG] Sent 184 bytes
no ex
05:10:41.266 [DBG] 33 bytes read
no ex
05:10:41.266 [DBG] Sent 32 bytes
no ex
05:10:44.349 [DBG] 33 bytes read
no ex
05:10:44.349 [DBG] Sent 32 bytes
no ex
05:10:45.057 [DBG] Sent 184 bytes
no ex
05:10:47.380 [DBG] 33 bytes read
no ex
05:10:47.381 [DBG] Sent 32 bytes
no ex
05:10:49.055 [DBG] Sent 76 bytes
no ex
05:10:50.047 [DBG] Sent 184 bytes
no ex
05:10:50.470 [DBG] 33 bytes read
no ex
05:10:50.470 [DBG] Sent 32 bytes
no ex
05:10:53.419 [DBG] 33 bytes read
no ex
05:10:53.420 [DBG] Sent 32 bytes
no ex
05:10:55.055 [DBG] Sent 184 bytes
no ex
05:10:56.332 [DBG] 33 bytes read
no ex
05:10:56.332 [DBG] Sent 32 bytes
no ex
05:10:59.273 [DBG] 33 bytes read
no ex
05:10:59.273 [DBG] Sent 32 bytes
no ex
05:11:00.047 [DBG] Sent 72 bytes
no ex
05:11:00.047 [DBG] Sent 184 bytes
no ex
05:11:02.449 [DBG] 33 bytes read
no ex
05:11:02.449 [DBG] Sent 32 bytes
no ex
05:11:05.051 [DBG] Sent 184 bytes
no ex
05:11:05.392 [DBG] 33 bytes read
no ex
05:11:05.392 [DBG] Sent 32 bytes
no ex
05:11:08.342 [DBG] 33 bytes read
no ex
05:11:08.342 [DBG] Sent 32 bytes
no ex
05:11:10.052 [DBG] Sent 184 bytes
no ex
05:11:11.355 [DBG] 33 bytes read
no ex
05:11:11.355 [DBG] Sent 32 bytes
no ex
05:11:13.058 [DBG] Sent 73 bytes
no ex
05:11:14.349 [DBG] 33 bytes read
no ex
05:11:14.349 [DBG] Sent 32 bytes
no ex
05:11:15.059 [DBG] Sent 184 bytes
no ex
05:11:17.443 [DBG] 33 bytes read
no ex
05:11:17.443 [DBG] Sent 32 bytes
no ex
05:11:20.054 [DBG] Sent 184 bytes
no ex
05:11:20.352 [DBG] 33 bytes read
no ex
05:11:20.352 [DBG] Sent 32 bytes
no ex
05:11:23.339 [DBG] 33 bytes read
no ex
05:11:23.339 [DBG] Sent 32 bytes
no ex
05:11:25.057 [DBG] Sent 184 bytes
no ex
05:11:26.266 [DBG] 33 bytes read
no ex
05:11:26.266 [DBG] Sent 32 bytes
no ex
05:11:29.287 [DBG] 33 bytes read
no ex
05:11:29.287 [DBG] Sent 32 bytes
no ex
05:11:30.050 [DBG] Sent 184 bytes
no ex
05:11:32.330 [DBG] 33 bytes read
no ex
05:11:32.331 [DBG] Sent 32 bytes
no ex
05:11:35.058 [DBG] Sent 184 bytes
no ex
05:11:35.127 [INF] RunScan: 0
05:11:35.127 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:11)","Data":null,"DataObj":null}
05:11:35.279 [DBG] 33 bytes read
no ex
05:11:35.279 [DBG] Sent 32 bytes
no ex
05:11:38.351 [DBG] 33 bytes read
no ex
05:11:38.351 [DBG] Sent 32 bytes
no ex
05:11:40.062 [DBG] Sent 184 bytes
no ex
05:11:41.285 [DBG] 33 bytes read
no ex
05:11:41.285 [DBG] Sent 32 bytes
no ex
05:11:44.348 [DBG] 33 bytes read
no ex
05:11:44.349 [DBG] Sent 32 bytes
no ex
05:11:45.065 [DBG] Sent 184 bytes
no ex
05:11:47.342 [DBG] 33 bytes read
no ex
05:11:47.342 [DBG] Sent 32 bytes
no ex
05:11:50.061 [DBG] Sent 184 bytes
no ex
05:11:50.286 [DBG] 33 bytes read
no ex
05:11:50.286 [DBG] Sent 32 bytes
no ex
05:11:53.332 [DBG] 33 bytes read
no ex
05:11:53.332 [DBG] Sent 32 bytes
no ex
05:11:55.049 [DBG] Sent 184 bytes
no ex
05:11:56.306 [DBG] 33 bytes read
no ex
05:11:56.307 [DBG] Sent 32 bytes
no ex
05:11:59.351 [DBG] 33 bytes read
no ex
05:11:59.351 [DBG] Sent 32 bytes
no ex
05:12:00.050 [DBG] Sent 184 bytes
no ex
05:12:02.052 [DBG] Sent 70 bytes
no ex
05:12:02.418 [DBG] 33 bytes read
no ex
05:12:02.419 [DBG] Sent 32 bytes
no ex
05:12:05.056 [DBG] Sent 184 bytes
no ex
05:12:05.988 [DBG] 33 bytes read
no ex
05:12:05.988 [DBG] Sent 32 bytes
no ex
05:12:08.539 [DBG] 33 bytes read
no ex
05:12:08.540 [DBG] Sent 32 bytes
no ex
05:12:10.062 [DBG] Sent 184 bytes
no ex
05:12:12.257 [DBG] 33 bytes read
no ex
05:12:12.257 [DBG] Sent 32 bytes
no ex
05:12:14.412 [DBG] 33 bytes read
no ex
05:12:14.412 [DBG] Sent 32 bytes
no ex
05:12:15.066 [DBG] Sent 184 bytes
no ex
05:12:17.365 [DBG] 33 bytes read
no ex
05:12:17.365 [DBG] Sent 32 bytes
no ex
05:12:20.059 [DBG] Sent 184 bytes
no ex
05:12:20.417 [DBG] 33 bytes read
no ex
05:12:20.417 [DBG] Sent 32 bytes
no ex
05:12:23.622 [DBG] 33 bytes read
no ex
05:12:23.622 [DBG] Sent 32 bytes
no ex
05:12:25.065 [DBG] Sent 184 bytes
no ex
05:12:26.832 [DBG] 33 bytes read
no ex
05:12:26.832 [DBG] Sent 32 bytes
no ex
05:12:29.303 [DBG] 33 bytes read
no ex
05:12:29.303 [DBG] Sent 32 bytes
no ex
05:12:30.056 [DBG] Sent 184 bytes
no ex
05:12:32.325 [DBG] 33 bytes read
no ex
05:12:32.325 [DBG] Sent 32 bytes
no ex
05:12:35.061 [DBG] Sent 184 bytes
no ex
05:12:35.441 [DBG] 33 bytes read
no ex
05:12:35.441 [DBG] Sent 32 bytes
no ex
05:12:36.184 [INF] RunHandleWinLoss: 0
05:12:36.185 [INF] CalculateResult: 20220805
05:12:36.185 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
05:12:38.266 [DBG] 33 bytes read
no ex
05:12:38.266 [DBG] Sent 32 bytes
no ex
05:12:40.064 [DBG] Sent 184 bytes
no ex
05:12:41.291 [DBG] 33 bytes read
no ex
05:12:41.291 [DBG] Sent 32 bytes
no ex
05:12:44.323 [DBG] 33 bytes read
no ex
05:12:44.323 [DBG] Sent 32 bytes
no ex
05:12:45.058 [DBG] Sent 184 bytes
no ex
05:12:47.324 [DBG] 33 bytes read
no ex
05:12:47.324 [DBG] Sent 32 bytes
no ex
05:12:50.061 [DBG] Sent 184 bytes
no ex
05:12:50.280 [DBG] 33 bytes read
no ex
05:12:50.280 [DBG] Sent 32 bytes
no ex
05:12:53.264 [DBG] 33 bytes read
no ex
05:12:53.264 [DBG] Sent 32 bytes
no ex
05:12:55.066 [DBG] Sent 184 bytes
no ex
05:12:56.342 [DBG] 33 bytes read
no ex
05:12:56.342 [DBG] Sent 32 bytes
no ex
05:12:59.265 [DBG] 33 bytes read
no ex
05:12:59.265 [DBG] Sent 32 bytes
no ex
05:13:00.069 [DBG] Sent 184 bytes
no ex
05:13:02.325 [DBG] 33 bytes read
no ex
05:13:02.325 [DBG] Sent 32 bytes
no ex
05:13:05.061 [DBG] Sent 184 bytes
no ex
05:13:05.339 [DBG] 33 bytes read
no ex
05:13:05.339 [DBG] Sent 32 bytes
no ex
05:13:08.346 [DBG] 33 bytes read
no ex
05:13:08.346 [DBG] Sent 32 bytes
no ex
05:13:10.071 [DBG] Sent 184 bytes
no ex
05:13:11.315 [DBG] 33 bytes read
no ex
05:13:11.316 [DBG] Sent 32 bytes
no ex
05:13:14.371 [DBG] 33 bytes read
no ex
05:13:14.371 [DBG] Sent 32 bytes
no ex
05:13:15.058 [DBG] Sent 184 bytes
no ex
05:13:17.266 [DBG] 33 bytes read
no ex
05:13:17.267 [DBG] Sent 32 bytes
no ex
05:13:20.065 [DBG] Sent 184 bytes
no ex
05:13:20.329 [DBG] 33 bytes read
no ex
05:13:20.329 [DBG] Sent 32 bytes
no ex
05:13:23.299 [DBG] 33 bytes read
no ex
05:13:23.299 [DBG] Sent 32 bytes
no ex
05:13:25.069 [DBG] Sent 184 bytes
no ex
05:13:26.269 [DBG] 33 bytes read
no ex
05:13:26.269 [DBG] Sent 32 bytes
no ex
05:13:29.326 [DBG] 33 bytes read
no ex
05:13:29.326 [DBG] Sent 32 bytes
no ex
05:13:30.073 [DBG] Sent 184 bytes
no ex
05:13:32.274 [DBG] 33 bytes read
no ex
05:13:32.274 [DBG] Sent 32 bytes
no ex
05:13:35.071 [DBG] Sent 184 bytes
no ex
05:13:35.269 [DBG] 33 bytes read
no ex
05:13:35.269 [DBG] Sent 32 bytes
no ex
05:13:38.322 [DBG] 33 bytes read
no ex
05:13:38.322 [DBG] Sent 32 bytes
no ex
05:13:40.062 [DBG] Sent 184 bytes
no ex
05:13:41.288 [DBG] 33 bytes read
no ex
05:13:41.288 [DBG] Sent 32 bytes
no ex
05:13:44.353 [DBG] 33 bytes read
no ex
05:13:44.354 [DBG] Sent 32 bytes
no ex
05:13:45.069 [DBG] Sent 184 bytes
no ex
05:13:47.338 [DBG] 33 bytes read
no ex
05:13:47.338 [DBG] Sent 32 bytes
no ex
05:13:50.072 [DBG] Sent 184 bytes
no ex
05:13:50.350 [DBG] 33 bytes read
no ex
05:13:50.350 [DBG] Sent 32 bytes
no ex
05:13:53.439 [DBG] 33 bytes read
no ex
05:13:53.439 [DBG] Sent 32 bytes
no ex
05:13:55.079 [DBG] Sent 184 bytes
no ex
05:13:56.356 [DBG] 33 bytes read
no ex
05:13:56.357 [DBG] Sent 32 bytes
no ex
05:13:59.329 [DBG] 33 bytes read
no ex
05:13:59.329 [DBG] Sent 32 bytes
no ex
05:14:00.071 [DBG] Sent 184 bytes
no ex
05:14:02.408 [DBG] 33 bytes read
no ex
05:14:02.408 [DBG] Sent 32 bytes
no ex
05:14:05.078 [DBG] Sent 184 bytes
no ex
05:14:05.327 [DBG] 33 bytes read
no ex
05:14:05.327 [DBG] Sent 32 bytes
no ex
05:14:08.316 [DBG] 33 bytes read
no ex
05:14:08.317 [DBG] Sent 32 bytes
no ex
05:14:10.065 [DBG] Sent 184 bytes
no ex
05:14:11.375 [DBG] 33 bytes read
no ex
05:14:11.375 [DBG] Sent 32 bytes
no ex
05:14:14.381 [DBG] 33 bytes read
no ex
05:14:14.381 [DBG] Sent 32 bytes
no ex
05:14:15.069 [DBG] Sent 184 bytes
no ex
05:14:17.266 [DBG] 33 bytes read
no ex
05:14:17.266 [DBG] Sent 32 bytes
no ex
05:14:20.070 [DBG] Sent 184 bytes
no ex
05:14:20.353 [DBG] 33 bytes read
no ex
05:14:20.353 [DBG] Sent 32 bytes
no ex
05:14:23.267 [DBG] 33 bytes read
no ex
05:14:23.267 [DBG] Sent 32 bytes
no ex
05:14:25.074 [DBG] Sent 184 bytes
no ex
05:14:26.319 [DBG] 33 bytes read
no ex
05:14:26.320 [DBG] Sent 32 bytes
no ex
05:14:29.333 [DBG] 33 bytes read
no ex
05:14:29.333 [DBG] Sent 32 bytes
no ex
05:14:30.079 [DBG] Sent 184 bytes
no ex
05:14:32.345 [DBG] 33 bytes read
no ex
05:14:32.345 [DBG] Sent 32 bytes
no ex
05:14:35.083 [DBG] Sent 184 bytes
no ex
05:14:35.298 [DBG] 33 bytes read
no ex
05:14:35.299 [DBG] Sent 32 bytes
no ex
05:14:38.355 [DBG] 33 bytes read
no ex
05:14:38.355 [DBG] Sent 32 bytes
no ex
05:14:40.070 [DBG] Sent 184 bytes
no ex
05:14:41.324 [DBG] 33 bytes read
no ex
05:14:41.325 [DBG] Sent 32 bytes
no ex
05:14:44.303 [DBG] 33 bytes read
no ex
05:14:44.303 [DBG] Sent 32 bytes
no ex
05:14:45.082 [DBG] Sent 184 bytes
no ex
05:14:47.329 [DBG] 33 bytes read
no ex
05:14:47.329 [DBG] Sent 32 bytes
no ex
05:14:50.072 [DBG] Sent 184 bytes
no ex
05:14:50.359 [DBG] 33 bytes read
no ex
05:14:50.360 [DBG] Sent 32 bytes
no ex
05:14:53.283 [DBG] 33 bytes read
no ex
05:14:53.283 [DBG] Sent 32 bytes
no ex
05:14:55.081 [DBG] Sent 184 bytes
no ex
05:14:56.273 [DBG] 33 bytes read
no ex
05:14:56.274 [DBG] Sent 32 bytes
no ex
05:14:59.335 [DBG] 33 bytes read
no ex
05:14:59.335 [DBG] Sent 32 bytes
no ex
05:15:00.082 [DBG] Sent 184 bytes
no ex
05:15:02.328 [DBG] 33 bytes read
no ex
05:15:02.328 [DBG] Sent 32 bytes
no ex
05:15:05.074 [DBG] Sent 184 bytes
no ex
05:15:05.299 [DBG] 33 bytes read
no ex
05:15:05.299 [DBG] Sent 32 bytes
no ex
05:15:08.327 [DBG] 33 bytes read
no ex
05:15:08.328 [DBG] Sent 32 bytes
no ex
05:15:10.078 [DBG] Sent 184 bytes
no ex
05:15:11.442 [DBG] 33 bytes read
no ex
05:15:11.442 [DBG] Sent 32 bytes
no ex
05:15:14.337 [DBG] 33 bytes read
no ex
05:15:14.337 [DBG] Sent 32 bytes
no ex
05:15:15.089 [DBG] Sent 184 bytes
no ex
05:15:17.348 [DBG] 33 bytes read
no ex
05:15:17.348 [DBG] Sent 32 bytes
no ex
05:15:20.073 [DBG] Sent 184 bytes
no ex
05:15:20.311 [DBG] 33 bytes read
no ex
05:15:20.311 [DBG] Sent 32 bytes
no ex
05:15:23.306 [DBG] 33 bytes read
no ex
05:15:23.307 [DBG] Sent 32 bytes
no ex
05:15:25.088 [DBG] Sent 184 bytes
no ex
05:15:26.358 [DBG] 33 bytes read
no ex
05:15:26.358 [DBG] Sent 32 bytes
no ex
05:15:29.527 [DBG] 33 bytes read
no ex
05:15:29.527 [DBG] Sent 32 bytes
no ex
05:15:30.080 [DBG] Sent 184 bytes
no ex
05:15:32.364 [DBG] 33 bytes read
no ex
05:15:32.365 [DBG] Sent 32 bytes
no ex
05:15:35.080 [DBG] Sent 184 bytes
no ex
05:15:35.348 [DBG] 33 bytes read
no ex
05:15:35.348 [DBG] Sent 32 bytes
no ex
05:15:38.287 [DBG] 33 bytes read
no ex
05:15:38.287 [DBG] Sent 32 bytes
no ex
05:15:40.087 [DBG] Sent 184 bytes
no ex
05:15:41.291 [DBG] 33 bytes read
no ex
05:15:41.291 [DBG] Sent 32 bytes
no ex
05:15:44.348 [DBG] 33 bytes read
no ex
05:15:44.349 [DBG] Sent 32 bytes
no ex
05:15:45.079 [DBG] Sent 184 bytes
no ex
05:15:47.274 [DBG] 33 bytes read
no ex
05:15:47.274 [DBG] Sent 32 bytes
no ex
05:15:50.084 [DBG] Sent 184 bytes
no ex
05:15:50.285 [DBG] 33 bytes read
no ex
05:15:50.285 [DBG] Sent 32 bytes
no ex
05:15:53.350 [DBG] 33 bytes read
no ex
05:15:53.350 [DBG] Sent 32 bytes
no ex
05:15:55.087 [DBG] Sent 184 bytes
no ex
05:15:56.267 [DBG] 33 bytes read
no ex
05:15:56.267 [DBG] Sent 32 bytes
no ex
05:15:59.353 [DBG] 33 bytes read
no ex
05:15:59.354 [DBG] Sent 32 bytes
no ex
05:16:00.087 [DBG] Sent 184 bytes
no ex
05:16:02.333 [DBG] 33 bytes read
no ex
05:16:02.333 [DBG] Sent 32 bytes
no ex
05:16:05.092 [DBG] Sent 184 bytes
no ex
05:16:05.401 [DBG] 33 bytes read
no ex
05:16:05.401 [DBG] Sent 32 bytes
no ex
05:16:08.324 [DBG] 33 bytes read
no ex
05:16:08.324 [DBG] Sent 32 bytes
no ex
05:16:10.090 [DBG] Sent 184 bytes
no ex
05:16:11.349 [DBG] 33 bytes read
no ex
05:16:11.349 [DBG] Sent 32 bytes
no ex
05:16:14.267 [DBG] 33 bytes read
no ex
05:16:14.267 [DBG] Sent 32 bytes
no ex
05:16:15.079 [DBG] Sent 184 bytes
no ex
05:16:17.346 [DBG] 33 bytes read
no ex
05:16:17.346 [DBG] Sent 32 bytes
no ex
05:16:20.089 [DBG] Sent 184 bytes
no ex
05:16:20.409 [DBG] 33 bytes read
no ex
05:16:20.409 [DBG] Sent 32 bytes
no ex
05:16:23.480 [DBG] 33 bytes read
no ex
05:16:23.481 [DBG] Sent 32 bytes
no ex
05:16:25.096 [DBG] Sent 184 bytes
no ex
05:16:26.501 [DBG] 33 bytes read
no ex
05:16:26.501 [DBG] Sent 32 bytes
no ex
05:16:29.606 [DBG] 33 bytes read
no ex
05:16:29.607 [DBG] Sent 32 bytes
no ex
05:16:30.086 [DBG] Sent 184 bytes
no ex
05:16:32.711 [DBG] 33 bytes read
no ex
05:16:32.712 [DBG] Sent 32 bytes
no ex
05:16:35.095 [DBG] Sent 184 bytes
no ex
05:16:35.128 [INF] RunScan: 0
05:16:35.128 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:16)","Data":null,"DataObj":null}
05:16:35.453 [DBG] 33 bytes read
no ex
05:16:35.453 [DBG] Sent 32 bytes
no ex
05:16:38.384 [DBG] 33 bytes read
no ex
05:16:38.384 [DBG] Sent 32 bytes
no ex
05:16:40.097 [DBG] Sent 184 bytes
no ex
05:16:41.375 [DBG] 33 bytes read
no ex
05:16:41.375 [DBG] Sent 32 bytes
no ex
05:16:44.365 [DBG] 33 bytes read
no ex
05:16:44.365 [DBG] Sent 32 bytes
no ex
05:16:45.085 [DBG] Sent 184 bytes
no ex
05:16:47.332 [DBG] 33 bytes read
no ex
05:16:47.332 [DBG] Sent 32 bytes
no ex
05:16:50.094 [DBG] Sent 184 bytes
no ex
05:16:50.487 [DBG] 33 bytes read
no ex
05:16:50.488 [DBG] Sent 32 bytes
no ex
05:16:53.319 [DBG] 33 bytes read
no ex
05:16:53.320 [DBG] Sent 32 bytes
no ex
05:16:55.094 [DBG] Sent 184 bytes
no ex
05:16:56.291 [DBG] 33 bytes read
no ex
05:16:56.291 [DBG] Sent 32 bytes
no ex
05:16:59.305 [DBG] 33 bytes read
no ex
05:16:59.305 [DBG] Sent 32 bytes
no ex
05:17:00.098 [DBG] Sent 184 bytes
no ex
05:17:02.354 [DBG] 33 bytes read
no ex
05:17:02.355 [DBG] Sent 32 bytes
no ex
05:17:05.085 [DBG] Sent 184 bytes
no ex
05:17:05.326 [DBG] 33 bytes read
no ex
05:17:05.326 [DBG] Sent 32 bytes
no ex
05:17:08.345 [DBG] 33 bytes read
no ex
05:17:08.345 [DBG] Sent 32 bytes
no ex
05:17:10.102 [DBG] Sent 184 bytes
no ex
05:17:11.381 [DBG] 33 bytes read
no ex
05:17:11.381 [DBG] Sent 32 bytes
no ex
05:17:14.418 [DBG] 33 bytes read
no ex
05:17:14.418 [DBG] Sent 32 bytes
no ex
05:17:15.088 [DBG] Sent 184 bytes
no ex
05:17:17.391 [DBG] 33 bytes read
no ex
05:17:17.391 [DBG] Sent 32 bytes
no ex
05:17:20.094 [DBG] Sent 184 bytes
no ex
05:17:20.479 [DBG] 33 bytes read
no ex
05:17:20.479 [DBG] Sent 32 bytes
no ex
05:17:23.416 [DBG] 33 bytes read
no ex
05:17:23.416 [DBG] Sent 32 bytes
no ex
05:17:25.091 [DBG] Sent 184 bytes
no ex
05:17:26.266 [DBG] 33 bytes read
no ex
05:17:26.266 [DBG] Sent 32 bytes
no ex
05:17:29.316 [DBG] 33 bytes read
no ex
05:17:29.316 [DBG] Sent 32 bytes
no ex
05:17:30.098 [DBG] Sent 184 bytes
no ex
05:17:32.328 [DBG] 33 bytes read
no ex
05:17:32.328 [DBG] Sent 32 bytes
no ex
05:17:35.105 [DBG] Sent 184 bytes
no ex
05:17:35.328 [DBG] 33 bytes read
no ex
05:17:35.329 [DBG] Sent 32 bytes
no ex
05:17:36.187 [INF] RunHandleWinLoss: 0
05:17:36.187 [INF] CalculateResult: 20220805
05:17:36.187 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
05:17:38.325 [DBG] 33 bytes read
no ex
05:17:38.325 [DBG] Sent 32 bytes
no ex
05:17:40.094 [DBG] Sent 184 bytes
no ex
05:17:41.268 [DBG] 33 bytes read
no ex
05:17:41.269 [DBG] Sent 32 bytes
no ex
05:17:44.345 [DBG] 33 bytes read
no ex
05:17:44.345 [DBG] Sent 32 bytes
no ex
05:17:45.089 [DBG] Sent 184 bytes
no ex
05:17:47.445 [DBG] 33 bytes read
no ex
05:17:47.445 [DBG] Sent 32 bytes
no ex
05:17:50.094 [DBG] Sent 184 bytes
no ex
05:17:50.565 [DBG] 33 bytes read
no ex
05:17:50.565 [DBG] Sent 32 bytes
no ex
05:17:53.374 [DBG] 33 bytes read
no ex
05:17:53.374 [DBG] Sent 32 bytes
no ex
05:17:55.098 [DBG] Sent 184 bytes
no ex
05:17:56.271 [DBG] 33 bytes read
no ex
05:17:56.271 [DBG] Sent 32 bytes
no ex
05:17:59.269 [DBG] 33 bytes read
no ex
05:17:59.269 [DBG] Sent 32 bytes
no ex
05:18:00.095 [DBG] Sent 184 bytes
no ex
05:18:02.365 [DBG] 33 bytes read
no ex
05:18:02.365 [DBG] Sent 32 bytes
no ex
05:18:05.100 [DBG] Sent 184 bytes
no ex
05:18:05.403 [DBG] 33 bytes read
no ex
05:18:05.403 [DBG] Sent 32 bytes
no ex
05:18:08.326 [DBG] 33 bytes read
no ex
05:18:08.326 [DBG] Sent 32 bytes
no ex
05:18:10.101 [DBG] Sent 184 bytes
no ex
05:18:11.298 [DBG] 33 bytes read
no ex
05:18:11.298 [DBG] Sent 32 bytes
no ex
05:18:14.335 [DBG] 33 bytes read
no ex
05:18:14.336 [DBG] Sent 32 bytes
no ex
05:18:15.104 [DBG] Sent 184 bytes
no ex
05:18:17.330 [DBG] 33 bytes read
no ex
05:18:17.330 [DBG] Sent 32 bytes
no ex
05:18:20.101 [DBG] Sent 184 bytes
no ex
05:18:20.354 [DBG] 33 bytes read
no ex
05:18:20.355 [DBG] Sent 32 bytes
no ex
05:18:23.298 [DBG] 33 bytes read
no ex
05:18:23.298 [DBG] Sent 32 bytes
no ex
05:18:25.105 [DBG] Sent 184 bytes
no ex
05:18:26.340 [DBG] 33 bytes read
no ex
05:18:26.340 [DBG] Sent 32 bytes
no ex
05:18:29.327 [DBG] 33 bytes read
no ex
05:18:29.327 [DBG] Sent 32 bytes
no ex
05:18:30.109 [DBG] Sent 184 bytes
no ex
05:18:32.301 [DBG] 33 bytes read
no ex
05:18:32.301 [DBG] Sent 32 bytes
no ex
05:18:35.096 [DBG] Sent 184 bytes
no ex
05:18:35.367 [DBG] 33 bytes read
no ex
05:18:35.367 [DBG] Sent 32 bytes
no ex
05:18:38.357 [DBG] 33 bytes read
no ex
05:18:38.357 [DBG] Sent 32 bytes
no ex
05:18:40.104 [DBG] Sent 184 bytes
no ex
05:18:41.421 [DBG] 33 bytes read
no ex
05:18:41.422 [DBG] Sent 32 bytes
no ex
05:18:44.334 [DBG] 33 bytes read
no ex
05:18:44.334 [DBG] Sent 32 bytes
no ex
05:18:45.098 [DBG] Sent 184 bytes
no ex
05:18:47.322 [DBG] 33 bytes read
no ex
05:18:47.322 [DBG] Sent 32 bytes
no ex
05:18:50.103 [DBG] Sent 184 bytes
no ex
05:18:50.266 [DBG] 33 bytes read
no ex
05:18:50.266 [DBG] Sent 32 bytes
no ex
05:18:53.310 [DBG] 33 bytes read
no ex
05:18:53.310 [DBG] Sent 32 bytes
no ex
05:18:55.108 [DBG] Sent 184 bytes
no ex
05:18:56.276 [DBG] 33 bytes read
no ex
05:18:56.276 [DBG] Sent 32 bytes
no ex
05:18:59.267 [DBG] 33 bytes read
no ex
05:18:59.267 [DBG] Sent 32 bytes
no ex
05:19:00.113 [DBG] Sent 184 bytes
no ex
05:19:02.404 [DBG] 33 bytes read
no ex
05:19:02.404 [DBG] Sent 32 bytes
no ex
05:19:05.110 [DBG] Sent 184 bytes
no ex
05:19:05.444 [DBG] 33 bytes read
no ex
05:19:05.445 [DBG] Sent 32 bytes
no ex
05:19:08.298 [DBG] 33 bytes read
no ex
05:19:08.298 [DBG] Sent 32 bytes
no ex
05:19:10.100 [DBG] Sent 184 bytes
no ex
05:19:11.273 [DBG] 33 bytes read
no ex
05:19:11.274 [DBG] Sent 32 bytes
no ex
05:19:14.319 [DBG] 33 bytes read
no ex
05:19:14.319 [DBG] Sent 32 bytes
no ex
05:19:15.106 [DBG] Sent 184 bytes
no ex
05:19:17.269 [DBG] 33 bytes read
no ex
05:19:17.269 [DBG] Sent 32 bytes
no ex
05:19:20.109 [DBG] Sent 184 bytes
no ex
05:19:20.298 [DBG] 33 bytes read
no ex
05:19:20.299 [DBG] Sent 32 bytes
no ex
05:19:23.287 [DBG] 33 bytes read
no ex
05:19:23.287 [DBG] Sent 32 bytes
no ex
05:19:25.110 [DBG] Sent 184 bytes
no ex
05:19:26.342 [DBG] 33 bytes read
no ex
05:19:26.342 [DBG] Sent 32 bytes
no ex
05:19:30.087 [DBG] 33 bytes read
no ex
05:19:30.087 [DBG] Sent 32 bytes
no ex
05:19:30.100 [DBG] Sent 184 bytes
no ex
05:19:32.270 [DBG] 33 bytes read
no ex
05:19:32.270 [DBG] Sent 32 bytes
no ex
05:19:35.108 [DBG] Sent 184 bytes
no ex
05:19:35.355 [DBG] 33 bytes read
no ex
05:19:35.356 [DBG] Sent 32 bytes
no ex
05:19:38.359 [DBG] 33 bytes read
no ex
05:19:38.359 [DBG] Sent 32 bytes
no ex
05:19:40.116 [DBG] Sent 184 bytes
no ex
05:19:41.291 [DBG] 33 bytes read
no ex
05:19:41.291 [DBG] Sent 32 bytes
no ex
05:19:44.292 [DBG] 33 bytes read
no ex
05:19:44.293 [DBG] Sent 32 bytes
no ex
05:19:45.110 [DBG] Sent 184 bytes
no ex
05:19:47.351 [DBG] 33 bytes read
no ex
05:19:47.351 [DBG] Sent 32 bytes
no ex
05:19:50.105 [DBG] Sent 184 bytes
no ex
05:19:50.677 [DBG] 0 bytes read. Closing.
no ex
05:21:35.128 [INF] RunScan: 0
05:21:35.128 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:21)","Data":null,"DataObj":null}
05:22:36.188 [INF] RunHandleWinLoss: 0
05:22:36.188 [INF] CalculateResult: 20220805
05:22:36.188 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
05:26:35.128 [INF] RunScan: 0
05:26:35.128 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:26)","Data":null,"DataObj":null}
05:27:36.189 [INF] RunHandleWinLoss: 0
05:27:36.189 [INF] CalculateResult: 20220805
05:27:36.189 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
05:31:35.128 [INF] RunScan: 0
05:31:35.128 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:31)","Data":null,"DataObj":null}
05:32:36.191 [INF] RunHandleWinLoss: 0
05:32:36.191 [INF] CalculateResult: 20220805
05:32:36.191 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
05:36:35.128 [INF] RunScan: 0
05:36:35.128 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:36)","Data":null,"DataObj":null}
05:37:36.192 [INF] RunHandleWinLoss: 0
05:37:36.192 [INF] CalculateResult: 20220805
05:37:36.192 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
05:41:35.129 [INF] RunScan: 0
05:41:35.129 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:41)","Data":null,"DataObj":null}
05:42:36.194 [INF] RunHandleWinLoss: 0
05:42:36.194 [INF] CalculateResult: 20220805
05:42:36.194 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
05:46:35.129 [INF] RunScan: 0
05:46:35.129 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:46)","Data":null,"DataObj":null}
05:47:36.196 [INF] RunHandleWinLoss: 0
05:47:36.196 [INF] CalculateResult: 20220805
05:47:36.196 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
05:51:35.129 [INF] RunScan: 0
05:51:35.129 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:51)","Data":null,"DataObj":null}
05:52:36.198 [INF] RunHandleWinLoss: 0
05:52:36.198 [INF] CalculateResult: 20220805
05:52:36.198 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
05:56:35.129 [INF] RunScan: 0
05:56:35.129 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:56)","Data":null,"DataObj":null}
05:57:36.200 [INF] RunHandleWinLoss: 0
05:57:36.200 [INF] CalculateResult: 20220805
05:57:36.200 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
06:01:35.129 [INF] RunScan: 0
06:01:35.129 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:1)","Data":null,"DataObj":null}
06:02:36.201 [INF] RunHandleWinLoss: 0
06:02:36.201 [INF] CalculateResult: 20220805
06:02:36.201 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
06:06:35.130 [INF] RunScan: 0
06:06:35.130 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:6)","Data":null,"DataObj":null}
06:07:36.203 [INF] RunHandleWinLoss: 0
06:07:36.203 [INF] CalculateResult: 20220805
06:07:36.203 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
06:11:35.130 [INF] RunScan: 0
06:11:35.130 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:11)","Data":null,"DataObj":null}
06:12:36.204 [INF] RunHandleWinLoss: 0
06:12:36.204 [INF] CalculateResult: 20220805
06:12:36.204 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
06:16:35.130 [INF] RunScan: 0
06:16:35.130 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:16)","Data":null,"DataObj":null}
06:17:36.205 [INF] RunHandleWinLoss: 0
06:17:36.206 [INF] CalculateResult: 20220805
06:17:36.206 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
06:21:35.130 [INF] RunScan: 0
06:21:35.130 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:21)","Data":null,"DataObj":null}
06:22:36.208 [INF] RunHandleWinLoss: 0
06:22:36.208 [INF] CalculateResult: 20220805
06:22:36.208 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
06:26:35.130 [INF] RunScan: 0
06:26:35.131 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:26)","Data":null,"DataObj":null}
06:27:36.209 [INF] RunHandleWinLoss: 0
06:27:36.209 [INF] CalculateResult: 20220805
06:27:36.209 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
06:31:35.131 [INF] RunScan: 0
06:31:35.131 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:31)","Data":null,"DataObj":null}
06:32:36.211 [INF] RunHandleWinLoss: 0
06:32:36.211 [INF] CalculateResult: 20220805
06:32:36.211 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
06:36:35.131 [INF] RunScan: 0
06:36:35.131 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:36)","Data":null,"DataObj":null}
06:37:36.212 [INF] RunHandleWinLoss: 0
06:37:36.212 [INF] CalculateResult: 20220805
06:37:36.212 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
06:41:35.131 [INF] RunScan: 0
06:41:35.131 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:41)","Data":null,"DataObj":null}
06:42:36.214 [INF] RunHandleWinLoss: 0
06:42:36.214 [INF] CalculateResult: 20220805
06:42:36.214 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
06:46:35.131 [INF] RunScan: 0
06:46:35.131 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:46)","Data":null,"DataObj":null}
06:47:36.215 [INF] RunHandleWinLoss: 0
06:47:36.215 [INF] CalculateResult: 20220805
06:47:36.215 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
06:51:35.131 [INF] RunScan: 0
06:51:35.132 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:51)","Data":null,"DataObj":null}
06:52:36.216 [INF] RunHandleWinLoss: 0
06:52:36.217 [INF] CalculateResult: 20220805
06:52:36.217 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
06:56:35.132 [INF] RunScan: 0
06:56:35.132 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:56)","Data":null,"DataObj":null}
06:57:16.550 [DBG] Client connected from 205.210.31.141:45679
no ex
06:57:16.552 [DBG] 207 bytes read
no ex
06:57:26.273 [DBG] 0 bytes read. Closing.
no ex
06:57:36.218 [INF] RunHandleWinLoss: 0
06:57:36.218 [INF] CalculateResult: 20220805
06:57:36.218 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
07:01:35.132 [INF] RunScan: 0
07:01:35.132 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:1)","Data":null,"DataObj":null}
07:02:36.220 [INF] RunHandleWinLoss: 0
07:02:36.220 [INF] CalculateResult: 20220805
07:02:36.220 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
07:06:35.132 [INF] RunScan: 0
07:06:35.132 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:6)","Data":null,"DataObj":null}
07:07:36.221 [INF] RunHandleWinLoss: 0
07:07:36.221 [INF] CalculateResult: 20220805
07:07:36.221 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
07:11:35.132 [INF] RunScan: 0
07:11:35.132 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:11)","Data":null,"DataObj":null}
07:12:36.222 [INF] RunHandleWinLoss: 0
07:12:36.223 [INF] CalculateResult: 20220805
07:12:36.223 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
07:16:35.133 [INF] RunScan: 0
07:16:35.133 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:16)","Data":null,"DataObj":null}
07:17:36.224 [INF] RunHandleWinLoss: 0
07:17:36.224 [INF] CalculateResult: 20220805
07:17:36.224 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
07:21:35.133 [INF] RunScan: 0
07:21:35.133 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:21)","Data":null,"DataObj":null}
07:22:36.225 [INF] RunHandleWinLoss: 0
07:22:36.225 [INF] CalculateResult: 20220805
07:22:36.225 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
07:26:35.133 [INF] RunScan: 0
07:26:35.133 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:26)","Data":null,"DataObj":null}
07:27:36.227 [INF] RunHandleWinLoss: 0
07:27:36.227 [INF] CalculateResult: 20220805
07:27:36.227 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
07:31:35.133 [INF] RunScan: 0
07:31:35.133 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:31)","Data":null,"DataObj":null}
07:32:36.228 [INF] RunHandleWinLoss: 0
07:32:36.228 [INF] CalculateResult: 20220805
07:32:36.228 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
07:36:35.134 [INF] RunScan: 0
07:36:35.134 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:36)","Data":null,"DataObj":null}
07:37:36.230 [INF] RunHandleWinLoss: 0
07:37:36.230 [INF] CalculateResult: 20220805
07:37:36.230 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
07:41:35.134 [INF] RunScan: 0
07:41:35.134 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:41)","Data":null,"DataObj":null}
07:42:36.231 [INF] RunHandleWinLoss: 0
07:42:36.231 [INF] CalculateResult: 20220805
07:42:36.231 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
07:46:35.134 [INF] RunScan: 0
07:46:35.134 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:46)","Data":null,"DataObj":null}
07:47:36.233 [INF] RunHandleWinLoss: 0
07:47:36.233 [INF] CalculateResult: 20220805
07:47:36.233 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
07:51:35.134 [INF] RunScan: 0
07:51:35.134 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:51)","Data":null,"DataObj":null}
07:52:36.234 [INF] RunHandleWinLoss: 0
07:52:36.234 [INF] CalculateResult: 20220805
07:52:36.234 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
07:56:35.134 [INF] RunScan: 0
07:56:35.135 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:56)","Data":null,"DataObj":null}
07:57:36.236 [INF] RunHandleWinLoss: 0
07:57:36.236 [INF] CalculateResult: 20220805
07:57:36.236 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
08:01:35.135 [INF] RunScan: 0
08:01:35.135 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:1)","Data":null,"DataObj":null}
08:02:36.237 [INF] RunHandleWinLoss: 0
08:02:36.237 [INF] CalculateResult: 20220805
08:02:36.237 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
08:06:35.136 [INF] RunScan: 0
08:06:35.136 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:6)","Data":null,"DataObj":null}
08:07:36.238 [INF] RunHandleWinLoss: 0
08:07:36.239 [INF] CalculateResult: 20220805
08:07:36.239 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
08:11:35.136 [INF] RunScan: 0
08:11:35.136 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:11)","Data":null,"DataObj":null}
08:12:36.240 [INF] RunHandleWinLoss: 0
08:12:36.240 [INF] CalculateResult: 20220805
08:12:36.240 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
08:16:35.136 [INF] RunScan: 0
08:16:35.136 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:16)","Data":null,"DataObj":null}
08:17:36.242 [INF] RunHandleWinLoss: 0
08:17:36.242 [INF] CalculateResult: 20220805
08:17:36.242 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
08:21:35.136 [INF] RunScan: 0
08:21:35.136 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:21)","Data":null,"DataObj":null}
08:22:36.243 [INF] RunHandleWinLoss: 0
08:22:36.243 [INF] CalculateResult: 20220805
08:22:36.243 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
08:26:35.136 [INF] RunScan: 0
08:26:35.137 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:26)","Data":null,"DataObj":null}
08:27:36.245 [INF] RunHandleWinLoss: 0
08:27:36.245 [INF] CalculateResult: 20220805
08:27:36.245 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
08:31:35.137 [INF] RunScan: 0
08:31:35.137 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:31)","Data":null,"DataObj":null}
08:32:36.246 [INF] RunHandleWinLoss: 0
08:32:36.247 [INF] CalculateResult: 20220805
08:32:36.247 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
08:36:35.137 [INF] RunScan: 0
08:36:35.137 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:36)","Data":null,"DataObj":null}
08:37:36.248 [INF] RunHandleWinLoss: 0
08:37:36.248 [INF] CalculateResult: 20220805
08:37:36.248 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
08:41:35.137 [INF] RunScan: 0
08:41:35.138 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:41)","Data":null,"DataObj":null}
08:42:36.250 [INF] RunHandleWinLoss: 0
08:42:36.250 [INF] CalculateResult: 20220805
08:42:36.250 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
08:46:35.138 [INF] RunScan: 0
08:46:35.138 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:46)","Data":null,"DataObj":null}
08:47:36.252 [INF] RunHandleWinLoss: 0
08:47:36.252 [INF] CalculateResult: 20220805
08:47:36.252 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
08:51:35.138 [INF] RunScan: 0
08:51:35.138 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:51)","Data":null,"DataObj":null}
08:52:36.253 [INF] RunHandleWinLoss: 0
08:52:36.253 [INF] CalculateResult: 20220805
08:52:36.253 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
08:56:35.139 [INF] RunScan: 0
08:56:35.139 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:56)","Data":null,"DataObj":null}
08:57:36.255 [INF] RunHandleWinLoss: 0
08:57:36.255 [INF] CalculateResult: 20220805
08:57:36.255 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
09:01:35.139 [INF] RunScan: 0
09:01:35.139 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:1)","Data":null,"DataObj":null}
09:02:36.256 [INF] RunHandleWinLoss: 0
09:02:36.256 [INF] CalculateResult: 20220805
09:02:36.256 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
09:06:35.139 [INF] RunScan: 0
09:06:35.139 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:6)","Data":null,"DataObj":null}
09:07:36.258 [INF] RunHandleWinLoss: 0
09:07:36.258 [INF] CalculateResult: 20220805
09:07:36.258 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
09:11:35.139 [INF] RunScan: 0
09:11:35.140 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:11)","Data":null,"DataObj":null}
09:12:36.260 [INF] RunHandleWinLoss: 0
09:12:36.260 [INF] CalculateResult: 20220805
09:12:36.260 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
09:16:35.140 [INF] RunScan: 0
09:16:35.140 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:16)","Data":null,"DataObj":null}
09:17:36.261 [INF] RunHandleWinLoss: 0
09:17:36.261 [INF] CalculateResult: 20220805
09:17:36.261 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
09:21:35.140 [INF] RunScan: 0
09:21:35.140 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:21)","Data":null,"DataObj":null}
09:22:36.262 [INF] RunHandleWinLoss: 0
09:22:36.262 [INF] CalculateResult: 20220805
09:22:36.262 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
09:26:35.140 [INF] RunScan: 0
09:26:35.140 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:26)","Data":null,"DataObj":null}
09:27:36.264 [INF] RunHandleWinLoss: 0
09:27:36.264 [INF] CalculateResult: 20220805
09:27:36.264 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
09:31:35.140 [INF] RunScan: 0
09:31:35.141 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:31)","Data":null,"DataObj":null}
09:32:36.266 [INF] RunHandleWinLoss: 0
09:32:36.266 [INF] CalculateResult: 20220805
09:32:36.266 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
09:36:35.141 [INF] RunScan: 0
09:36:35.141 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:36)","Data":null,"DataObj":null}
09:37:36.268 [INF] RunHandleWinLoss: 0
09:37:36.268 [INF] CalculateResult: 20220805
09:37:36.268 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
09:41:35.141 [INF] RunScan: 0
09:41:35.141 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:41)","Data":null,"DataObj":null}
09:42:36.270 [INF] RunHandleWinLoss: 0
09:42:36.270 [INF] CalculateResult: 20220805
09:42:36.270 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
09:46:35.142 [INF] RunScan: 0
09:46:35.142 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:46)","Data":null,"DataObj":null}
09:47:36.271 [INF] RunHandleWinLoss: 0
09:47:36.271 [INF] CalculateResult: 20220805
09:47:36.271 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
09:51:35.142 [INF] RunScan: 0
09:51:35.142 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:51)","Data":null,"DataObj":null}
09:52:36.272 [INF] RunHandleWinLoss: 0
09:52:36.272 [INF] CalculateResult: 20220805
09:52:36.272 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
09:56:35.142 [INF] RunScan: 0
09:56:35.142 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:56)","Data":null,"DataObj":null}
09:57:36.274 [INF] RunHandleWinLoss: 0
09:57:36.274 [INF] CalculateResult: 20220805
09:57:36.274 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
10:01:35.142 [INF] RunScan: 0
10:01:35.142 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:1)","Data":null,"DataObj":null}
10:02:36.275 [INF] RunHandleWinLoss: 0
10:02:36.275 [INF] CalculateResult: 20220805
10:02:36.275 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
10:06:35.142 [INF] RunScan: 0
10:06:35.143 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:6)","Data":null,"DataObj":null}
10:07:36.277 [INF] RunHandleWinLoss: 0
10:07:36.277 [INF] CalculateResult: 20220805
10:07:36.277 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
10:11:35.143 [INF] RunScan: 0
10:11:35.143 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:11)","Data":null,"DataObj":null}
10:12:36.278 [INF] RunHandleWinLoss: 0
10:12:36.278 [INF] CalculateResult: 20220805
10:12:36.278 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
10:16:35.143 [INF] RunScan: 0
10:16:35.143 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:16)","Data":null,"DataObj":null}
10:17:36.280 [INF] RunHandleWinLoss: 0
10:17:36.280 [INF] CalculateResult: 20220805
10:17:36.280 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
10:21:35.143 [INF] RunScan: 0
10:21:35.143 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:21)","Data":null,"DataObj":null}
10:22:36.282 [INF] RunHandleWinLoss: 0
10:22:36.282 [INF] CalculateResult: 20220805
10:22:36.282 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
10:26:35.143 [INF] RunScan: 0
10:26:35.143 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:26)","Data":null,"DataObj":null}
10:27:36.284 [INF] RunHandleWinLoss: 0
10:27:36.284 [INF] CalculateResult: 20220805
10:27:36.284 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
10:31:35.143 [INF] RunScan: 0
10:31:35.144 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:31)","Data":null,"DataObj":null}
10:32:36.285 [INF] RunHandleWinLoss: 0
10:32:36.286 [INF] CalculateResult: 20220805
10:32:36.286 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
10:36:35.144 [INF] RunScan: 0
10:36:35.144 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:36)","Data":null,"DataObj":null}
10:37:36.287 [INF] RunHandleWinLoss: 0
10:37:36.287 [INF] CalculateResult: 20220805
10:37:36.287 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
10:41:35.144 [INF] RunScan: 0
10:41:35.144 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:41)","Data":null,"DataObj":null}
10:42:36.289 [INF] RunHandleWinLoss: 0
10:42:36.289 [INF] CalculateResult: 20220805
10:42:36.289 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
10:46:35.144 [INF] RunScan: 0
10:46:35.144 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:46)","Data":null,"DataObj":null}
10:47:36.290 [INF] RunHandleWinLoss: 0
10:47:36.290 [INF] CalculateResult: 20220805
10:47:36.290 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
10:51:35.144 [INF] RunScan: 0
10:51:35.145 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:51)","Data":null,"DataObj":null}
10:52:36.291 [INF] RunHandleWinLoss: 0
10:52:36.291 [INF] CalculateResult: 20220805
10:52:36.291 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
10:56:35.145 [INF] RunScan: 0
10:56:35.145 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:56)","Data":null,"DataObj":null}
10:57:36.294 [INF] RunHandleWinLoss: 0
10:57:36.294 [INF] CalculateResult: 20220805
10:57:36.294 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
11:01:35.145 [INF] RunScan: 0
11:01:35.145 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:1)","Data":null,"DataObj":null}
11:02:36.296 [INF] RunHandleWinLoss: 0
11:02:36.296 [INF] CalculateResult: 20220805
11:02:36.296 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
11:06:35.145 [INF] RunScan: 0
11:06:35.145 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:6)","Data":null,"DataObj":null}
11:07:36.297 [INF] RunHandleWinLoss: 0
11:07:36.297 [INF] CalculateResult: 20220805
11:07:36.297 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
11:11:35.145 [INF] RunScan: 0
11:11:35.145 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:11)","Data":null,"DataObj":null}
11:12:36.299 [INF] RunHandleWinLoss: 0
11:12:36.299 [INF] CalculateResult: 20220805
11:12:36.299 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
11:16:35.145 [INF] RunScan: 0
11:16:35.146 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:16)","Data":null,"DataObj":null}
11:17:36.301 [INF] RunHandleWinLoss: 0
11:17:36.301 [INF] CalculateResult: 20220805
11:17:36.301 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
11:21:35.146 [INF] RunScan: 0
11:21:35.146 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:21)","Data":null,"DataObj":null}
11:22:36.304 [INF] RunHandleWinLoss: 0
11:22:36.304 [INF] CalculateResult: 20220805
11:22:36.304 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
11:26:35.146 [INF] RunScan: 0
11:26:35.146 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:26)","Data":null,"DataObj":null}
11:27:36.305 [INF] RunHandleWinLoss: 0
11:27:36.305 [INF] CalculateResult: 20220805
11:27:36.305 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
11:31:35.146 [INF] RunScan: 0
11:31:35.147 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:31)","Data":null,"DataObj":null}
11:32:36.306 [INF] RunHandleWinLoss: 0
11:32:36.306 [INF] CalculateResult: 20220805
11:32:36.306 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
11:36:35.148 [INF] RunScan: 0
11:36:35.148 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:36)","Data":null,"DataObj":null}
11:37:36.308 [INF] RunHandleWinLoss: 0
11:37:36.308 [INF] CalculateResult: 20220805
11:37:36.308 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
11:41:35.148 [INF] RunScan: 0
11:41:35.148 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:41)","Data":null,"DataObj":null}
11:42:36.310 [INF] RunHandleWinLoss: 0
11:42:36.310 [INF] CalculateResult: 20220805
11:42:36.310 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
11:46:35.149 [INF] RunScan: 0
11:46:35.149 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:46)","Data":null,"DataObj":null}
11:47:36.312 [INF] RunHandleWinLoss: 0
11:47:36.312 [INF] CalculateResult: 20220805
11:47:36.312 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
11:51:35.149 [INF] RunScan: 0
11:51:35.149 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:51)","Data":null,"DataObj":null}
11:52:36.314 [INF] RunHandleWinLoss: 0
11:52:36.314 [INF] CalculateResult: 20220805
11:52:36.314 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
11:53:51.208 [DBG] Client connected from 127.0.0.1:35678
no ex
11:53:51.208 [DBG] 869 bytes read
no ex
11:53:51.208 [DBG] Building Hybi-14 Response
no ex
11:53:51.208 [DBG] Sent 129 bytes
no ex
11:53:51.346 [DBG] 145 bytes read
no ex
11:53:51.346 [DBG] Sent 30 bytes
no ex
11:53:51.346 [INF] GET: http://127.0.0.1:8081/api?c=3&un=trautrautii&pw=e10adc3949ba59abbe56e057f20f883e&pf=web&at=
11:53:51.359 [INF] GET response: {"success":true,"message":null,"errorCode":"0","data":null,"sessionKey":"eyJuaWNrbmFtZSI6Ildvd3d5MTkiLCJhdmF0YXIiOiIwIiwidmluVG90YWwiOjAsInh1VG90YWwiOjUwMDAwMCwidmlwcG9pbnQiOjAsInZpcHBvaW50U2F2ZSI6MCwiY3JlYXRlVGltZSI6IjA1LTA4LTIwMjIiLCJpcEFkZHJlc3MiOiIxMjcuMC4wLjEiLCJjZXJ0aWZpY2F0ZSI6ZmFsc2UsImx1Y2t5Um90YXRlIjowLCJkYWlMeSI6MCwibW9iaWxlU2VjdXJlIjowLCJiaXJ0aGRheSI6IiIsImFwcFNlY3VyZSI6MCwidXNlcm5hbWUiOiJUcmF1dHJhdXRpaSIsImVtYWlsIjowLCJhZGRyZXNzIjpudWxsLCJ2ZXJpZnlNb2JpbGUiOmZhbHNlfQ==","accessToken":"43c5bbf823e8ab14ef25ca5b0bff6f81"}

11:53:51.371 [DBG] Sent 5724 bytes
no ex
11:53:53.409 [DBG] Sent 184 bytes
no ex
11:53:54.346 [DBG] 31 bytes read
no ex
11:53:54.346 [DBG] Sent 30 bytes
no ex
11:53:56.429 [DBG] 31 bytes read
no ex
11:53:56.429 [DBG] Sent 30 bytes
no ex
11:53:56.541 [DBG] 31 bytes read
no ex
11:53:56.541 [DBG] Sent 30 bytes
no ex
11:53:56.587 [DBG] 31 bytes read
no ex
11:53:56.588 [DBG] Sent 30 bytes
no ex
11:53:56.639 [DBG] 124 bytes read
no ex
11:53:56.653 [DBG] Sent 35 bytes
no ex
11:53:57.584 [DBG] 31 bytes read
no ex
11:53:57.584 [DBG] Sent 30 bytes
no ex
11:53:58.409 [DBG] Sent 184 bytes
no ex
11:54:00.345 [DBG] 31 bytes read
no ex
11:54:00.345 [DBG] Sent 30 bytes
no ex
11:54:03.304 [DBG] 31 bytes read
no ex
11:54:03.304 [DBG] Sent 30 bytes
no ex
11:54:03.416 [DBG] Sent 184 bytes
no ex
11:54:04.091 [DBG] 47 bytes read
no ex
11:54:04.091 [INF] GET: http://127.0.0.1:19082/api_backend?c=8797&nn=Wowwy19&mn=-50000&h=75d1c2fc6a2359e3271258608f527455
11:54:05.172 [INF] GET response: {"success":false,"message":null,"errorCode":"1002","data":"Không có nickname: Wowwy19 hoặc tiền hoàn trả bằng 0: -50000"}

11:54:05.172 [DBG] Sent 20 bytes
no ex
11:54:06.318 [DBG] 31 bytes read
no ex
11:54:06.319 [DBG] Sent 30 bytes
no ex
11:54:08.406 [DBG] Sent 184 bytes
no ex
11:54:09.313 [DBG] 31 bytes read
no ex
11:54:09.313 [DBG] Sent 30 bytes
no ex
11:54:11.718 [DBG] 31 bytes read
no ex
11:54:11.719 [DBG] Sent 30 bytes
no ex
11:54:11.832 [DBG] 31 bytes read
no ex
11:54:11.832 [DBG] Sent 30 bytes
no ex
11:54:11.899 [DBG] 31 bytes read
no ex
11:54:11.899 [DBG] Sent 30 bytes
no ex
11:54:11.955 [DBG] 124 bytes read
no ex
11:54:11.976 [DBG] Sent 35 bytes
no ex
11:54:12.332 [DBG] 31 bytes read
no ex
11:54:12.332 [DBG] Sent 30 bytes
no ex
11:54:13.411 [DBG] Sent 184 bytes
no ex
11:54:15.337 [DBG] 31 bytes read
no ex
11:54:15.337 [DBG] Sent 30 bytes
no ex
11:54:18.315 [DBG] 31 bytes read
no ex
11:54:18.315 [DBG] Sent 30 bytes
no ex
11:54:18.410 [DBG] Sent 184 bytes
no ex
11:54:21.323 [DBG] 31 bytes read
no ex
11:54:21.323 [DBG] Sent 30 bytes
no ex
11:54:22.408 [DBG] 47 bytes read
no ex
11:54:22.408 [INF] GET: http://127.0.0.1:19082/api_backend?c=8797&nn=Wowwy19&mn=-50000&h=75d1c2fc6a2359e3271258608f527455
11:54:23.407 [DBG] Sent 184 bytes
no ex
11:54:23.472 [INF] GET response: {"success":false,"message":null,"errorCode":"1002","data":"Không có nickname: Wowwy19 hoặc tiền hoàn trả bằng 0: -50000"}

11:54:23.473 [DBG] Sent 20 bytes
no ex
11:54:24.315 [DBG] 31 bytes read
no ex
11:54:24.316 [DBG] Sent 30 bytes
no ex
11:54:28.413 [DBG] Sent 184 bytes
no ex
11:54:28.835 [DBG] 31 bytes read
no ex
11:54:28.835 [DBG] Sent 30 bytes
no ex
11:54:30.447 [DBG] 31 bytes read
no ex
11:54:30.447 [DBG] Sent 30 bytes
no ex
11:54:33.423 [DBG] 31 bytes read
no ex
11:54:33.424 [DBG] Sent 30 bytes
no ex
11:54:33.424 [DBG] Sent 184 bytes
no ex
11:54:36.399 [DBG] 31 bytes read
no ex
11:54:36.399 [DBG] Sent 30 bytes
no ex
11:54:38.410 [DBG] Sent 184 bytes
no ex
11:54:39.416 [DBG] 31 bytes read
no ex
11:54:39.417 [DBG] Sent 30 bytes
no ex
11:54:42.449 [DBG] 31 bytes read
no ex
11:54:42.450 [DBG] Sent 30 bytes
no ex
11:54:43.422 [DBG] Sent 184 bytes
no ex
11:54:45.635 [DBG] 31 bytes read
no ex
11:54:45.636 [DBG] Sent 30 bytes
no ex
11:54:48.420 [DBG] Sent 184 bytes
no ex
11:54:48.431 [DBG] 31 bytes read
no ex
11:54:48.431 [DBG] Sent 30 bytes
no ex
11:54:51.635 [DBG] 31 bytes read
no ex
11:54:51.636 [DBG] Sent 30 bytes
no ex
11:54:53.425 [DBG] Sent 184 bytes
no ex
11:54:54.607 [DBG] 31 bytes read
no ex
11:54:54.608 [DBG] Sent 30 bytes
no ex
11:54:57.445 [DBG] 31 bytes read
no ex
11:54:57.445 [DBG] Sent 30 bytes
no ex
11:54:58.428 [DBG] Sent 184 bytes
no ex
11:55:02.464 [DBG] Sent 4 bytes
no ex
11:55:02.464 [DBG] Swallowing ObjectDisposedException
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
11:55:06.477 [DBG] Client connected from 127.0.0.1:35790
no ex
11:55:06.477 [DBG] 869 bytes read
no ex
11:55:06.477 [DBG] Building Hybi-14 Response
no ex
11:55:06.478 [DBG] Sent 129 bytes
no ex
11:55:06.724 [DBG] 145 bytes read
no ex
11:55:06.724 [DBG] Sent 30 bytes
no ex
11:55:06.725 [INF] GET: http://127.0.0.1:8081/api?c=3&un=trautrautii&pw=e10adc3949ba59abbe56e057f20f883e&pf=web&at=
11:55:06.735 [INF] GET response: {"success":true,"message":null,"errorCode":"0","data":null,"sessionKey":"eyJuaWNrbmFtZSI6Ildvd3d5MTkiLCJhdmF0YXIiOiIwIiwidmluVG90YWwiOjAsInh1VG90YWwiOjUwMDAwMCwidmlwcG9pbnQiOjAsInZpcHBvaW50U2F2ZSI6MCwiY3JlYXRlVGltZSI6IjA1LTA4LTIwMjIiLCJpcEFkZHJlc3MiOiIxMjcuMC4wLjEiLCJjZXJ0aWZpY2F0ZSI6ZmFsc2UsImx1Y2t5Um90YXRlIjowLCJkYWlMeSI6MCwibW9iaWxlU2VjdXJlIjowLCJiaXJ0aGRheSI6IiIsImFwcFNlY3VyZSI6MCwidXNlcm5hbWUiOiJUcmF1dHJhdXRpaSIsImVtYWlsIjowLCJhZGRyZXNzIjpudWxsLCJ2ZXJpZnlNb2JpbGUiOmZhbHNlfQ==","accessToken":"bc72ffd38c358eb3d3908bfe7cd1ee54"}

11:55:06.740 [DBG] Sent 5728 bytes
no ex
11:55:08.423 [DBG] Sent 184 bytes
no ex
11:55:09.705 [DBG] 31 bytes read
no ex
11:55:09.705 [DBG] Sent 30 bytes
no ex
11:55:12.705 [DBG] 31 bytes read
no ex
11:55:12.706 [DBG] Sent 30 bytes
no ex
11:55:13.417 [DBG] Sent 184 bytes
no ex
11:55:16.028 [DBG] 31 bytes read
no ex
11:55:16.028 [DBG] Sent 30 bytes
no ex
11:55:18.424 [DBG] Sent 184 bytes
no ex
11:55:18.704 [DBG] 31 bytes read
no ex
11:55:18.704 [DBG] Sent 30 bytes
no ex
11:55:21.709 [DBG] 31 bytes read
no ex
11:55:21.709 [DBG] Sent 30 bytes
no ex
11:55:23.430 [DBG] Sent 184 bytes
no ex
11:55:24.748 [DBG] 31 bytes read
no ex
11:55:24.748 [DBG] Sent 30 bytes
no ex
11:55:27.723 [DBG] 31 bytes read
no ex
11:55:27.723 [DBG] Sent 30 bytes
no ex
11:55:28.415 [DBG] Sent 184 bytes
no ex
11:55:30.705 [DBG] 31 bytes read
no ex
11:55:30.706 [DBG] Sent 30 bytes
no ex
11:55:31.336 [DBG] 58 bytes read
no ex
11:55:31.340 [DBG] Sent 71 bytes
no ex
11:55:33.424 [DBG] Sent 184 bytes
no ex
11:55:33.722 [DBG] 31 bytes read
no ex
11:55:33.722 [DBG] Sent 30 bytes
no ex
11:55:36.145 [DBG] 58 bytes read
no ex
11:55:36.156 [DBG] Sent 71 bytes
no ex
11:55:36.700 [DBG] 31 bytes read
no ex
11:55:36.700 [DBG] Sent 30 bytes
no ex
11:55:38.426 [DBG] Sent 184 bytes
no ex
11:55:39.382 [DBG] 58 bytes read
no ex
11:55:39.400 [DBG] Sent 325 bytes
no ex
11:55:39.712 [DBG] 31 bytes read
no ex
11:55:39.713 [DBG] Sent 30 bytes
no ex
11:55:42.717 [DBG] 31 bytes read
no ex
11:55:42.717 [DBG] Sent 30 bytes
no ex
11:55:43.421 [DBG] Sent 184 bytes
no ex
11:55:45.720 [DBG] 31 bytes read
no ex
11:55:45.720 [DBG] Sent 30 bytes
no ex
11:55:48.424 [DBG] Sent 184 bytes
no ex
11:55:49.572 [DBG] 47 bytes read
no ex
11:55:49.586 [DBG] Sent 61 bytes
no ex
11:55:50.426 [DBG] Sent 73 bytes
no ex
11:55:50.555 [DBG] 32 bytes read
no ex
11:55:50.565 [DBG] Sent 41 bytes
no ex
11:55:50.566 [DBG] 31 bytes read
no ex
11:55:50.567 [DBG] Sent 30 bytes
no ex
11:55:51.840 [DBG] 31 bytes read
no ex
11:55:51.840 [DBG] Sent 30 bytes
no ex
11:55:53.423 [DBG] Sent 184 bytes
no ex
11:55:54.774 [DBG] 31 bytes read
no ex
11:55:54.774 [DBG] Sent 30 bytes
no ex
11:55:57.804 [DBG] 31 bytes read
no ex
11:55:57.805 [DBG] Sent 30 bytes
no ex
11:55:58.426 [DBG] Sent 184 bytes
no ex
11:56:00.992 [DBG] 31 bytes read
no ex
11:56:00.992 [DBG] Sent 30 bytes
no ex
11:56:03.421 [DBG] Sent 184 bytes
no ex
11:56:03.868 [DBG] 31 bytes read
no ex
11:56:03.869 [DBG] Sent 30 bytes
no ex
11:56:06.780 [DBG] 31 bytes read
no ex
11:56:06.780 [DBG] Sent 30 bytes
no ex
11:56:08.426 [DBG] Sent 184 bytes
no ex
11:56:08.850 [DBG] 58 bytes read
no ex
11:56:08.854 [DBG] Sent 71 bytes
no ex
11:56:09.787 [DBG] 31 bytes read
no ex
11:56:09.788 [DBG] Sent 30 bytes
no ex
11:56:12.940 [DBG] 31 bytes read
no ex
11:56:12.940 [DBG] Sent 30 bytes
no ex
11:56:13.430 [DBG] Sent 184 bytes
no ex
11:56:15.823 [DBG] 31 bytes read
no ex
11:56:15.823 [DBG] Sent 30 bytes
no ex
11:56:18.434 [DBG] Sent 184 bytes
no ex
11:56:18.822 [DBG] 31 bytes read
no ex
11:56:18.822 [DBG] Sent 30 bytes
no ex
11:56:21.862 [DBG] 31 bytes read
no ex
11:56:21.862 [DBG] Sent 30 bytes
no ex
11:56:23.422 [DBG] Sent 184 bytes
no ex
11:56:24.839 [DBG] 94 bytes read
no ex
11:56:24.844 [DBG] Sent 61 bytes
no ex
11:56:24.844 [DBG] Sent 61 bytes
no ex
11:56:25.043 [DBG] 31 bytes read
no ex
11:56:25.044 [DBG] Sent 30 bytes
no ex
11:56:25.996 [DBG] 94 bytes read
no ex
11:56:26.003 [DBG] Sent 61 bytes
no ex
11:56:26.003 [DBG] Sent 61 bytes
no ex
11:56:27.284 [DBG] 94 bytes read
no ex
11:56:27.289 [DBG] Sent 61 bytes
no ex
11:56:27.289 [DBG] Sent 61 bytes
no ex
11:56:27.709 [DBG] 31 bytes read
no ex
11:56:27.709 [DBG] Sent 30 bytes
no ex
11:56:28.433 [DBG] Sent 184 bytes
no ex
11:56:30.704 [DBG] 31 bytes read
no ex
11:56:30.704 [DBG] Sent 30 bytes
no ex
11:56:33.428 [DBG] Sent 184 bytes
no ex
11:56:33.480 [DBG] 47 bytes read
no ex
11:56:33.497 [DBG] Sent 61 bytes
no ex
11:56:33.700 [DBG] 31 bytes read
no ex
11:56:33.700 [DBG] Sent 30 bytes
no ex
11:56:35.149 [INF] RunScan: 0
11:56:35.149 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:56)","Data":null,"DataObj":null}
11:56:36.740 [DBG] 31 bytes read
no ex
11:56:36.740 [DBG] Sent 30 bytes
no ex
11:56:38.430 [DBG] Sent 184 bytes
no ex
11:56:38.472 [DBG] 47 bytes read
no ex
11:56:38.481 [DBG] Sent 61 bytes
no ex
11:56:39.978 [DBG] 31 bytes read
no ex
11:56:39.979 [DBG] Sent 30 bytes
no ex
11:56:42.698 [DBG] 31 bytes read
no ex
11:56:42.698 [DBG] Sent 30 bytes
no ex
11:56:43.439 [DBG] Sent 184 bytes
no ex
11:56:45.700 [DBG] 31 bytes read
no ex
11:56:45.700 [DBG] Sent 30 bytes
no ex
11:56:48.426 [DBG] Sent 184 bytes
no ex
11:56:48.764 [DBG] 31 bytes read
no ex
11:56:48.764 [DBG] Sent 30 bytes
no ex
11:56:51.739 [DBG] 31 bytes read
no ex
11:56:51.740 [DBG] Sent 30 bytes
no ex
11:56:53.434 [DBG] Sent 184 bytes
no ex
11:56:54.713 [DBG] 31 bytes read
no ex
11:56:54.713 [DBG] Sent 30 bytes
no ex
11:56:57.700 [DBG] 31 bytes read
no ex
11:56:57.700 [DBG] Sent 30 bytes
no ex
11:56:58.436 [DBG] Sent 184 bytes
no ex
11:57:00.714 [DBG] 31 bytes read
no ex
11:57:00.714 [DBG] Sent 30 bytes
no ex
11:57:03.430 [DBG] Sent 184 bytes
no ex
11:57:03.700 [DBG] 31 bytes read
no ex
11:57:03.700 [DBG] Sent 30 bytes
no ex
11:57:06.682 [DBG] 31 bytes read
no ex
11:57:06.682 [DBG] Sent 30 bytes
no ex
11:57:08.434 [DBG] Sent 184 bytes
no ex
11:57:09.713 [DBG] 31 bytes read
no ex
11:57:09.713 [DBG] Sent 30 bytes
no ex
11:57:12.697 [DBG] 31 bytes read
no ex
11:57:12.697 [DBG] Sent 30 bytes
no ex
11:57:13.435 [DBG] Sent 184 bytes
no ex
11:57:15.727 [DBG] 31 bytes read
no ex
11:57:15.727 [DBG] Sent 30 bytes
no ex
11:57:18.441 [DBG] Sent 184 bytes
no ex
11:57:18.710 [DBG] 31 bytes read
no ex
11:57:18.711 [DBG] Sent 30 bytes
no ex
11:57:21.728 [DBG] 31 bytes read
no ex
11:57:21.728 [DBG] Sent 30 bytes
no ex
11:57:23.436 [DBG] Sent 184 bytes
no ex
11:57:24.723 [DBG] 31 bytes read
no ex
11:57:24.723 [DBG] Sent 30 bytes
no ex
11:57:27.719 [DBG] 31 bytes read
no ex
11:57:27.719 [DBG] Sent 30 bytes
no ex
11:57:28.442 [DBG] Sent 184 bytes
no ex
11:57:29.433 [DBG] Sent 74 bytes
no ex
11:57:30.732 [DBG] 31 bytes read
no ex
11:57:30.732 [DBG] Sent 30 bytes
no ex
11:57:33.444 [DBG] Sent 184 bytes
no ex
11:57:33.840 [DBG] 31 bytes read
no ex
11:57:33.840 [DBG] Sent 30 bytes
no ex
11:57:36.315 [INF] RunHandleWinLoss: 0
11:57:36.315 [INF] CalculateResult: 20220805
11:57:36.315 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
11:57:36.713 [DBG] 31 bytes read
no ex
11:57:36.714 [DBG] Sent 30 bytes
no ex
11:57:37.578 [DBG] 47 bytes read
no ex
11:57:37.593 [DBG] Sent 61 bytes
no ex
11:57:38.153 [DBG] 47 bytes read
no ex
11:57:38.156 [DBG] Sent 61 bytes
no ex
11:57:38.430 [DBG] Sent 184 bytes
no ex
11:57:39.766 [DBG] 31 bytes read
no ex
11:57:39.766 [DBG] Sent 30 bytes
no ex
11:57:42.741 [DBG] 31 bytes read
no ex
11:57:42.741 [DBG] Sent 30 bytes
no ex
11:57:43.434 [DBG] Sent 184 bytes
no ex
11:57:45.716 [DBG] 31 bytes read
no ex
11:57:45.716 [DBG] Sent 30 bytes
no ex
11:57:48.436 [DBG] Sent 184 bytes
no ex
11:57:48.692 [DBG] 31 bytes read
no ex
11:57:48.692 [DBG] Sent 30 bytes
no ex
11:57:51.716 [DBG] 31 bytes read
no ex
11:57:51.716 [DBG] Sent 30 bytes
no ex
11:57:53.438 [DBG] Sent 184 bytes
no ex
11:57:54.704 [DBG] 31 bytes read
no ex
11:57:54.704 [DBG] Sent 30 bytes
no ex
11:57:57.748 [DBG] 31 bytes read
no ex
11:57:57.748 [DBG] Sent 30 bytes
no ex
11:57:58.442 [DBG] Sent 184 bytes
no ex
11:58:00.707 [DBG] 31 bytes read
no ex
11:58:00.707 [DBG] Sent 30 bytes
no ex
11:58:03.431 [DBG] Sent 184 bytes
no ex
11:58:03.704 [DBG] 31 bytes read
no ex
11:58:03.704 [DBG] Sent 30 bytes
no ex
11:58:06.718 [DBG] 31 bytes read
no ex
11:58:06.718 [DBG] Sent 30 bytes
no ex
11:58:08.447 [DBG] Sent 184 bytes
no ex
11:58:09.723 [DBG] 31 bytes read
no ex
11:58:09.723 [DBG] Sent 30 bytes
no ex
11:58:12.712 [DBG] 31 bytes read
no ex
11:58:12.713 [DBG] Sent 30 bytes
no ex
11:58:13.433 [DBG] Sent 184 bytes
no ex
11:58:15.733 [DBG] 31 bytes read
no ex
11:58:15.733 [DBG] Sent 30 bytes
no ex
11:58:18.439 [DBG] Sent 184 bytes
no ex
11:58:18.740 [DBG] 31 bytes read
no ex
11:58:18.741 [DBG] Sent 30 bytes
no ex
11:58:21.739 [DBG] 31 bytes read
no ex
11:58:21.739 [DBG] Sent 30 bytes
no ex
11:58:23.441 [DBG] Sent 184 bytes
no ex
11:58:24.715 [DBG] 31 bytes read
no ex
11:58:24.715 [DBG] Sent 30 bytes
no ex
11:58:27.714 [DBG] 31 bytes read
no ex
11:58:27.714 [DBG] Sent 30 bytes
no ex
11:58:28.447 [DBG] Sent 184 bytes
no ex
11:58:30.716 [DBG] 31 bytes read
no ex
11:58:30.718 [DBG] Sent 30 bytes
no ex
11:58:33.435 [DBG] Sent 184 bytes
no ex
11:58:33.690 [DBG] 32 bytes read
no ex
11:58:33.690 [DBG] Sent 31 bytes
no ex
11:58:36.729 [DBG] 32 bytes read
no ex
11:58:36.729 [DBG] Sent 31 bytes
no ex
11:58:38.440 [DBG] Sent 184 bytes
no ex
11:58:39.705 [DBG] 32 bytes read
no ex
11:58:39.705 [DBG] Sent 31 bytes
no ex
11:58:43.441 [DBG] Sent 184 bytes
no ex
11:58:44.323 [DBG] 80 bytes read
no ex
11:58:44.323 [DBG] Sent 31 bytes
no ex
11:58:44.327 [DBG] Sent 62 bytes
no ex
11:58:45.695 [DBG] 32 bytes read
no ex
11:58:45.695 [DBG] Sent 31 bytes
no ex
11:58:48.445 [DBG] Sent 184 bytes
no ex
11:58:48.739 [DBG] 32 bytes read
no ex
11:58:48.740 [DBG] Sent 31 bytes
no ex
11:58:51.718 [DBG] 32 bytes read
no ex
11:58:51.718 [DBG] Sent 31 bytes
no ex
11:58:53.446 [DBG] Sent 184 bytes
no ex
11:58:54.726 [DBG] 32 bytes read
no ex
11:58:54.726 [DBG] Sent 31 bytes
no ex
11:58:57.732 [DBG] 32 bytes read
no ex
11:58:57.732 [DBG] Sent 31 bytes
no ex
11:58:58.447 [DBG] Sent 184 bytes
no ex
11:59:00.726 [DBG] 32 bytes read
no ex
11:59:00.726 [DBG] Sent 31 bytes
no ex
11:59:03.450 [DBG] Sent 184 bytes
no ex
11:59:04.325 [DBG] 48 bytes read
no ex
11:59:04.342 [DBG] Sent 62 bytes
no ex
11:59:05.785 [DBG] 32 bytes read
no ex
11:59:05.785 [DBG] Sent 31 bytes
no ex
11:59:06.707 [DBG] 32 bytes read
no ex
11:59:06.707 [DBG] Sent 31 bytes
no ex
11:59:08.443 [DBG] Sent 184 bytes
no ex
11:59:09.801 [DBG] 32 bytes read
no ex
11:59:09.801 [DBG] Sent 31 bytes
no ex
11:59:12.715 [DBG] 32 bytes read
no ex
11:59:12.716 [DBG] Sent 31 bytes
no ex
11:59:13.446 [DBG] Sent 184 bytes
no ex
11:59:15.862 [DBG] 32 bytes read
no ex
11:59:15.862 [DBG] Sent 31 bytes
no ex
11:59:17.773 [DBG] 48 bytes read
no ex
11:59:17.784 [DBG] Sent 62 bytes
no ex
11:59:18.453 [DBG] Sent 184 bytes
no ex
11:59:18.696 [DBG] 32 bytes read
no ex
11:59:18.696 [DBG] Sent 31 bytes
no ex
11:59:21.720 [DBG] 32 bytes read
no ex
11:59:21.720 [DBG] Sent 31 bytes
no ex
11:59:23.453 [DBG] Sent 184 bytes
no ex
11:59:24.857 [DBG] 80 bytes read
no ex
11:59:24.857 [DBG] Sent 31 bytes
no ex
11:59:24.870 [DBG] Sent 62 bytes
no ex
11:59:27.749 [DBG] 48 bytes read
no ex
11:59:27.750 [DBG] Sent 62 bytes
no ex
11:59:27.800 [DBG] 32 bytes read
no ex
11:59:27.801 [DBG] Sent 31 bytes
no ex
11:59:28.449 [DBG] Sent 184 bytes
no ex
11:59:30.696 [DBG] 32 bytes read
no ex
11:59:30.696 [DBG] Sent 31 bytes
no ex
11:59:31.184 [DBG] 48 bytes read
no ex
11:59:31.199 [DBG] Sent 62 bytes
no ex
11:59:33.452 [DBG] Sent 184 bytes
no ex
11:59:33.714 [DBG] 32 bytes read
no ex
11:59:33.715 [DBG] Sent 31 bytes
no ex
11:59:36.969 [DBG] 32 bytes read
no ex
11:59:36.970 [DBG] Sent 31 bytes
no ex
11:59:38.456 [DBG] Sent 184 bytes
no ex
11:59:39.721 [DBG] 32 bytes read
no ex
11:59:39.721 [DBG] Sent 31 bytes
no ex
11:59:42.702 [DBG] 32 bytes read
no ex
11:59:42.702 [DBG] Sent 31 bytes
no ex
11:59:43.453 [DBG] Sent 184 bytes
no ex
11:59:45.717 [DBG] 32 bytes read
no ex
11:59:45.718 [DBG] Sent 31 bytes
no ex
11:59:48.452 [DBG] Sent 184 bytes
no ex
11:59:48.713 [DBG] 32 bytes read
no ex
11:59:48.713 [DBG] Sent 31 bytes
no ex
11:59:51.698 [DBG] 32 bytes read
no ex
11:59:51.698 [DBG] Sent 31 bytes
no ex
11:59:53.450 [DBG] Sent 184 bytes
no ex
11:59:54.728 [DBG] 32 bytes read
no ex
11:59:54.728 [DBG] Sent 31 bytes
no ex
11:59:57.713 [DBG] 32 bytes read
no ex
11:59:57.713 [DBG] Sent 31 bytes
no ex
11:59:58.454 [DBG] Sent 184 bytes
no ex
12:00:00.596 [DBG] 48 bytes read
no ex
12:00:00.612 [DBG] Sent 62 bytes
no ex
12:00:00.669 [DBG] 48 bytes read
no ex
12:00:00.681 [DBG] Sent 62 bytes
no ex
12:00:01.586 [DBG] 48 bytes read
no ex
12:00:01.590 [DBG] Sent 62 bytes
no ex
12:00:01.743 [DBG] 32 bytes read
no ex
12:00:01.744 [DBG] Sent 31 bytes
no ex
12:00:02.933 [DBG] 48 bytes read
no ex
12:00:02.940 [DBG] Sent 62 bytes
no ex
12:00:03.454 [DBG] Sent 184 bytes
no ex
12:00:03.772 [DBG] 32 bytes read
no ex
12:00:03.772 [DBG] Sent 31 bytes
no ex
12:00:04.840 [DBG] 48 bytes read
no ex
12:00:04.854 [DBG] Sent 62 bytes
no ex
12:00:05.683 [DBG] 48 bytes read
no ex
12:00:05.690 [DBG] Sent 62 bytes
no ex
12:00:06.848 [DBG] 32 bytes read
no ex
12:00:06.848 [DBG] Sent 31 bytes
no ex
12:00:08.460 [DBG] Sent 184 bytes
no ex
12:00:09.844 [DBG] 32 bytes read
no ex
12:00:09.844 [DBG] Sent 31 bytes
no ex
12:00:12.805 [DBG] 32 bytes read
no ex
12:00:12.805 [DBG] Sent 31 bytes
no ex
12:00:13.454 [DBG] Sent 184 bytes
no ex
12:00:14.834 [DBG] 59 bytes read
no ex
12:00:14.837 [DBG] Sent 72 bytes
no ex
12:00:15.816 [DBG] 32 bytes read
no ex
12:00:15.816 [DBG] Sent 31 bytes
no ex
12:00:18.447 [DBG] Sent 184 bytes
no ex
12:00:18.548 [DBG] 59 bytes read
no ex
12:00:18.567 [DBG] Sent 330 bytes
no ex
12:00:18.812 [DBG] 32 bytes read
no ex
12:00:18.813 [DBG] Sent 31 bytes
no ex
12:00:21.820 [DBG] 32 bytes read
no ex
12:00:21.820 [DBG] Sent 31 bytes
no ex
12:00:23.133 [DBG] 59 bytes read
no ex
12:00:23.139 [DBG] Sent 331 bytes
no ex
12:00:23.463 [DBG] Sent 184 bytes
no ex
12:00:24.804 [DBG] 32 bytes read
no ex
12:00:24.805 [DBG] Sent 31 bytes
no ex
12:00:27.834 [DBG] 32 bytes read
no ex
12:00:27.834 [DBG] Sent 31 bytes
no ex
12:00:28.450 [DBG] Sent 184 bytes
no ex
12:00:30.877 [DBG] 32 bytes read
no ex
12:00:30.877 [DBG] Sent 31 bytes
no ex
12:00:33.454 [DBG] Sent 184 bytes
no ex
12:00:33.852 [DBG] 32 bytes read
no ex
12:00:33.852 [DBG] Sent 31 bytes
no ex
12:00:35.451 [DBG] Sent 73 bytes
no ex
12:00:36.772 [DBG] 32 bytes read
no ex
12:00:36.772 [DBG] Sent 31 bytes
no ex
12:00:38.460 [DBG] Sent 184 bytes
no ex
12:00:39.878 [DBG] 32 bytes read
no ex
12:00:39.879 [DBG] Sent 31 bytes
no ex
12:00:42.808 [DBG] 32 bytes read
no ex
12:00:42.808 [DBG] Sent 31 bytes
no ex
12:00:43.454 [DBG] Sent 184 bytes
no ex
12:00:45.852 [DBG] 32 bytes read
no ex
12:00:45.852 [DBG] Sent 31 bytes
no ex
12:00:46.111 [DBG] 33 bytes read
no ex
12:00:46.119 [DBG] Sent 42 bytes
no ex
12:00:48.457 [DBG] Sent 184 bytes
no ex
12:00:48.872 [DBG] 32 bytes read
no ex
12:00:48.872 [DBG] Sent 31 bytes
no ex
12:00:51.837 [DBG] 32 bytes read
no ex
12:00:51.837 [DBG] Sent 31 bytes
no ex
12:00:52.586 [DBG] 48 bytes read
no ex
12:00:52.588 [DBG] 48 bytes read
no ex
12:00:52.590 [DBG] Sent 62 bytes
no ex
12:00:52.590 [DBG] Sent 62 bytes
no ex
12:00:53.462 [DBG] Sent 184 bytes
no ex
12:00:54.223 [DBG] 96 bytes read
no ex
12:00:54.230 [DBG] Sent 62 bytes
no ex
12:00:54.230 [DBG] Sent 62 bytes
no ex
12:00:54.729 [DBG] 32 bytes read
no ex
12:00:54.729 [DBG] Sent 31 bytes
no ex
12:00:55.148 [DBG] 96 bytes read
no ex
12:00:55.152 [DBG] Sent 62 bytes
no ex
12:00:55.152 [DBG] Sent 62 bytes
no ex
12:00:57.703 [DBG] 32 bytes read
no ex
12:00:57.703 [DBG] Sent 31 bytes
no ex
12:00:58.465 [DBG] Sent 184 bytes
no ex
12:01:00.161 [DBG] 48 bytes read
no ex
12:01:00.176 [DBG] Sent 62 bytes
no ex
12:01:00.244 [DBG] 48 bytes read
no ex
12:01:00.261 [DBG] Sent 62 bytes
no ex
12:01:00.701 [DBG] 32 bytes read
no ex
12:01:00.701 [DBG] Sent 31 bytes
no ex
12:01:03.465 [DBG] Sent 184 bytes
no ex
12:01:03.703 [DBG] 32 bytes read
no ex
12:01:03.704 [DBG] Sent 31 bytes
no ex
12:01:06.726 [DBG] 32 bytes read
no ex
12:01:06.726 [DBG] Sent 31 bytes
no ex
12:01:08.461 [DBG] Sent 184 bytes
no ex
12:01:09.730 [DBG] 32 bytes read
no ex
12:01:09.730 [DBG] Sent 31 bytes
no ex
12:01:12.737 [DBG] 32 bytes read
no ex
12:01:12.737 [DBG] Sent 31 bytes
no ex
12:01:13.455 [DBG] Sent 184 bytes
no ex
12:01:15.726 [DBG] 32 bytes read
no ex
12:01:15.726 [DBG] Sent 31 bytes
no ex
12:01:18.459 [DBG] Sent 184 bytes
no ex
12:01:18.701 [DBG] 32 bytes read
no ex
12:01:18.701 [DBG] Sent 31 bytes
no ex
12:01:21.788 [DBG] 32 bytes read
no ex
12:01:21.789 [DBG] Sent 31 bytes
no ex
12:01:23.467 [DBG] Sent 184 bytes
no ex
12:01:25.345 [DBG] 32 bytes read
no ex
12:01:25.346 [DBG] Sent 31 bytes
no ex
12:01:27.720 [DBG] 32 bytes read
no ex
12:01:27.720 [DBG] Sent 31 bytes
no ex
12:01:28.470 [DBG] Sent 184 bytes
no ex
12:01:29.002 [DBG] 48 bytes read
no ex
12:01:29.019 [DBG] Sent 62 bytes
no ex
12:01:29.878 [DBG] 48 bytes read
no ex
12:01:29.891 [DBG] Sent 62 bytes
no ex
12:01:30.731 [DBG] 32 bytes read
no ex
12:01:30.732 [DBG] Sent 31 bytes
no ex
12:01:33.460 [DBG] Sent 184 bytes
no ex
12:01:33.740 [DBG] 32 bytes read
no ex
12:01:33.740 [DBG] Sent 31 bytes
no ex
12:01:35.149 [INF] RunScan: 0
12:01:35.149 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:1)","Data":null,"DataObj":null}
12:01:36.980 [DBG] 32 bytes read
no ex
12:01:36.980 [DBG] Sent 31 bytes
no ex
12:01:38.465 [DBG] Sent 184 bytes
no ex
12:01:40.054 [DBG] 32 bytes read
no ex
12:01:40.054 [DBG] Sent 31 bytes
no ex
12:01:42.832 [DBG] 32 bytes read
no ex
12:01:42.833 [DBG] Sent 31 bytes
no ex
12:01:43.470 [DBG] Sent 184 bytes
no ex
12:01:45.788 [DBG] 32 bytes read
no ex
12:01:45.789 [DBG] Sent 31 bytes
no ex
12:01:48.463 [DBG] Sent 184 bytes
no ex
12:01:48.913 [DBG] 32 bytes read
no ex
12:01:48.913 [DBG] Sent 31 bytes
no ex
12:01:51.865 [DBG] 32 bytes read
no ex
12:01:51.865 [DBG] Sent 31 bytes
no ex
12:01:53.473 [DBG] Sent 184 bytes
no ex
12:01:55.103 [DBG] 32 bytes read
no ex
12:01:55.104 [DBG] Sent 31 bytes
no ex
12:01:57.702 [DBG] 32 bytes read
no ex
12:01:57.703 [DBG] Sent 31 bytes
no ex
12:01:58.464 [DBG] Sent 184 bytes
no ex
12:02:00.802 [DBG] 32 bytes read
no ex
12:02:00.802 [DBG] Sent 31 bytes
no ex
12:02:03.460 [DBG] Sent 184 bytes
no ex
12:02:03.714 [DBG] 32 bytes read
no ex
12:02:03.714 [DBG] Sent 31 bytes
no ex
12:02:06.743 [DBG] 32 bytes read
no ex
12:02:06.743 [DBG] Sent 31 bytes
no ex
12:02:08.464 [DBG] Sent 184 bytes
no ex
12:02:09.717 [DBG] 32 bytes read
no ex
12:02:09.717 [DBG] Sent 31 bytes
no ex
12:02:12.716 [DBG] 32 bytes read
no ex
12:02:12.717 [DBG] Sent 31 bytes
no ex
12:02:13.461 [DBG] Sent 184 bytes
no ex
12:02:15.715 [DBG] 32 bytes read
no ex
12:02:15.715 [DBG] Sent 31 bytes
no ex
12:02:18.474 [DBG] Sent 184 bytes
no ex
12:02:18.693 [DBG] 32 bytes read
no ex
12:02:18.693 [DBG] Sent 31 bytes
no ex
12:02:21.716 [DBG] 32 bytes read
no ex
12:02:21.717 [DBG] Sent 31 bytes
no ex
12:02:23.468 [DBG] Sent 184 bytes
no ex
12:02:24.710 [DBG] 32 bytes read
no ex
12:02:24.711 [DBG] Sent 31 bytes
no ex
12:02:27.743 [DBG] 32 bytes read
no ex
12:02:27.743 [DBG] Sent 31 bytes
no ex
12:02:28.471 [DBG] Sent 184 bytes
no ex
12:02:30.700 [DBG] 32 bytes read
no ex
12:02:30.700 [DBG] Sent 31 bytes
no ex
12:02:33.470 [DBG] Sent 184 bytes
no ex
12:02:33.909 [DBG] 32 bytes read
no ex
12:02:33.910 [DBG] Sent 31 bytes
no ex
12:02:36.316 [INF] RunHandleWinLoss: 0
12:02:36.316 [INF] CalculateResult: 20220805
12:02:36.316 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
12:02:36.690 [DBG] 32 bytes read
no ex
12:02:36.691 [DBG] Sent 31 bytes
no ex
12:02:38.480 [DBG] Sent 184 bytes
no ex
12:02:39.690 [DBG] 32 bytes read
no ex
12:02:39.690 [DBG] Sent 31 bytes
no ex
12:02:42.705 [DBG] 32 bytes read
no ex
12:02:42.706 [DBG] Sent 31 bytes
no ex
12:02:43.469 [DBG] Sent 184 bytes
no ex
12:02:45.720 [DBG] 32 bytes read
no ex
12:02:45.720 [DBG] Sent 31 bytes
no ex
12:02:48.472 [DBG] Sent 184 bytes
no ex
12:02:48.709 [DBG] 32 bytes read
no ex
12:02:48.709 [DBG] Sent 31 bytes
no ex
12:02:51.690 [DBG] 32 bytes read
no ex
12:02:51.690 [DBG] Sent 31 bytes
no ex
12:02:53.478 [DBG] Sent 184 bytes
no ex
12:02:54.682 [DBG] 32 bytes read
no ex
12:02:54.683 [DBG] Sent 31 bytes
no ex
12:02:57.783 [DBG] 32 bytes read
no ex
12:02:57.783 [DBG] Sent 31 bytes
no ex
12:02:58.473 [DBG] Sent 184 bytes
no ex
12:03:00.684 [DBG] 32 bytes read
no ex
12:03:00.684 [DBG] Sent 31 bytes
no ex
12:03:03.482 [DBG] Sent 184 bytes
no ex
12:03:03.716 [DBG] 32 bytes read
no ex
12:03:03.716 [DBG] Sent 31 bytes
no ex
12:03:06.698 [DBG] 32 bytes read
no ex
12:03:06.699 [DBG] Sent 31 bytes
no ex
12:03:08.483 [DBG] Sent 184 bytes
no ex
12:03:09.953 [DBG] 32 bytes read
no ex
12:03:09.953 [DBG] Sent 31 bytes
no ex
12:03:12.693 [DBG] 32 bytes read
no ex
12:03:12.694 [DBG] Sent 31 bytes
no ex
12:03:13.471 [DBG] Sent 184 bytes
no ex
12:03:15.688 [DBG] 32 bytes read
no ex
12:03:15.688 [DBG] Sent 31 bytes
no ex
12:03:18.475 [DBG] Sent 184 bytes
no ex
12:03:18.686 [DBG] 32 bytes read
no ex
12:03:18.686 [DBG] Sent 31 bytes
no ex
12:03:21.698 [DBG] 32 bytes read
no ex
12:03:21.699 [DBG] Sent 31 bytes
no ex
12:03:23.477 [DBG] Sent 184 bytes
no ex
12:03:24.698 [DBG] 32 bytes read
no ex
12:03:24.698 [DBG] Sent 31 bytes
no ex
12:03:27.690 [DBG] 32 bytes read
no ex
12:03:27.691 [DBG] Sent 31 bytes
no ex
12:03:28.481 [DBG] Sent 184 bytes
no ex
12:03:30.691 [DBG] 32 bytes read
no ex
12:03:30.691 [DBG] Sent 31 bytes
no ex
12:03:33.487 [DBG] Sent 184 bytes
no ex
12:03:33.682 [DBG] 32 bytes read
no ex
12:03:33.683 [DBG] Sent 31 bytes
no ex
12:03:36.695 [DBG] 32 bytes read
no ex
12:03:36.695 [DBG] Sent 31 bytes
no ex
12:03:38.488 [DBG] Sent 184 bytes
no ex
12:03:39.687 [DBG] 33 bytes read
no ex
12:03:39.687 [DBG] Sent 32 bytes
no ex
12:03:42.860 [DBG] 33 bytes read
no ex
12:03:42.860 [DBG] Sent 32 bytes
no ex
12:03:43.476 [DBG] Sent 184 bytes
no ex
12:03:45.938 [DBG] 33 bytes read
no ex
12:03:45.938 [DBG] Sent 32 bytes
no ex
12:03:48.479 [DBG] Sent 184 bytes
no ex
12:03:49.102 [DBG] 33 bytes read
no ex
12:03:49.102 [DBG] Sent 32 bytes
no ex
12:03:53.278 [DBG] 33 bytes read
no ex
12:03:53.279 [DBG] Sent 32 bytes
no ex
12:03:53.485 [DBG] Sent 184 bytes
no ex
12:03:55.806 [DBG] 33 bytes read
no ex
12:03:55.807 [DBG] Sent 32 bytes
no ex
12:03:58.488 [DBG] Sent 184 bytes
no ex
12:04:00.830 [DBG] Sent 4 bytes
no ex
12:04:00.831 [DBG] Swallowing ObjectDisposedException
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
12:04:07.604 [DBG] Client connected from 127.0.0.1:37156
no ex
12:04:07.604 [DBG] 869 bytes read
no ex
12:04:07.604 [DBG] Building Hybi-14 Response
no ex
12:04:07.604 [DBG] Sent 129 bytes
no ex
12:04:08.135 [DBG] 33 bytes read
no ex
12:04:08.135 [DBG] Sent 32 bytes
no ex
12:04:08.138 [DBG] 116 bytes read
no ex
12:04:08.138 [INF] GET: http://127.0.0.1:8081/api?c=3&un=trautrautii&pw=e10adc3949ba59abbe56e057f20f883e&pf=web&at=
12:04:08.153 [INF] GET response: {"success":true,"message":null,"errorCode":"0","data":null,"sessionKey":"eyJuaWNrbmFtZSI6Ildvd3d5MTkiLCJhdmF0YXIiOiIwIiwidmluVG90YWwiOjAsInh1VG90YWwiOjUwMDAwMCwidmlwcG9pbnQiOjAsInZpcHBvaW50U2F2ZSI6MCwiY3JlYXRlVGltZSI6IjA1LTA4LTIwMjIiLCJpcEFkZHJlc3MiOiIxMjcuMC4wLjEiLCJjZXJ0aWZpY2F0ZSI6ZmFsc2UsImx1Y2t5Um90YXRlIjowLCJkYWlMeSI6MCwibW9iaWxlU2VjdXJlIjowLCJiaXJ0aGRheSI6IiIsImFwcFNlY3VyZSI6MCwidXNlcm5hbWUiOiJUcmF1dHJhdXRpaSIsImVtYWlsIjowLCJhZGRyZXNzIjpudWxsLCJ2ZXJpZnlNb2JpbGUiOmZhbHNlfQ==","accessToken":"fcf3b74b50cbb10069bd73c0412ef37e"}

12:04:08.159 [DBG] Sent 5730 bytes
no ex
12:04:08.485 [DBG] Sent 184 bytes
no ex
12:04:11.231 [DBG] 33 bytes read
no ex
12:04:11.232 [DBG] Sent 32 bytes
no ex
12:04:13.480 [DBG] Sent 184 bytes
no ex
12:04:15.210 [DBG] 33 bytes read
no ex
12:04:15.210 [DBG] Sent 32 bytes
no ex
12:04:15.493 [DBG] Sent 75 bytes
no ex
12:04:17.122 [DBG] 33 bytes read
no ex
12:04:17.122 [DBG] Sent 32 bytes
no ex
12:04:18.481 [DBG] Sent 184 bytes
no ex
12:04:20.128 [DBG] 33 bytes read
no ex
12:04:20.129 [DBG] Sent 32 bytes
no ex
12:04:23.107 [DBG] 33 bytes read
no ex
12:04:23.108 [DBG] Sent 32 bytes
no ex
12:04:23.484 [DBG] Sent 184 bytes
no ex
12:04:26.141 [DBG] 33 bytes read
no ex
12:04:26.142 [DBG] Sent 32 bytes
no ex
12:04:28.488 [DBG] Sent 184 bytes
no ex
12:04:29.114 [DBG] 33 bytes read
no ex
12:04:29.114 [DBG] Sent 32 bytes
no ex
12:04:32.109 [DBG] 33 bytes read
no ex
12:04:32.109 [DBG] Sent 32 bytes
no ex
12:04:33.494 [DBG] Sent 184 bytes
no ex
12:04:33.494 [DBG] Sent 73 bytes
no ex
12:04:35.092 [DBG] 33 bytes read
no ex
12:04:35.092 [DBG] Sent 32 bytes
no ex
12:04:38.121 [DBG] 33 bytes read
no ex
12:04:38.121 [DBG] Sent 32 bytes
no ex
12:04:38.494 [DBG] Sent 184 bytes
no ex
12:04:43.143 [DBG] Sent 4 bytes
no ex
12:04:43.143 [DBG] Swallowing ObjectDisposedException
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
12:06:35.150 [INF] RunScan: 0
12:06:35.150 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:6)","Data":null,"DataObj":null}
12:07:36.318 [INF] RunHandleWinLoss: 0
12:07:36.318 [INF] CalculateResult: 20220805
12:07:36.318 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
12:11:35.150 [INF] RunScan: 0
12:11:35.150 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:11)","Data":null,"DataObj":null}
12:12:36.319 [INF] RunHandleWinLoss: 0
12:12:36.319 [INF] CalculateResult: 20220805
12:12:36.319 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
12:16:35.150 [INF] RunScan: 0
12:16:35.150 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:16)","Data":null,"DataObj":null}
12:17:36.321 [INF] RunHandleWinLoss: 0
12:17:36.321 [INF] CalculateResult: 20220805
12:17:36.321 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
12:21:35.150 [INF] RunScan: 0
12:21:35.150 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:21)","Data":null,"DataObj":null}
12:22:36.323 [INF] RunHandleWinLoss: 0
12:22:36.323 [INF] CalculateResult: 20220805
12:22:36.323 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
12:26:35.150 [INF] RunScan: 0
12:26:35.150 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:26)","Data":null,"DataObj":null}
12:27:36.324 [INF] RunHandleWinLoss: 0
12:27:36.324 [INF] CalculateResult: 20220805
12:27:36.324 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
12:31:35.151 [INF] RunScan: 0
12:31:35.151 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:31)","Data":null,"DataObj":null}
12:32:36.326 [INF] RunHandleWinLoss: 0
12:32:36.326 [INF] CalculateResult: 20220805
12:32:36.326 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
12:36:35.151 [INF] RunScan: 0
12:36:35.151 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:36)","Data":null,"DataObj":null}
12:37:36.327 [INF] RunHandleWinLoss: 0
12:37:36.327 [INF] CalculateResult: 20220805
12:37:36.327 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
12:38:59.192 [DBG] Client connected from 127.0.0.1:41600
no ex
12:38:59.192 [DBG] 1189 bytes read
no ex
12:38:59.192 [DBG] Building Hybi-14 Response
no ex
12:38:59.193 [DBG] Sent 129 bytes
no ex
12:39:00.473 [DBG] 144 bytes read
no ex
12:39:00.473 [DBG] Sent 30 bytes
no ex
12:39:00.473 [INF] GET: http://127.0.0.1:8081/api?c=3&un=cuoctinh94&pw=a8e1560b56e0284a54727be2fe3c8853&pf=web&at=
12:39:00.482 [INF] GET response: {"success":true,"message":null,"errorCode":"0","data":null,"sessionKey":"eyJuaWNrbmFtZSI6InRpbmh0aW5odGluaCIsImF2YXRhciI6IjAiLCJ2aW5Ub3RhbCI6MCwieHVUb3RhbCI6NTAwMDAwLCJ2aXBwb2ludCI6MCwidmlwcG9pbnRTYXZlIjowLCJjcmVhdGVUaW1lIjoiMDUtMDgtMjAyMiIsImlwQWRkcmVzcyI6IjEyNy4wLjAuMSIsImNlcnRpZmljYXRlIjpmYWxzZSwibHVja3lSb3RhdGUiOjAsImRhaUx5IjowLCJtb2JpbGVTZWN1cmUiOjAsImJpcnRoZGF5IjoiIiwiYXBwU2VjdXJlIjowLCJ1c2VybmFtZSI6ImN1b2N0aW5oOTQiLCJlbWFpbCI6MCwiYWRkcmVzcyI6bnVsbCwidmVyaWZ5TW9iaWxlIjpmYWxzZX0=","accessToken":"0fa3e4c783c686510ed7aa50d1d8179f"}

12:39:00.492 [DBG] Sent 5729 bytes
no ex
12:39:03.463 [DBG] 31 bytes read
no ex
12:39:03.463 [DBG] Sent 30 bytes
no ex
12:39:03.751 [DBG] Sent 184 bytes
no ex
12:39:06.502 [DBG] 31 bytes read
no ex
12:39:06.502 [DBG] Sent 30 bytes
no ex
12:39:08.757 [DBG] Sent 184 bytes
no ex
12:39:09.476 [DBG] 31 bytes read
no ex
12:39:09.477 [DBG] Sent 30 bytes
no ex
12:39:12.472 [DBG] 31 bytes read
no ex
12:39:12.472 [DBG] Sent 30 bytes
no ex
12:39:13.763 [DBG] Sent 184 bytes
no ex
12:39:15.480 [DBG] 31 bytes read
no ex
12:39:15.480 [DBG] Sent 30 bytes
no ex
12:39:18.516 [DBG] 31 bytes read
no ex
12:39:18.516 [DBG] Sent 30 bytes
no ex
12:39:18.766 [DBG] Sent 184 bytes
no ex
12:39:21.475 [DBG] 31 bytes read
no ex
12:39:21.475 [DBG] Sent 30 bytes
no ex
12:39:23.759 [DBG] Sent 184 bytes
no ex
12:39:24.467 [DBG] 31 bytes read
no ex
12:39:24.468 [DBG] Sent 30 bytes
no ex
12:39:27.475 [DBG] 31 bytes read
no ex
12:39:27.475 [DBG] Sent 30 bytes
no ex
12:39:28.765 [DBG] Sent 184 bytes
no ex
12:39:30.462 [DBG] 31 bytes read
no ex
12:39:30.462 [DBG] Sent 30 bytes
no ex
12:39:33.463 [DBG] 31 bytes read
no ex
12:39:33.463 [DBG] Sent 30 bytes
no ex
12:39:33.755 [DBG] Sent 184 bytes
no ex
12:39:36.470 [DBG] 31 bytes read
no ex
12:39:36.470 [DBG] Sent 30 bytes
no ex
12:39:38.759 [DBG] Sent 184 bytes
no ex
12:39:39.465 [DBG] 31 bytes read
no ex
12:39:39.466 [DBG] Sent 30 bytes
no ex
12:39:41.554 [DBG] 8 bytes read
no ex
12:39:41.554 [DBG] Sent 4 bytes
no ex
12:41:35.151 [INF] RunScan: 0
12:41:35.151 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:41)","Data":null,"DataObj":null}
12:42:36.329 [INF] RunHandleWinLoss: 0
12:42:36.329 [INF] CalculateResult: 20220805
12:42:36.329 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
12:46:35.151 [INF] RunScan: 0
12:46:35.151 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:46)","Data":null,"DataObj":null}
12:47:36.330 [INF] RunHandleWinLoss: 0
12:47:36.330 [INF] CalculateResult: 20220805
12:47:36.330 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
12:51:35.151 [INF] RunScan: 0
12:51:35.152 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:51)","Data":null,"DataObj":null}
12:52:36.332 [INF] RunHandleWinLoss: 0
12:52:36.332 [INF] CalculateResult: 20220805
12:52:36.332 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
12:56:35.152 [INF] RunScan: 0
12:56:35.152 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:56)","Data":null,"DataObj":null}
12:57:36.334 [INF] RunHandleWinLoss: 0
12:57:36.334 [INF] CalculateResult: 20220805
12:57:36.335 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
13:01:35.152 [INF] RunScan: 0
13:01:35.152 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:1)","Data":null,"DataObj":null}
13:02:36.336 [INF] RunHandleWinLoss: 0
13:02:36.336 [INF] CalculateResult: 20220805
13:02:36.336 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
13:06:35.152 [INF] RunScan: 0
13:06:35.152 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:6)","Data":null,"DataObj":null}
13:07:36.337 [INF] RunHandleWinLoss: 0
13:07:36.337 [INF] CalculateResult: 20220805
13:07:36.337 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
13:11:35.152 [INF] RunScan: 0
13:11:35.153 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:11)","Data":null,"DataObj":null}
13:12:36.339 [INF] RunHandleWinLoss: 0
13:12:36.339 [INF] CalculateResult: 20220805
13:12:36.339 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
13:16:35.153 [INF] RunScan: 0
13:16:35.153 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:16)","Data":null,"DataObj":null}
13:17:36.341 [INF] RunHandleWinLoss: 0
13:17:36.341 [INF] CalculateResult: 20220805
13:17:36.341 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
13:18:47.034 [DBG] Client connected from 192.241.215.36:40036
no ex
13:18:47.034 [DBG] 26 bytes read
no ex
13:18:57.035 [DBG] 0 bytes read. Closing.
no ex
13:21:35.153 [INF] RunScan: 0
13:21:35.153 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:21)","Data":null,"DataObj":null}
13:22:36.343 [INF] RunHandleWinLoss: 0
13:22:36.343 [INF] CalculateResult: 20220805
13:22:36.343 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
13:26:35.153 [INF] RunScan: 0
13:26:35.154 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:26)","Data":null,"DataObj":null}
13:27:36.344 [INF] RunHandleWinLoss: 0
13:27:36.345 [INF] CalculateResult: 20220805
13:27:36.345 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
13:31:35.154 [INF] RunScan: 0
13:31:35.154 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:31)","Data":null,"DataObj":null}
13:32:36.346 [INF] RunHandleWinLoss: 0
13:32:36.346 [INF] CalculateResult: 20220805
13:32:36.346 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
13:36:35.154 [INF] RunScan: 0
13:36:35.154 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:36)","Data":null,"DataObj":null}
13:37:36.347 [INF] RunHandleWinLoss: 0
13:37:36.348 [INF] CalculateResult: 20220805
13:37:36.348 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
13:40:51.717 [DBG] Client connected from 127.0.0.1:47994
no ex
13:40:51.717 [DBG] 1169 bytes read
no ex
13:40:51.718 [DBG] Building Hybi-14 Response
no ex
13:40:51.718 [DBG] Sent 129 bytes
no ex
13:40:51.935 [DBG] 142 bytes read
no ex
13:40:51.935 [DBG] Sent 30 bytes
no ex
13:40:51.935 [INF] GET: http://127.0.0.1:8081/api?c=3&un=tien0380&pw=9e7f9beb5041eae804813098c86b17d2&pf=web&at=
13:40:51.946 [INF] GET response: {"success":true,"message":null,"errorCode":"0","data":null,"sessionKey":"eyJuaWNrbmFtZSI6InRpZW4wMzgwMzkiLCJhdmF0YXIiOiIwIiwidmluVG90YWwiOjAsInh1VG90YWwiOjUwMDAwMCwidmlwcG9pbnQiOjAsInZpcHBvaW50U2F2ZSI6MCwiY3JlYXRlVGltZSI6IjA1LTA4LTIwMjIiLCJpcEFkZHJlc3MiOiIxMjcuMC4wLjEiLCJjZXJ0aWZpY2F0ZSI6ZmFsc2UsImx1Y2t5Um90YXRlIjowLCJkYWlMeSI6MCwibW9iaWxlU2VjdXJlIjowLCJiaXJ0aGRheSI6IiIsImFwcFNlY3VyZSI6MCwidXNlcm5hbWUiOiJ0aWVuMDM4MCIsImVtYWlsIjowLCJhZGRyZXNzIjpudWxsLCJ2ZXJpZnlNb2JpbGUiOnRydWV9","accessToken":"4323bd1816d0eaa334b37a20bd19d578"}

13:40:51.962 [DBG] Sent 5727 bytes
no ex
13:40:54.289 [DBG] Sent 184 bytes
no ex
13:40:54.910 [DBG] 31 bytes read
no ex
13:40:54.910 [DBG] Sent 30 bytes
no ex
13:40:57.911 [DBG] 31 bytes read
no ex
13:40:57.911 [DBG] Sent 30 bytes
no ex
13:40:59.278 [DBG] Sent 184 bytes
no ex
13:41:00.913 [DBG] 31 bytes read
no ex
13:41:00.913 [DBG] Sent 30 bytes
no ex
13:41:03.923 [DBG] 31 bytes read
no ex
13:41:03.924 [DBG] Sent 30 bytes
no ex
13:41:04.282 [DBG] Sent 184 bytes
no ex
13:41:06.930 [DBG] 31 bytes read
no ex
13:41:06.930 [DBG] Sent 30 bytes
no ex
13:41:09.281 [DBG] Sent 184 bytes
no ex
13:41:09.931 [DBG] 31 bytes read
no ex
13:41:09.931 [DBG] Sent 30 bytes
no ex
13:41:12.938 [DBG] 31 bytes read
no ex
13:41:12.938 [DBG] Sent 30 bytes
no ex
13:41:14.284 [DBG] Sent 184 bytes
no ex
13:41:15.930 [DBG] 31 bytes read
no ex
13:41:15.931 [DBG] Sent 30 bytes
no ex
13:41:18.937 [DBG] 31 bytes read
no ex
13:41:18.937 [DBG] Sent 30 bytes
no ex
13:41:19.285 [DBG] Sent 184 bytes
no ex
13:41:21.947 [DBG] 31 bytes read
no ex
13:41:21.947 [DBG] Sent 30 bytes
no ex
13:41:24.289 [DBG] Sent 184 bytes
no ex
13:41:24.937 [DBG] 31 bytes read
no ex
13:41:24.937 [DBG] Sent 30 bytes
no ex
13:41:27.997 [DBG] 31 bytes read
no ex
13:41:27.997 [DBG] Sent 30 bytes
no ex
13:41:29.288 [DBG] Sent 184 bytes
no ex
13:41:31.016 [DBG] 31 bytes read
no ex
13:41:31.016 [DBG] Sent 30 bytes
no ex
13:41:33.964 [DBG] 31 bytes read
no ex
13:41:33.965 [DBG] Sent 30 bytes
no ex
13:41:34.298 [DBG] Sent 184 bytes
no ex
13:41:35.154 [INF] RunScan: 0
13:41:35.154 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:41)","Data":null,"DataObj":null}
13:41:36.936 [DBG] 31 bytes read
no ex
13:41:36.937 [DBG] Sent 30 bytes
no ex
13:41:39.300 [DBG] Sent 184 bytes
no ex
13:41:39.936 [DBG] 31 bytes read
no ex
13:41:39.936 [DBG] Sent 30 bytes
no ex
13:41:42.938 [DBG] 31 bytes read
no ex
13:41:42.939 [DBG] Sent 30 bytes
no ex
13:41:44.300 [DBG] Sent 184 bytes
no ex
13:41:45.935 [DBG] 31 bytes read
no ex
13:41:45.935 [DBG] Sent 30 bytes
no ex
13:41:48.957 [DBG] 31 bytes read
no ex
13:41:48.957 [DBG] Sent 30 bytes
no ex
13:41:49.296 [DBG] Sent 184 bytes
no ex
13:41:51.944 [DBG] 31 bytes read
no ex
13:41:51.945 [DBG] Sent 30 bytes
no ex
13:41:54.299 [DBG] Sent 184 bytes
no ex
13:41:54.949 [DBG] 31 bytes read
no ex
13:41:54.949 [DBG] Sent 30 bytes
no ex
13:41:57.937 [DBG] 31 bytes read
no ex
13:41:57.938 [DBG] Sent 30 bytes
no ex
13:41:59.306 [DBG] Sent 184 bytes
no ex
13:42:00.940 [DBG] 31 bytes read
no ex
13:42:00.941 [DBG] Sent 30 bytes
no ex
13:42:03.946 [DBG] 31 bytes read
no ex
13:42:03.946 [DBG] Sent 30 bytes
no ex
13:42:04.294 [DBG] Sent 184 bytes
no ex
13:42:06.944 [DBG] 31 bytes read
no ex
13:42:06.945 [DBG] Sent 30 bytes
no ex
13:42:09.298 [DBG] Sent 184 bytes
no ex
13:42:09.941 [DBG] 31 bytes read
no ex
13:42:09.941 [DBG] Sent 30 bytes
no ex
13:42:12.303 [DBG] Sent 75 bytes
no ex
13:42:13.470 [DBG] 31 bytes read
no ex
13:42:13.471 [DBG] Sent 30 bytes
no ex
13:42:14.292 [DBG] Sent 184 bytes
no ex
13:42:16.451 [DBG] 31 bytes read
no ex
13:42:16.452 [DBG] Sent 30 bytes
no ex
13:42:19.301 [DBG] Sent 184 bytes
no ex
13:42:19.671 [DBG] 31 bytes read
no ex
13:42:19.671 [DBG] Sent 30 bytes
no ex
13:42:24.136 [DBG] 31 bytes read
no ex
13:42:24.136 [DBG] Sent 30 bytes
no ex
13:42:24.308 [DBG] Sent 184 bytes
no ex
13:42:27.141 [DBG] 31 bytes read
no ex
13:42:27.142 [DBG] Sent 30 bytes
no ex
13:42:29.296 [DBG] Sent 184 bytes
no ex
13:42:30.154 [DBG] 31 bytes read
no ex
13:42:30.154 [DBG] Sent 30 bytes
no ex
13:42:33.167 [DBG] 31 bytes read
no ex
13:42:33.167 [DBG] Sent 30 bytes
no ex
13:42:34.299 [DBG] Sent 184 bytes
no ex
13:42:36.168 [DBG] 31 bytes read
no ex
13:42:36.168 [DBG] Sent 30 bytes
no ex
13:42:36.349 [INF] RunHandleWinLoss: 0
13:42:36.349 [INF] CalculateResult: 20220805
13:42:36.349 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
13:42:39.168 [DBG] 31 bytes read
no ex
13:42:39.168 [DBG] Sent 30 bytes
no ex
13:42:39.306 [DBG] Sent 184 bytes
no ex
13:42:42.173 [DBG] 31 bytes read
no ex
13:42:42.173 [DBG] Sent 30 bytes
no ex
13:42:44.309 [DBG] Sent 184 bytes
no ex
13:42:45.173 [DBG] 31 bytes read
no ex
13:42:45.173 [DBG] Sent 30 bytes
no ex
13:42:48.177 [DBG] 31 bytes read
no ex
13:42:48.178 [DBG] Sent 30 bytes
no ex
13:42:49.305 [DBG] Sent 184 bytes
no ex
13:42:51.176 [DBG] 31 bytes read
no ex
13:42:51.176 [DBG] Sent 30 bytes
no ex
13:42:54.285 [DBG] 31 bytes read
no ex
13:42:54.285 [DBG] Sent 30 bytes
no ex
13:42:54.310 [DBG] Sent 184 bytes
no ex
13:42:57.179 [DBG] 31 bytes read
no ex
13:42:57.179 [DBG] Sent 30 bytes
no ex
13:42:59.298 [DBG] Sent 184 bytes
no ex
13:43:00.189 [DBG] 31 bytes read
no ex
13:43:00.190 [DBG] Sent 30 bytes
no ex
13:43:03.181 [DBG] 31 bytes read
no ex
13:43:03.182 [DBG] Sent 30 bytes
no ex
13:43:04.311 [DBG] Sent 184 bytes
no ex
13:43:06.189 [DBG] 31 bytes read
no ex
13:43:06.189 [DBG] Sent 30 bytes
no ex
13:43:09.177 [DBG] 31 bytes read
no ex
13:43:09.177 [DBG] Sent 30 bytes
no ex
13:43:09.298 [DBG] Sent 184 bytes
no ex
13:43:12.180 [DBG] 31 bytes read
no ex
13:43:12.180 [DBG] Sent 30 bytes
no ex
13:43:14.307 [DBG] Sent 184 bytes
no ex
13:43:15.197 [DBG] 31 bytes read
no ex
13:43:15.197 [DBG] Sent 30 bytes
no ex
13:43:18.228 [DBG] 31 bytes read
no ex
13:43:18.228 [DBG] Sent 30 bytes
no ex
13:43:19.311 [DBG] Sent 184 bytes
no ex
13:43:21.178 [DBG] 31 bytes read
no ex
13:43:21.178 [DBG] Sent 30 bytes
no ex
13:43:24.214 [DBG] 31 bytes read
no ex
13:43:24.215 [DBG] Sent 30 bytes
no ex
13:43:24.299 [DBG] Sent 184 bytes
no ex
13:43:27.297 [DBG] 31 bytes read
no ex
13:43:27.298 [DBG] Sent 30 bytes
no ex
13:43:29.314 [DBG] Sent 184 bytes
no ex
13:43:30.876 [DBG] 31 bytes read
no ex
13:43:30.876 [DBG] Sent 30 bytes
no ex
13:43:34.305 [DBG] Sent 184 bytes
no ex
13:43:35.899 [DBG] Sent 4 bytes
no ex
13:43:35.900 [DBG] Swallowing ObjectDisposedException
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
13:46:35.154 [INF] RunScan: 0
13:46:35.154 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:46)","Data":null,"DataObj":null}
13:47:03.437 [DBG] Client connected from 185.220.100.241:4894
no ex
13:47:12.740 [DBG] 227 bytes read
no ex
13:47:12.741 [DBG] Error while reading
Fleck.WebSocketException: Exception of type 'Fleck.WebSocketException' was thrown.
   at Fleck.HandlerFactory.BuildHandler(WebSocketHttpRequest request, Action`1 onMessage, Action onClose, Action`1 onBinary, Action`1 onPing, Action`1 onPong) in /var/app/banca/Fleck/HandlerFactory.cs:line 25
   at Fleck.WebSocketServer.<>c__DisplayClass42_0.<OnClientConnect>b__1(WebSocketHttpRequest r) in /var/app/banca/Fleck/WebSocketServer.cs:line 164
   at Fleck.WebSocketConnection.CreateHandler(IEnumerable`1 data) in /var/app/banca/Fleck/WebSocketConnection.cs:line 159
   at Fleck.WebSocketConnection.<>c__DisplayClass61_0.<Read>b__0(Int32 r) in /var/app/banca/Fleck/WebSocketConnection.cs:line 193
   at Fleck.SocketWrapper.<>c__DisplayClass24_0.<Receive>b__1(Task`1 t) in /var/app/banca/Fleck/SocketWrapper.cs:line 126
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
13:47:18.156 [DBG] Client connected from 209.141.55.26:55934
no ex
13:47:22.383 [DBG] 26 bytes read
no ex
13:47:24.802 [DBG] 0 bytes read. Closing.
no ex
13:47:35.109 [DBG] Client connected from 185.220.100.249:13510
no ex
13:47:36.351 [INF] RunHandleWinLoss: 0
13:47:36.351 [INF] CalculateResult: 20220805
13:47:36.351 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
13:47:38.592 [DBG] 44 bytes read
no ex
13:47:42.069 [DBG] 0 bytes read. Closing.
no ex
13:47:42.226 [DBG] Client connected from 185.220.100.249:3766
no ex
13:47:49.303 [DBG] 53 bytes read
no ex
13:47:49.805 [DBG] 0 bytes read. Closing.
no ex
13:48:00.176 [DBG] Client connected from 23.129.64.131:6965
no ex
13:48:08.177 [DBG] Client connected from 199.249.230.87:33418
no ex
13:48:09.653 [DBG] Client connected from 193.214.214.202:46364
no ex
13:48:12.855 [DBG] 0 bytes read. Closing.
no ex
13:48:13.034 [DBG] Client connected from 193.214.214.202:46370
no ex
13:48:16.164 [DBG] 239 bytes read
no ex
13:48:19.008 [DBG] 0 bytes read. Closing.
no ex
13:48:22.189 [DBG] 0 bytes read. Closing.
no ex
13:48:33.146 [DBG] 0 bytes read. Closing.
no ex
13:51:35.155 [INF] RunScan: 0
13:51:35.155 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:51)","Data":null,"DataObj":null}
13:52:36.352 [INF] RunHandleWinLoss: 0
13:52:36.352 [INF] CalculateResult: 20220805
13:52:36.352 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
13:56:35.155 [INF] RunScan: 0
13:56:35.155 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:56)","Data":null,"DataObj":null}
13:57:36.354 [INF] RunHandleWinLoss: 0
13:57:36.354 [INF] CalculateResult: 20220805
13:57:36.354 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
14:01:35.155 [INF] RunScan: 0
14:01:35.155 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:1)","Data":null,"DataObj":null}
14:02:36.356 [INF] RunHandleWinLoss: 0
14:02:36.356 [INF] CalculateResult: 20220805
14:02:36.356 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
14:06:35.155 [INF] RunScan: 0
14:06:35.156 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:6)","Data":null,"DataObj":null}
14:07:36.357 [INF] RunHandleWinLoss: 0
14:07:36.357 [INF] CalculateResult: 20220805
14:07:36.357 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
14:11:35.156 [INF] RunScan: 0
14:11:35.156 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:11)","Data":null,"DataObj":null}
14:12:36.359 [INF] RunHandleWinLoss: 0
14:12:36.359 [INF] CalculateResult: 20220805
14:12:36.359 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
14:16:35.157 [INF] RunScan: 0
14:16:35.157 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:16)","Data":null,"DataObj":null}
14:17:36.361 [INF] RunHandleWinLoss: 0
14:17:36.361 [INF] CalculateResult: 20220805
14:17:36.361 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
14:21:35.157 [INF] RunScan: 0
14:21:35.157 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:21)","Data":null,"DataObj":null}
14:22:36.362 [INF] RunHandleWinLoss: 0
14:22:36.362 [INF] CalculateResult: 20220805
14:22:36.362 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
14:26:35.157 [INF] RunScan: 0
14:26:35.157 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:26)","Data":null,"DataObj":null}
14:27:36.363 [INF] RunHandleWinLoss: 0
14:27:36.364 [INF] CalculateResult: 20220805
14:27:36.364 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
14:31:35.157 [INF] RunScan: 0
14:31:35.157 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:31)","Data":null,"DataObj":null}
14:32:36.365 [INF] RunHandleWinLoss: 0
14:32:36.366 [INF] CalculateResult: 20220805
14:32:36.366 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
14:36:35.158 [INF] RunScan: 0
14:36:35.158 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:36)","Data":null,"DataObj":null}
14:37:36.367 [INF] RunHandleWinLoss: 0
14:37:36.367 [INF] CalculateResult: 20220805
14:37:36.367 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
14:41:35.158 [INF] RunScan: 0
14:41:35.158 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:41)","Data":null,"DataObj":null}
14:42:36.369 [INF] RunHandleWinLoss: 0
14:42:36.369 [INF] CalculateResult: 20220805
14:42:36.369 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
14:46:35.158 [INF] RunScan: 0
14:46:35.158 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:46)","Data":null,"DataObj":null}
14:47:36.371 [INF] RunHandleWinLoss: 0
14:47:36.371 [INF] CalculateResult: 20220805
14:47:36.371 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
14:51:35.158 [INF] RunScan: 0
14:51:35.158 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:51)","Data":null,"DataObj":null}
14:52:21.794 [DBG] Client connected from 139.59.86.252:51714
no ex
14:52:21.794 [DBG] 204 bytes read
no ex
14:52:21.794 [DBG] Error while reading
Fleck.WebSocketException: Exception of type 'Fleck.WebSocketException' was thrown.
   at Fleck.HandlerFactory.BuildHandler(WebSocketHttpRequest request, Action`1 onMessage, Action onClose, Action`1 onBinary, Action`1 onPing, Action`1 onPong) in /var/app/banca/Fleck/HandlerFactory.cs:line 25
   at Fleck.WebSocketServer.<>c__DisplayClass42_0.<OnClientConnect>b__1(WebSocketHttpRequest r) in /var/app/banca/Fleck/WebSocketServer.cs:line 164
   at Fleck.WebSocketConnection.CreateHandler(IEnumerable`1 data) in /var/app/banca/Fleck/WebSocketConnection.cs:line 159
   at Fleck.WebSocketConnection.<>c__DisplayClass61_0.<Read>b__0(Int32 r) in /var/app/banca/Fleck/WebSocketConnection.cs:line 193
   at Fleck.SocketWrapper.<>c__DisplayClass24_0.<Receive>b__1(Task`1 t) in /var/app/banca/Fleck/SocketWrapper.cs:line 126
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
14:52:36.373 [INF] RunHandleWinLoss: 0
14:52:36.373 [INF] CalculateResult: 20220805
14:52:36.373 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
14:52:52.499 [DBG] Client connected from 139.59.86.252:52840
no ex
14:52:52.499 [DBG] 6 bytes read
no ex
14:53:07.500 [DBG] 0 bytes read. Closing.
no ex
14:56:35.158 [INF] RunScan: 0
14:56:35.158 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:56)","Data":null,"DataObj":null}
14:57:36.374 [INF] RunHandleWinLoss: 0
14:57:36.374 [INF] CalculateResult: 20220805
14:57:36.374 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
15:01:35.159 [INF] RunScan: 0
15:01:35.159 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:1)","Data":null,"DataObj":null}
15:02:36.376 [INF] RunHandleWinLoss: 0
15:02:36.376 [INF] CalculateResult: 20220805
15:02:36.376 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
15:06:35.159 [INF] RunScan: 0
15:06:35.159 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:6)","Data":null,"DataObj":null}
15:07:36.377 [INF] RunHandleWinLoss: 0
15:07:36.377 [INF] CalculateResult: 20220805
15:07:36.377 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
15:11:35.159 [INF] RunScan: 0
15:11:35.160 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:11)","Data":null,"DataObj":null}
15:12:36.378 [INF] RunHandleWinLoss: 0
15:12:36.378 [INF] CalculateResult: 20220805
15:12:36.378 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
15:16:35.160 [INF] RunScan: 0
15:16:35.160 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:16)","Data":null,"DataObj":null}
15:17:36.380 [INF] RunHandleWinLoss: 0
15:17:36.380 [INF] CalculateResult: 20220805
15:17:36.380 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
15:21:35.160 [INF] RunScan: 0
15:21:35.160 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:21)","Data":null,"DataObj":null}
15:22:36.382 [INF] RunHandleWinLoss: 0
15:22:36.382 [INF] CalculateResult: 20220805
15:22:36.382 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
15:26:35.160 [INF] RunScan: 0
15:26:35.160 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:26)","Data":null,"DataObj":null}
15:27:36.383 [INF] RunHandleWinLoss: 0
15:27:36.383 [INF] CalculateResult: 20220805
15:27:36.383 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
15:31:35.160 [INF] RunScan: 0
15:31:35.161 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:31)","Data":null,"DataObj":null}
15:32:36.385 [INF] RunHandleWinLoss: 0
15:32:36.385 [INF] CalculateResult: 20220805
15:32:36.385 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
15:36:35.161 [INF] RunScan: 0
15:36:35.161 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:36)","Data":null,"DataObj":null}
15:37:36.388 [INF] RunHandleWinLoss: 0
15:37:36.388 [INF] CalculateResult: 20220805
15:37:36.388 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
15:41:35.161 [INF] RunScan: 0
15:41:35.161 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:41)","Data":null,"DataObj":null}
15:42:36.389 [INF] RunHandleWinLoss: 0
15:42:36.389 [INF] CalculateResult: 20220805
15:42:36.389 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
15:46:35.161 [INF] RunScan: 0
15:46:35.161 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:46)","Data":null,"DataObj":null}
15:47:36.391 [INF] RunHandleWinLoss: 0
15:47:36.391 [INF] CalculateResult: 20220805
15:47:36.391 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
15:51:35.161 [INF] RunScan: 0
15:51:35.162 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:51)","Data":null,"DataObj":null}
15:52:36.392 [INF] RunHandleWinLoss: 0
15:52:36.392 [INF] CalculateResult: 20220805
15:52:36.392 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
15:56:15.681 [DBG] Client connected from 103.160.84.45:46362
no ex
15:56:15.682 [DBG] Error while reading
System.IO.IOException: Unable to read data from the transport connection: Connection reset by peer.
 ---> System.Net.Sockets.SocketException (104): Connection reset by peer
   at System.Net.Sockets.Socket.EndReceive(IAsyncResult asyncResult)
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   --- End of inner exception stack trace ---
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
15:56:21.915 [DBG] Client connected from 103.160.84.45:49467
no ex
15:56:21.926 [DBG] 517 bytes read
no ex
15:56:35.162 [INF] RunScan: 0
15:56:35.162 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:56)","Data":null,"DataObj":null}
15:56:41.020 [DBG] 0 bytes read. Closing.
no ex
15:56:41.057 [DBG] Client connected from 103.160.84.45:34639
no ex
15:56:41.061 [DBG] 39 bytes read
no ex
15:57:01.165 [DBG] 0 bytes read. Closing.
no ex
15:57:01.201 [DBG] Client connected from 103.160.84.45:48887
no ex
15:57:21.309 [DBG] 1 bytes read
no ex
15:57:21.309 [DBG] 0 bytes read. Closing.
no ex
15:57:23.762 [DBG] Client connected from 103.160.84.45:50741
no ex
15:57:23.972 [DBG] Client connected from 103.160.84.45:35177
no ex
15:57:23.974 [DBG] Client connected from 103.160.84.45:55321
no ex
15:57:23.976 [DBG] 16 bytes read
no ex
15:57:23.984 [DBG] 8 bytes read
no ex
15:57:24.111 [DBG] Client connected from 103.160.84.45:51005
no ex
15:57:24.113 [DBG] 7 bytes read
no ex
15:57:29.000 [DBG] 0 bytes read. Closing.
no ex
15:57:29.001 [DBG] 0 bytes read. Closing.
no ex
15:57:29.132 [DBG] 7 bytes read
no ex
15:57:29.262 [DBG] Client connected from 103.160.84.45:39271
no ex
15:57:33.808 [DBG] 0 bytes read. Closing.
no ex
15:57:34.003 [DBG] Client connected from 103.160.84.45:51939
no ex
15:57:34.006 [DBG] 18 bytes read
no ex
15:57:34.156 [DBG] 0 bytes read. Closing.
no ex
15:57:34.308 [DBG] 0 bytes read. Closing.
no ex
15:57:36.394 [INF] RunHandleWinLoss: 0
15:57:36.394 [INF] CalculateResult: 20220805
15:57:36.394 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
15:57:39.040 [DBG] 0 bytes read. Closing.
no ex
15:57:39.249 [DBG] Client connected from 103.160.84.45:53401
no ex
15:57:39.250 [DBG] 6 bytes read
no ex
15:57:39.302 [DBG] Client connected from 103.160.84.45:49081
no ex
15:57:39.304 [DBG] 45 bytes read
no ex
15:57:44.270 [DBG] 0 bytes read. Closing.
no ex
15:57:44.329 [DBG] 0 bytes read. Closing.
no ex
15:57:44.361 [DBG] Client connected from 103.160.84.45:59709
no ex
15:57:44.366 [DBG] 11 bytes read
no ex
15:57:44.541 [DBG] Client connected from 103.160.84.45:44181
no ex
15:57:44.541 [DBG] 14 bytes read
no ex
15:57:44.679 [DBG] Client connected from 103.160.84.45:40299
no ex
15:57:44.680 [DBG] 42 bytes read
no ex
15:57:44.690 [DBG] Client connected from 103.160.84.45:39581
no ex
15:57:44.693 [DBG] 47 bytes read
no ex
15:57:49.394 [DBG] 0 bytes read. Closing.
no ex
15:57:49.566 [DBG] 0 bytes read. Closing.
no ex
15:57:49.692 [DBG] Client connected from 103.160.84.45:42325
no ex
15:57:49.693 [DBG] 28 bytes read
no ex
15:57:49.700 [DBG] 0 bytes read. Closing.
no ex
15:57:49.714 [DBG] 0 bytes read. Closing.
no ex
15:57:54.721 [DBG] 0 bytes read. Closing.
no ex
15:57:54.915 [DBG] Client connected from 103.160.84.45:55865
no ex
15:57:54.917 [DBG] 344 bytes read
no ex
15:57:59.936 [DBG] 0 bytes read. Closing.
no ex
15:58:00.098 [DBG] Client connected from 103.160.84.45:48885
no ex
15:58:00.099 [DBG] 5 bytes read
no ex
15:58:05.120 [DBG] 0 bytes read. Closing.
no ex
15:58:05.271 [DBG] Client connected from 103.160.84.45:39151
no ex
15:58:05.271 [DBG] 95 bytes read
no ex
15:58:10.292 [DBG] 0 bytes read. Closing.
no ex
15:58:18.385 [DBG] Client connected from 103.160.84.45:56997
no ex
15:58:18.441 [DBG] 938 bytes read
no ex
15:58:21.456 [DBG] 0 bytes read. Closing.
no ex
15:58:21.493 [DBG] Client connected from 103.160.84.45:43465
no ex
15:58:21.548 [DBG] 964 bytes read
no ex
15:58:24.568 [DBG] 0 bytes read. Closing.
no ex
15:58:24.604 [DBG] Client connected from 103.160.84.45:56613
no ex
15:58:24.658 [DBG] 938 bytes read
no ex
15:58:27.668 [DBG] 0 bytes read. Closing.
no ex
15:58:27.700 [DBG] Client connected from 103.160.84.45:45675
no ex
15:58:27.754 [DBG] 964 bytes read
no ex
15:58:30.767 [DBG] 0 bytes read. Closing.
no ex
15:58:30.801 [DBG] Client connected from 103.160.84.45:56493
no ex
15:58:30.856 [DBG] 976 bytes read
no ex
15:58:33.866 [DBG] 0 bytes read. Closing.
no ex
15:58:33.902 [DBG] Client connected from 103.160.84.45:55803
no ex
15:58:33.957 [DBG] 1002 bytes read
no ex
15:58:36.974 [DBG] 0 bytes read. Closing.
no ex
15:58:37.012 [DBG] Client connected from 103.160.84.45:60129
no ex
15:58:37.066 [DBG] 967 bytes read
no ex
15:58:40.076 [DBG] 0 bytes read. Closing.
no ex
15:58:40.113 [DBG] Client connected from 103.160.84.45:53773
no ex
15:58:40.164 [DBG] 69 bytes read
no ex
15:58:43.176 [DBG] 0 bytes read. Closing.
no ex
15:58:43.214 [DBG] Client connected from 103.160.84.45:50083
no ex
15:58:43.271 [DBG] 938 bytes read
no ex
15:58:46.284 [DBG] 0 bytes read. Closing.
no ex
15:59:16.491 [DBG] Client connected from 103.160.84.45:40421
no ex
15:59:16.510 [DBG] 111 bytes read
no ex
15:59:16.636 [DBG] Client connected from 103.160.84.45:38103
no ex
15:59:16.636 [DBG] 90 bytes read
no ex
15:59:21.550 [DBG] 0 bytes read. Closing.
no ex
15:59:21.653 [DBG] 0 bytes read. Closing.
no ex
15:59:21.936 [DBG] Client connected from 103.160.84.45:52439
no ex
15:59:21.938 [DBG] 60 bytes read
no ex
15:59:26.959 [DBG] 0 bytes read. Closing.
no ex
15:59:36.179 [DBG] Client connected from 103.160.84.45:48269
no ex
15:59:36.186 [DBG] 90 bytes read
no ex
15:59:41.207 [DBG] 0 bytes read. Closing.
no ex
16:01:35.162 [INF] RunScan: 0
16:01:35.162 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:1)","Data":null,"DataObj":null}
16:02:36.395 [INF] RunHandleWinLoss: 0
16:02:36.395 [INF] CalculateResult: 20220805
16:02:36.395 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
16:04:04.904 [DBG] Client connected from 103.160.84.45:55737
no ex
16:04:04.904 [DBG] 36 bytes read
no ex
16:04:14.943 [DBG] 0 bytes read. Closing.
no ex
16:04:16.769 [DBG] Client connected from 103.160.84.45:36221
no ex
16:04:20.892 [DBG] Client connected from 103.160.84.45:49879
no ex
16:04:20.892 [DBG] 268 bytes read
no ex
16:04:21.789 [DBG] 0 bytes read. Closing.
no ex
16:04:25.911 [DBG] 0 bytes read. Closing.
no ex
16:04:39.229 [DBG] Client connected from 103.160.84.45:60101
no ex
16:04:39.230 [DBG] 252 bytes read
no ex
16:04:43.036 [DBG] Client connected from 103.160.84.45:56675
no ex
16:04:43.041 [DBG] 8 bytes read
no ex
16:04:44.248 [DBG] 0 bytes read. Closing.
no ex
16:04:45.766 [DBG] Client connected from 103.160.84.45:43277
no ex
16:04:45.768 [DBG] 10 bytes read
no ex
16:04:48.069 [DBG] 0 bytes read. Closing.
no ex
16:04:55.813 [DBG] 0 bytes read. Closing.
no ex
16:04:55.849 [DBG] Client connected from 103.160.84.45:35417
no ex
16:04:55.851 [DBG] 12 bytes read
no ex
16:05:05.893 [DBG] 0 bytes read. Closing.
no ex
16:05:06.073 [DBG] Client connected from 103.160.84.45:59425
no ex
16:05:06.074 [DBG] 11 bytes read
no ex
16:05:07.076 [DBG] 11 bytes read
no ex
16:05:08.081 [DBG] 11 bytes read
no ex
16:05:09.085 [DBG] 0 bytes read. Closing.
no ex
16:06:35.162 [INF] RunScan: 0
16:06:35.162 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:6)","Data":null,"DataObj":null}
16:07:36.399 [INF] RunHandleWinLoss: 0
16:07:36.399 [INF] CalculateResult: 20220805
16:07:36.399 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
16:09:54.364 [DBG] Client connected from 103.160.84.45:52487
no ex
16:09:54.369 [DBG] 97 bytes read
no ex
16:09:55.258 [DBG] Client connected from 103.160.84.45:51285
no ex
16:09:55.262 [DBG] 4 bytes read
no ex
16:09:57.726 [DBG] Client connected from 103.160.84.45:39679
no ex
16:09:57.728 [DBG] 20 bytes read
no ex
16:09:59.393 [DBG] 0 bytes read. Closing.
no ex
16:10:00.282 [DBG] 0 bytes read. Closing.
no ex
16:10:02.755 [DBG] 32 bytes read
no ex
16:10:07.773 [DBG] 0 bytes read. Closing.
no ex
16:10:23.619 [DBG] Client connected from 103.160.84.45:53889
no ex
16:10:23.619 [DBG] 23 bytes read
no ex
16:10:23.639 [DBG] Client connected from 103.160.84.45:43523
no ex
16:10:23.640 [DBG] 288 bytes read
no ex
16:10:26.650 [DBG] 0 bytes read. Closing.
no ex
16:10:28.646 [DBG] 0 bytes read. Closing.
no ex
16:11:35.162 [INF] RunScan: 0
16:11:35.162 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:11)","Data":null,"DataObj":null}
16:12:36.401 [INF] RunHandleWinLoss: 0
16:12:36.401 [INF] CalculateResult: 20220805
16:12:36.401 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
16:13:53.764 [DBG] Client connected from 103.160.84.45:41387
no ex
16:13:53.766 [DBG] 81 bytes read
no ex
16:13:54.149 [DBG] Client connected from 103.160.84.45:36713
no ex
16:13:54.149 [DBG] 116 bytes read
no ex
16:13:55.949 [DBG] Client connected from 103.160.84.45:44113
no ex
16:13:55.951 [DBG] 13 bytes read
no ex
16:13:58.356 [DBG] Client connected from 103.160.84.45:1023
no ex
16:13:58.362 [DBG] 15 bytes read
no ex
16:13:58.790 [DBG] 0 bytes read. Closing.
no ex
16:13:59.051 [DBG] Client connected from 103.160.84.45:40517
no ex
16:13:59.055 [DBG] 120 bytes read
no ex
16:13:59.176 [DBG] 0 bytes read. Closing.
no ex
16:13:59.354 [DBG] Client connected from 103.160.84.45:39083
no ex
16:13:59.355 [DBG] 10 bytes read
no ex
16:14:00.971 [DBG] 0 bytes read. Closing.
no ex
16:14:01.008 [DBG] Client connected from 103.160.84.45:54661
no ex
16:14:01.009 [DBG] 5 bytes read
no ex
16:14:03.392 [DBG] 0 bytes read. Closing.
no ex
16:14:03.505 [DBG] Client connected from 103.160.84.45:49951
no ex
16:14:03.505 [DBG] 29 bytes read
no ex
16:14:04.074 [DBG] 0 bytes read. Closing.
no ex
16:14:04.106 [DBG] Client connected from 103.160.84.45:54423
no ex
16:14:04.106 [DBG] 120 bytes read
no ex
16:14:04.384 [DBG] 0 bytes read. Closing.
no ex
16:14:04.595 [DBG] Client connected from 103.160.84.45:53405
no ex
16:14:04.596 [DBG] 5 bytes read
no ex
16:14:06.035 [DBG] 0 bytes read. Closing.
no ex
16:14:06.321 [DBG] Client connected from 103.160.84.45:33687
no ex
16:14:06.323 [DBG] 1 bytes read
no ex
16:14:08.524 [DBG] 0 bytes read. Closing.
no ex
16:14:08.796 [DBG] Client connected from 103.160.84.45:43721
no ex
16:14:08.798 [DBG] 6 bytes read
no ex
16:14:09.142 [DBG] 0 bytes read. Closing.
no ex
16:14:09.374 [DBG] Client connected from 103.160.84.45:52405
no ex
16:14:09.376 [DBG] 24 bytes read
no ex
16:14:09.635 [DBG] 0 bytes read. Closing.
no ex
16:14:10.569 [DBG] Client connected from 103.160.84.45:55989
no ex
16:14:10.569 [DBG] 4 bytes read
no ex
16:14:11.341 [DBG] 0 bytes read. Closing.
no ex
16:14:11.511 [DBG] Client connected from 103.160.84.45:40189
no ex
16:14:11.511 [DBG] 512 bytes read
no ex
16:14:11.545 [DBG] 71 bytes read
no ex
16:14:13.826 [DBG] 0 bytes read. Closing.
no ex
16:14:13.978 [DBG] Client connected from 103.160.84.45:43797
no ex
16:14:13.979 [DBG] 266 bytes read
no ex
16:14:14.401 [DBG] 0 bytes read. Closing.
no ex
16:14:15.075 [DBG] Client connected from 103.160.84.45:46893
no ex
16:14:15.078 [DBG] 4 bytes read
no ex
16:14:15.590 [DBG] 0 bytes read. Closing.
no ex
16:14:16.532 [DBG] 0 bytes read. Closing.
no ex
16:14:16.691 [DBG] Client connected from 103.160.84.45:60313
no ex
16:14:16.693 [DBG] 7 bytes read
no ex
16:14:18.730 [DBG] Client connected from 103.160.84.45:35859
no ex
16:14:18.731 [DBG] 5 bytes read
no ex
16:14:19.005 [DBG] 0 bytes read. Closing.
no ex
16:14:19.282 [DBG] Client connected from 103.160.84.45:60123
no ex
16:14:19.284 [DBG] 16 bytes read
no ex
16:14:20.098 [DBG] 0 bytes read. Closing.
no ex
16:14:20.248 [DBG] Client connected from 103.160.84.45:44471
no ex
16:14:20.249 [DBG] 60 bytes read
no ex
16:14:21.713 [DBG] 0 bytes read. Closing.
no ex
16:14:21.897 [DBG] Client connected from 103.160.84.45:45057
no ex
16:14:23.748 [DBG] 0 bytes read. Closing.
no ex
16:14:23.868 [DBG] Client connected from 103.160.84.45:38337
no ex
16:14:23.869 [DBG] 23 bytes read
no ex
16:14:25.271 [DBG] 0 bytes read. Closing.
no ex
16:14:25.473 [DBG] Client connected from 103.160.84.45:42005
no ex
16:14:25.473 [DBG] 389 bytes read
no ex
16:14:26.927 [DBG] 0 bytes read. Closing.
no ex
16:14:27.210 [DBG] Client connected from 103.160.84.45:46179
no ex
16:14:27.210 [DBG] 1010 bytes read
no ex
16:14:28.886 [DBG] 0 bytes read. Closing.
no ex
16:14:29.125 [DBG] Client connected from 103.160.84.45:51819
no ex
16:14:29.127 [DBG] 123 bytes read
no ex
16:14:29.328 [DBG] 0 bytes read. Closing.
no ex
16:14:29.488 [DBG] Client connected from 103.160.84.45:33443
no ex
16:14:29.491 [DBG] 5 bytes read
no ex
16:14:30.495 [DBG] 389 bytes read
no ex
16:14:32.237 [DBG] 0 bytes read. Closing.
no ex
16:14:32.370 [DBG] Client connected from 103.160.84.45:44219
no ex
16:14:32.370 [DBG] 28 bytes read
no ex
16:14:34.154 [DBG] 0 bytes read. Closing.
no ex
16:14:34.294 [DBG] Client connected from 103.160.84.45:46797
no ex
16:14:34.294 [DBG] 30 bytes read
no ex
16:14:34.514 [DBG] 0 bytes read. Closing.
no ex
16:14:35.033 [DBG] Client connected from 103.160.84.45:53765
no ex
16:14:35.034 [DBG] 148 bytes read
no ex
16:14:35.069 [DBG] 39 bytes read
no ex
16:14:35.519 [DBG] 389 bytes read
no ex
16:14:37.397 [DBG] 0 bytes read. Closing.
no ex
16:14:37.670 [DBG] Client connected from 103.160.84.45:49365
no ex
16:14:37.672 [DBG] 3 bytes read
no ex
16:14:39.315 [DBG] 0 bytes read. Closing.
no ex
16:14:40.058 [DBG] 0 bytes read. Closing.
no ex
16:14:40.543 [DBG] 0 bytes read. Closing.
no ex
16:14:40.877 [DBG] Client connected from 103.160.84.45:37395
no ex
16:14:40.880 [DBG] 0 bytes read. Closing.
no ex
16:14:42.693 [DBG] 0 bytes read. Closing.
no ex
16:14:45.055 [DBG] Client connected from 103.160.84.45:46274
no ex
16:14:51.087 [DBG] 4 bytes read
no ex
16:14:56.110 [DBG] 0 bytes read. Closing.
no ex
16:14:56.149 [DBG] Client connected from 103.160.84.45:46278
no ex
16:14:56.150 [DBG] 18 bytes read
no ex
16:15:01.174 [DBG] 0 bytes read. Closing.
no ex
16:15:01.205 [DBG] Client connected from 103.160.84.45:48938
no ex
16:15:01.206 [DBG] 22 bytes read
no ex
16:15:06.231 [DBG] 0 bytes read. Closing.
no ex
16:15:06.269 [DBG] Client connected from 103.160.84.45:53560
no ex
16:15:06.269 [DBG] 22 bytes read
no ex
16:15:11.293 [DBG] 0 bytes read. Closing.
no ex
16:15:11.329 [DBG] Client connected from 103.160.84.45:58134
no ex
16:15:11.329 [DBG] 44 bytes read
no ex
16:15:16.354 [DBG] 0 bytes read. Closing.
no ex
16:15:16.391 [DBG] Client connected from 103.160.84.45:58142
no ex
16:15:16.392 [DBG] 32 bytes read
no ex
16:15:21.417 [DBG] 0 bytes read. Closing.
no ex
16:15:21.454 [DBG] Client connected from 103.160.84.45:58144
no ex
16:15:21.455 [DBG] 14 bytes read
no ex
16:15:26.477 [DBG] 0 bytes read. Closing.
no ex
16:15:26.508 [DBG] Client connected from 103.160.84.45:58146
no ex
16:15:26.510 [DBG] 6 bytes read
no ex
16:15:34.046 [DBG] 0 bytes read. Closing.
no ex
16:15:34.085 [DBG] Client connected from 103.160.84.45:58148
no ex
16:15:34.086 [DBG] 88 bytes read
no ex
16:15:39.109 [DBG] 0 bytes read. Closing.
no ex
16:15:39.146 [DBG] Client connected from 103.160.84.45:58150
no ex
16:15:39.147 [DBG] 42 bytes read
no ex
16:15:44.168 [DBG] 0 bytes read. Closing.
no ex
16:15:44.194 [DBG] Client connected from 103.160.84.45:58152
no ex
16:15:44.194 [DBG] 110 bytes read
no ex
16:15:49.215 [DBG] 0 bytes read. Closing.
no ex
16:15:49.251 [DBG] Client connected from 103.160.84.45:58154
no ex
16:15:49.254 [DBG] 117 bytes read
no ex
16:15:54.278 [DBG] 0 bytes read. Closing.
no ex
16:15:54.307 [DBG] Client connected from 103.160.84.45:58156
no ex
16:15:54.308 [DBG] 168 bytes read
no ex
16:15:59.331 [DBG] 0 bytes read. Closing.
no ex
16:15:59.368 [DBG] Client connected from 103.160.84.45:58158
no ex
16:15:59.370 [DBG] 12 bytes read
no ex
16:16:04.396 [DBG] 0 bytes read. Closing.
no ex
16:16:04.424 [DBG] Client connected from 103.160.84.45:58160
no ex
16:16:04.426 [DBG] 53 bytes read
no ex
16:16:09.448 [DBG] 0 bytes read. Closing.
no ex
16:16:09.485 [DBG] Client connected from 103.160.84.45:58162
no ex
16:16:09.485 [DBG] 9 bytes read
no ex
16:16:09.956 [DBG] Client connected from 103.160.84.45:57173
no ex
16:16:09.956 [DBG] 12 bytes read
no ex
16:16:14.506 [DBG] 0 bytes read. Closing.
no ex
16:16:14.540 [DBG] Client connected from 103.160.84.45:58170
no ex
16:16:14.546 [DBG] 51 bytes read
no ex
16:16:19.570 [DBG] 0 bytes read. Closing.
no ex
16:16:19.603 [DBG] Client connected from 103.160.84.45:58172
no ex
16:16:19.604 [DBG] 14 bytes read
no ex
16:16:20.000 [DBG] 0 bytes read. Closing.
no ex
16:16:20.036 [DBG] Client connected from 103.160.84.45:57365
no ex
16:16:20.038 [DBG] 12 bytes read
no ex
16:16:24.629 [DBG] 0 bytes read. Closing.
no ex
16:16:24.658 [DBG] Client connected from 103.160.84.45:58174
no ex
16:16:24.659 [DBG] 223 bytes read
no ex
16:16:30.087 [DBG] 0 bytes read. Closing.
no ex
16:16:30.118 [DBG] Client connected from 103.160.84.45:49751
no ex
16:16:30.119 [DBG] 12 bytes read
no ex
16:16:32.195 [DBG] 0 bytes read. Closing.
no ex
16:16:32.235 [DBG] Client connected from 103.160.84.45:58176
no ex
16:16:32.239 [DBG] 16 bytes read
no ex
16:16:35.162 [INF] RunScan: 0
16:16:35.163 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:16)","Data":null,"DataObj":null}
16:16:37.264 [DBG] 0 bytes read. Closing.
no ex
16:16:37.296 [DBG] Client connected from 103.160.84.45:58180
no ex
16:16:37.298 [DBG] 11 bytes read
no ex
16:16:40.160 [DBG] 0 bytes read. Closing.
no ex
16:16:40.194 [DBG] Client connected from 103.160.84.45:50729
no ex
16:16:40.195 [DBG] 12 bytes read
no ex
16:16:42.321 [DBG] 0 bytes read. Closing.
no ex
16:16:42.360 [DBG] Client connected from 103.160.84.45:58182
no ex
16:16:42.360 [DBG] 23 bytes read
no ex
16:16:47.384 [DBG] 0 bytes read. Closing.
no ex
16:16:47.416 [DBG] Client connected from 103.160.84.45:58184
no ex
16:16:47.417 [DBG] 60 bytes read
no ex
16:16:50.242 [DBG] 0 bytes read. Closing.
no ex
16:16:52.441 [DBG] 0 bytes read. Closing.
no ex
16:16:52.476 [DBG] Client connected from 103.160.84.45:58186
no ex
16:16:52.477 [DBG] 7 bytes read
no ex
16:16:57.498 [DBG] 0 bytes read. Closing.
no ex
16:16:57.536 [DBG] Client connected from 103.160.84.45:58188
no ex
16:16:57.536 [DBG] 175 bytes read
no ex
16:17:02.560 [DBG] 0 bytes read. Closing.
no ex
16:17:02.586 [DBG] Client connected from 103.160.84.45:58190
no ex
16:17:02.586 [DBG] 90 bytes read
no ex
16:17:03.407 [DBG] Client connected from 103.160.84.45:44311
no ex
16:17:03.411 [DBG] 1 bytes read
no ex
16:17:07.611 [DBG] 0 bytes read. Closing.
no ex
16:17:07.647 [DBG] Client connected from 103.160.84.45:58192
no ex
16:17:07.648 [DBG] 52 bytes read
no ex
16:17:08.441 [DBG] 0 bytes read. Closing.
no ex
16:17:12.670 [DBG] 0 bytes read. Closing.
no ex
16:17:12.709 [DBG] Client connected from 103.160.84.45:58194
no ex
16:17:12.709 [DBG] 18 bytes read
no ex
16:17:16.089 [DBG] Client connected from 103.160.84.45:45871
no ex
16:17:16.089 [DBG] 47 bytes read
no ex
16:17:17.736 [DBG] 0 bytes read. Closing.
no ex
16:17:17.763 [DBG] Client connected from 103.160.84.45:58200
no ex
16:17:17.763 [DBG] 48 bytes read
no ex
16:17:21.114 [DBG] 0 bytes read. Closing.
no ex
16:17:22.788 [DBG] 0 bytes read. Closing.
no ex
16:17:22.878 [DBG] Client connected from 103.160.84.45:673
no ex
16:17:22.878 [DBG] 44 bytes read
no ex
16:17:23.879 [DBG] 0 bytes read. Closing.
no ex
16:17:36.402 [INF] RunHandleWinLoss: 0
16:17:36.402 [INF] CalculateResult: 20220805
16:17:36.402 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
16:21:35.163 [INF] RunScan: 0
16:21:35.163 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:21)","Data":null,"DataObj":null}
16:22:36.404 [INF] RunHandleWinLoss: 0
16:22:36.404 [INF] CalculateResult: 20220805
16:22:36.404 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
16:24:00.834 [DBG] Client connected from 103.160.84.45:46675
no ex
16:24:00.835 [DBG] 1041 bytes read
no ex
16:24:05.855 [DBG] 0 bytes read. Closing.
no ex
16:26:35.163 [INF] RunScan: 0
16:26:35.163 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:26)","Data":null,"DataObj":null}
16:27:36.405 [INF] RunHandleWinLoss: 0
16:27:36.405 [INF] CalculateResult: 20220805
16:27:36.405 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
16:31:05.816 [DBG] Client connected from 103.160.84.45:59379
no ex
16:31:05.855 [DBG] 1 bytes read
no ex
16:31:06.011 [DBG] 1 bytes read
no ex
16:31:06.088 [DBG] 0 bytes read. Closing.
no ex
16:31:11.880 [DBG] Client connected from 103.160.84.45:56507
no ex
16:31:11.880 [DBG] 77 bytes read
no ex
16:31:21.921 [DBG] 0 bytes read. Closing.
no ex
16:31:22.129 [DBG] Client connected from 103.160.84.45:57549
no ex
16:31:22.131 [DBG] 77 bytes read
no ex
16:31:29.833 [DBG] Client connected from 103.160.84.45:52981
no ex
16:31:32.174 [DBG] 0 bytes read. Closing.
no ex
16:31:32.501 [DBG] Client connected from 103.160.84.45:50825
no ex
16:31:32.502 [DBG] 217 bytes read
no ex
16:31:34.852 [DBG] 7 bytes read
no ex
16:31:35.163 [INF] RunScan: 0
16:31:35.163 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:31)","Data":null,"DataObj":null}
16:31:37.522 [DBG] 0 bytes read. Closing.
no ex
16:31:39.871 [DBG] 0 bytes read. Closing.
no ex
16:32:36.406 [INF] RunHandleWinLoss: 0
16:32:36.406 [INF] CalculateResult: 20220805
16:32:36.407 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
16:34:07.207 [DBG] Client connected from 103.160.84.45:50839
no ex
16:34:07.269 [DBG] 38 bytes read
no ex
16:34:17.052 [DBG] 0 bytes read. Closing.
no ex
16:34:17.087 [DBG] Client connected from 103.160.84.45:53247
no ex
16:34:17.145 [DBG] 50 bytes read
no ex
16:34:27.119 [DBG] 0 bytes read. Closing.
no ex
16:34:27.153 [DBG] Client connected from 103.160.84.45:46193
no ex
16:34:27.209 [DBG] 48 bytes read
no ex
16:34:37.079 [DBG] 0 bytes read. Closing.
no ex
16:34:37.119 [DBG] Client connected from 103.160.84.45:54779
no ex
16:34:37.179 [DBG] 60 bytes read
no ex
16:34:47.100 [DBG] 0 bytes read. Closing.
no ex
16:34:47.135 [DBG] Client connected from 103.160.84.45:52637
no ex
16:34:47.208 [DBG] 48 bytes read
no ex
16:34:57.127 [DBG] 0 bytes read. Closing.
no ex
16:34:57.161 [DBG] Client connected from 103.160.84.45:49631
no ex
16:34:57.227 [DBG] 60 bytes read
no ex
16:35:07.047 [DBG] 0 bytes read. Closing.
no ex
16:36:35.163 [INF] RunScan: 0
16:36:35.164 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:36)","Data":null,"DataObj":null}
16:37:36.408 [INF] RunHandleWinLoss: 0
16:37:36.408 [INF] CalculateResult: 20220805
16:37:36.408 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
16:41:35.164 [INF] RunScan: 0
16:41:35.164 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:41)","Data":null,"DataObj":null}
16:42:36.410 [INF] RunHandleWinLoss: 0
16:42:36.410 [INF] CalculateResult: 20220805
16:42:36.410 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
16:43:03.687 [DBG] Client connected from 103.160.84.45:55591
no ex
16:43:03.691 [DBG] 0 bytes read. Closing.
no ex
16:46:35.164 [INF] RunScan: 0
16:46:35.164 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:46)","Data":null,"DataObj":null}
16:47:36.411 [INF] RunHandleWinLoss: 0
16:47:36.411 [INF] CalculateResult: 20220805
16:47:36.411 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
16:51:35.164 [INF] RunScan: 0
16:51:35.164 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:51)","Data":null,"DataObj":null}
16:52:36.414 [INF] RunHandleWinLoss: 0
16:52:36.414 [INF] CalculateResult: 20220805
16:52:36.414 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
16:56:35.164 [INF] RunScan: 0
16:56:35.164 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:56)","Data":null,"DataObj":null}
16:57:36.416 [INF] RunHandleWinLoss: 0
16:57:36.416 [INF] CalculateResult: 20220805
16:57:36.416 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
17:01:35.164 [INF] RunScan: 0
17:01:35.165 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:1)","Data":null,"DataObj":null}
17:02:36.418 [INF] RunHandleWinLoss: 0
17:02:36.418 [INF] CalculateResult: 20220805
17:02:36.418 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
17:03:37.234 [DBG] Client connected from 127.0.0.1:41254
no ex
17:03:37.235 [DBG] 1166 bytes read
no ex
17:03:37.235 [DBG] Building Hybi-14 Response
no ex
17:03:37.235 [DBG] Sent 129 bytes
no ex
17:03:37.620 [DBG] 31 bytes read
no ex
17:03:37.620 [DBG] Sent 30 bytes
no ex
17:03:37.620 [DBG] 114 bytes read
no ex
17:03:37.621 [INF] GET: http://127.0.0.1:8081/api?c=3&un=fsfdsffffff&pw=96e79218965eb72c92a549dd5a330112&pf=web&at=
17:03:37.633 [INF] GET response: {"success":true,"message":null,"errorCode":"0","data":null,"sessionKey":"eyJuaWNrbmFtZSI6ImtqbHVoa2prIiwiYXZhdGFyIjoiMCIsInZpblRvdGFsIjowLCJ4dVRvdGFsIjo1MDAwMDAsInZpcHBvaW50IjowLCJ2aXBwb2ludFNhdmUiOjAsImNyZWF0ZVRpbWUiOiIwNS0wOC0yMDIyIiwiaXBBZGRyZXNzIjoiMTI3LjAuMC4xIiwiY2VydGlmaWNhdGUiOmZhbHNlLCJsdWNreVJvdGF0ZSI6MCwiZGFpTHkiOjAsIm1vYmlsZVNlY3VyZSI6MCwiYmlydGhkYXkiOiIiLCJhcHBTZWN1cmUiOjAsInVzZXJuYW1lIjoiZnNmZHNmZmZmZmYiLCJlbWFpbCI6MCwiYWRkcmVzcyI6bnVsbCwidmVyaWZ5TW9iaWxlIjpmYWxzZX0=","accessToken":"7c7279e8d6d711434740a13ea98a08dc"}

17:03:37.642 [DBG] Sent 5725 bytes
no ex
17:03:40.626 [DBG] 31 bytes read
no ex
17:03:40.626 [DBG] Sent 30 bytes
no ex
17:03:41.016 [DBG] Sent 184 bytes
no ex
17:03:43.628 [DBG] 31 bytes read
no ex
17:03:43.628 [DBG] Sent 30 bytes
no ex
17:03:44.599 [DBG] 31 bytes read
no ex
17:03:44.599 [DBG] Sent 30 bytes
no ex
17:03:44.980 [DBG] 31 bytes read
no ex
17:03:44.981 [DBG] Sent 30 bytes
no ex
17:03:45.352 [DBG] 31 bytes read
no ex
17:03:45.353 [DBG] Sent 30 bytes
no ex
17:03:45.759 [DBG] 124 bytes read
no ex
17:03:45.777 [DBG] Sent 35 bytes
no ex
17:03:46.017 [DBG] Sent 184 bytes
no ex
17:03:46.631 [DBG] 31 bytes read
no ex
17:03:46.632 [DBG] Sent 30 bytes
no ex
17:03:48.829 [DBG] 31 bytes read
no ex
17:03:48.829 [DBG] Sent 30 bytes
no ex
17:03:49.225 [DBG] 31 bytes read
no ex
17:03:49.226 [DBG] Sent 30 bytes
no ex
17:03:49.611 [DBG] 31 bytes read
no ex
17:03:49.611 [DBG] Sent 30 bytes
no ex
17:03:49.622 [DBG] 31 bytes read
no ex
17:03:49.622 [DBG] Sent 30 bytes
no ex
17:03:50.011 [DBG] 124 bytes read
no ex
17:03:50.015 [DBG] Sent 35 bytes
no ex
17:03:51.005 [DBG] Sent 184 bytes
no ex
17:03:52.637 [DBG] 31 bytes read
no ex
17:03:52.637 [DBG] Sent 30 bytes
no ex
17:03:52.929 [DBG] 31 bytes read
no ex
17:03:52.930 [DBG] Sent 30 bytes
no ex
17:03:53.526 [DBG] 31 bytes read
no ex
17:03:53.526 [DBG] Sent 30 bytes
no ex
17:03:53.897 [DBG] 31 bytes read
no ex
17:03:53.897 [DBG] Sent 30 bytes
no ex
17:03:54.272 [DBG] 124 bytes read
no ex
17:03:54.286 [DBG] Sent 35 bytes
no ex
17:03:55.626 [DBG] 31 bytes read
no ex
17:03:55.626 [DBG] Sent 30 bytes
no ex
17:03:56.010 [DBG] Sent 184 bytes
no ex
17:03:56.673 [DBG] 31 bytes read
no ex
17:03:56.673 [DBG] Sent 30 bytes
no ex
17:03:57.054 [DBG] 31 bytes read
no ex
17:03:57.055 [DBG] Sent 30 bytes
no ex
17:03:57.451 [DBG] 31 bytes read
no ex
17:03:57.451 [DBG] Sent 30 bytes
no ex
17:03:57.833 [DBG] 124 bytes read
no ex
17:03:57.838 [DBG] Sent 35 bytes
no ex
17:03:58.631 [DBG] 31 bytes read
no ex
17:03:58.631 [DBG] Sent 30 bytes
no ex
17:04:01.010 [DBG] Sent 184 bytes
no ex
17:04:01.634 [DBG] 31 bytes read
no ex
17:04:01.635 [DBG] Sent 30 bytes
no ex
17:04:04.623 [DBG] 31 bytes read
no ex
17:04:04.623 [DBG] Sent 30 bytes
no ex
17:04:04.801 [DBG] 31 bytes read
no ex
17:04:04.802 [DBG] Sent 30 bytes
no ex
17:04:05.181 [DBG] 31 bytes read
no ex
17:04:05.181 [DBG] Sent 30 bytes
no ex
17:04:05.568 [DBG] 31 bytes read
no ex
17:04:05.569 [DBG] Sent 30 bytes
no ex
17:04:05.948 [DBG] 124 bytes read
no ex
17:04:05.964 [DBG] Sent 35 bytes
no ex
17:04:06.016 [DBG] Sent 184 bytes
no ex
17:04:07.630 [DBG] 31 bytes read
no ex
17:04:07.630 [DBG] Sent 30 bytes
no ex
17:04:10.624 [DBG] 31 bytes read
no ex
17:04:10.624 [DBG] Sent 30 bytes
no ex
17:04:10.939 [DBG] 31 bytes read
no ex
17:04:10.939 [DBG] Sent 30 bytes
no ex
17:04:11.023 [DBG] Sent 184 bytes
no ex
17:04:11.326 [DBG] 31 bytes read
no ex
17:04:11.326 [DBG] Sent 30 bytes
no ex
17:04:11.719 [DBG] 31 bytes read
no ex
17:04:11.719 [DBG] Sent 30 bytes
no ex
17:04:12.093 [DBG] 124 bytes read
no ex
17:04:12.101 [DBG] Sent 35 bytes
no ex
17:04:13.624 [DBG] 31 bytes read
no ex
17:04:13.624 [DBG] Sent 30 bytes
no ex
17:04:16.013 [DBG] Sent 184 bytes
no ex
17:04:16.625 [DBG] 31 bytes read
no ex
17:04:16.625 [DBG] Sent 30 bytes
no ex
17:04:18.325 [DBG] 31 bytes read
no ex
17:04:18.325 [DBG] Sent 30 bytes
no ex
17:04:18.706 [DBG] 31 bytes read
no ex
17:04:18.706 [DBG] Sent 30 bytes
no ex
17:04:19.097 [DBG] 31 bytes read
no ex
17:04:19.098 [DBG] Sent 30 bytes
no ex
17:04:19.482 [DBG] 124 bytes read
no ex
17:04:19.498 [DBG] Sent 35 bytes
no ex
17:04:19.624 [DBG] 31 bytes read
no ex
17:04:19.624 [DBG] Sent 30 bytes
no ex
17:04:21.018 [DBG] Sent 184 bytes
no ex
17:04:22.081 [DBG] 31 bytes read
no ex
17:04:22.082 [DBG] Sent 30 bytes
no ex
17:04:22.472 [DBG] 31 bytes read
no ex
17:04:22.473 [DBG] Sent 30 bytes
no ex
17:04:22.622 [DBG] 31 bytes read
no ex
17:04:22.622 [DBG] Sent 30 bytes
no ex
17:04:22.855 [DBG] 31 bytes read
no ex
17:04:22.855 [DBG] Sent 30 bytes
no ex
17:04:23.228 [DBG] 124 bytes read
no ex
17:04:23.241 [DBG] Sent 35 bytes
no ex
17:04:25.626 [DBG] 31 bytes read
no ex
17:04:25.626 [DBG] Sent 30 bytes
no ex
17:04:26.012 [DBG] Sent 184 bytes
no ex
17:04:26.012 [DBG] Sent 73 bytes
no ex
17:04:28.637 [DBG] 31 bytes read
no ex
17:04:28.637 [DBG] Sent 30 bytes
no ex
17:04:31.016 [DBG] Sent 184 bytes
no ex
17:04:31.642 [DBG] 31 bytes read
no ex
17:04:31.643 [DBG] Sent 30 bytes
no ex
17:04:34.653 [DBG] 31 bytes read
no ex
17:04:34.653 [DBG] Sent 30 bytes
no ex
17:04:36.022 [DBG] Sent 184 bytes
no ex
17:04:37.627 [DBG] 31 bytes read
no ex
17:04:37.628 [DBG] Sent 30 bytes
no ex
17:04:40.642 [DBG] 31 bytes read
no ex
17:04:40.644 [DBG] Sent 30 bytes
no ex
17:04:41.018 [DBG] Sent 184 bytes
no ex
17:04:43.651 [DBG] 31 bytes read
no ex
17:04:43.651 [DBG] Sent 30 bytes
no ex
17:04:46.025 [DBG] Sent 184 bytes
no ex
17:04:46.631 [DBG] 31 bytes read
no ex
17:04:46.631 [DBG] Sent 30 bytes
no ex
17:04:49.630 [DBG] 31 bytes read
no ex
17:04:49.630 [DBG] Sent 30 bytes
no ex
17:04:51.021 [DBG] Sent 184 bytes
no ex
17:04:52.622 [DBG] 31 bytes read
no ex
17:04:52.622 [DBG] Sent 30 bytes
no ex
17:04:55.628 [DBG] 31 bytes read
no ex
17:04:55.628 [DBG] Sent 30 bytes
no ex
17:04:56.028 [DBG] Sent 184 bytes
no ex
17:04:58.634 [DBG] 31 bytes read
no ex
17:04:58.634 [DBG] Sent 30 bytes
no ex
17:05:01.019 [DBG] Sent 184 bytes
no ex
17:05:01.637 [DBG] 31 bytes read
no ex
17:05:01.637 [DBG] Sent 30 bytes
no ex
17:05:04.638 [DBG] 31 bytes read
no ex
17:05:04.638 [DBG] Sent 30 bytes
no ex
17:05:06.031 [DBG] Sent 184 bytes
no ex
17:05:07.626 [DBG] 31 bytes read
no ex
17:05:07.626 [DBG] Sent 30 bytes
no ex
17:05:10.629 [DBG] 31 bytes read
no ex
17:05:10.629 [DBG] Sent 30 bytes
no ex
17:05:11.033 [DBG] Sent 184 bytes
no ex
17:05:13.633 [DBG] 31 bytes read
no ex
17:05:13.634 [DBG] Sent 30 bytes
no ex
17:05:16.034 [DBG] Sent 184 bytes
no ex
17:05:16.625 [DBG] 31 bytes read
no ex
17:05:16.625 [DBG] Sent 30 bytes
no ex
17:05:19.629 [DBG] 31 bytes read
no ex
17:05:19.629 [DBG] Sent 30 bytes
no ex
17:05:21.019 [DBG] Sent 184 bytes
no ex
17:05:22.627 [DBG] 31 bytes read
no ex
17:05:22.627 [DBG] Sent 30 bytes
no ex
17:05:25.628 [DBG] 31 bytes read
no ex
17:05:25.629 [DBG] Sent 30 bytes
no ex
17:05:26.027 [DBG] Sent 184 bytes
no ex
17:05:28.627 [DBG] 31 bytes read
no ex
17:05:28.627 [DBG] Sent 30 bytes
no ex
17:05:31.029 [DBG] Sent 184 bytes
no ex
17:05:31.633 [DBG] 31 bytes read
no ex
17:05:31.633 [DBG] Sent 30 bytes
no ex
17:05:34.636 [DBG] 31 bytes read
no ex
17:05:34.636 [DBG] Sent 30 bytes
no ex
17:05:36.037 [DBG] Sent 184 bytes
no ex
17:05:37.635 [DBG] 31 bytes read
no ex
17:05:37.635 [DBG] Sent 30 bytes
no ex
17:05:40.626 [DBG] 31 bytes read
no ex
17:05:40.626 [DBG] Sent 30 bytes
no ex
17:05:41.024 [DBG] Sent 184 bytes
no ex
17:05:43.625 [DBG] 31 bytes read
no ex
17:05:43.625 [DBG] Sent 30 bytes
no ex
17:05:46.023 [DBG] Sent 184 bytes
no ex
17:05:46.625 [DBG] 31 bytes read
no ex
17:05:46.625 [DBG] Sent 30 bytes
no ex
17:05:49.625 [DBG] 31 bytes read
no ex
17:05:49.625 [DBG] Sent 30 bytes
no ex
17:05:51.025 [DBG] Sent 184 bytes
no ex
17:05:52.648 [DBG] 31 bytes read
no ex
17:05:52.649 [DBG] Sent 30 bytes
no ex
17:05:55.645 [DBG] 31 bytes read
no ex
17:05:55.646 [DBG] Sent 30 bytes
no ex
17:05:56.029 [DBG] Sent 184 bytes
no ex
17:05:59.168 [DBG] 31 bytes read
no ex
17:05:59.168 [DBG] Sent 30 bytes
no ex
17:06:01.029 [DBG] Sent 184 bytes
no ex
17:06:02.162 [DBG] 31 bytes read
no ex
17:06:02.162 [DBG] Sent 30 bytes
no ex
17:06:05.163 [DBG] 31 bytes read
no ex
17:06:05.163 [DBG] Sent 30 bytes
no ex
17:06:06.037 [DBG] Sent 184 bytes
no ex
17:06:08.167 [DBG] 31 bytes read
no ex
17:06:08.167 [DBG] Sent 30 bytes
no ex
17:06:11.041 [DBG] Sent 184 bytes
no ex
17:06:11.165 [DBG] 31 bytes read
no ex
17:06:11.165 [DBG] Sent 30 bytes
no ex
17:06:14.165 [DBG] 31 bytes read
no ex
17:06:14.165 [DBG] Sent 30 bytes
no ex
17:06:16.028 [DBG] Sent 184 bytes
no ex
17:06:17.164 [DBG] 31 bytes read
no ex
17:06:17.164 [DBG] Sent 30 bytes
no ex
17:06:20.164 [DBG] 31 bytes read
no ex
17:06:20.164 [DBG] Sent 30 bytes
no ex
17:06:21.035 [DBG] Sent 184 bytes
no ex
17:06:23.164 [DBG] 31 bytes read
no ex
17:06:23.164 [DBG] Sent 30 bytes
no ex
17:06:26.029 [DBG] Sent 184 bytes
no ex
17:06:26.169 [DBG] 31 bytes read
no ex
17:06:26.170 [DBG] Sent 30 bytes
no ex
17:06:29.166 [DBG] 31 bytes read
no ex
17:06:29.166 [DBG] Sent 30 bytes
no ex
17:06:31.039 [DBG] Sent 184 bytes
no ex
17:06:32.164 [DBG] 31 bytes read
no ex
17:06:32.164 [DBG] Sent 30 bytes
no ex
17:06:35.165 [INF] RunScan: 0
17:06:35.165 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:6)","Data":null,"DataObj":null}
17:06:35.172 [DBG] 31 bytes read
no ex
17:06:35.173 [DBG] Sent 30 bytes
no ex
17:06:36.043 [DBG] Sent 184 bytes
no ex
17:06:38.178 [DBG] 31 bytes read
no ex
17:06:38.178 [DBG] Sent 30 bytes
no ex
17:06:41.046 [DBG] Sent 184 bytes
no ex
17:06:41.170 [DBG] 31 bytes read
no ex
17:06:41.170 [DBG] Sent 30 bytes
no ex
17:06:44.169 [DBG] 31 bytes read
no ex
17:06:44.170 [DBG] Sent 30 bytes
no ex
17:06:46.046 [DBG] Sent 184 bytes
no ex
17:06:47.169 [DBG] 31 bytes read
no ex
17:06:47.170 [DBG] Sent 30 bytes
no ex
17:06:50.162 [DBG] 31 bytes read
no ex
17:06:50.163 [DBG] Sent 30 bytes
no ex
17:06:51.047 [DBG] Sent 184 bytes
no ex
17:06:53.165 [DBG] 31 bytes read
no ex
17:06:53.165 [DBG] Sent 30 bytes
no ex
17:06:55.625 [DBG] 31 bytes read
no ex
17:06:55.625 [DBG] Sent 30 bytes
no ex
17:06:56.047 [DBG] Sent 184 bytes
no ex
17:06:58.627 [DBG] 31 bytes read
no ex
17:06:58.627 [DBG] Sent 30 bytes
no ex
17:07:01.033 [DBG] Sent 184 bytes
no ex
17:07:01.659 [DBG] 31 bytes read
no ex
17:07:01.660 [DBG] Sent 30 bytes
no ex
17:07:04.623 [DBG] 31 bytes read
no ex
17:07:04.624 [DBG] Sent 30 bytes
no ex
17:07:06.040 [DBG] Sent 184 bytes
no ex
17:07:07.624 [DBG] 31 bytes read
no ex
17:07:07.625 [DBG] Sent 30 bytes
no ex
17:07:10.631 [DBG] 31 bytes read
no ex
17:07:10.631 [DBG] Sent 30 bytes
no ex
17:07:11.042 [DBG] Sent 184 bytes
no ex
17:07:13.626 [DBG] 31 bytes read
no ex
17:07:13.627 [DBG] Sent 30 bytes
no ex
17:07:16.045 [DBG] Sent 184 bytes
no ex
17:07:16.634 [DBG] 31 bytes read
no ex
17:07:16.634 [DBG] Sent 30 bytes
no ex
17:07:18.853 [DBG] 146 bytes read
no ex
17:07:18.853 [DBG] Sent 52 bytes
no ex
17:07:18.854 [DBG] Sent 41 bytes
no ex
17:07:18.854 [DBG] Sent 819 bytes
no ex
17:07:18.867 [DBG] Sent 41 bytes
no ex
17:07:18.873 [DBG] 47 bytes read
no ex
17:07:18.873 [DBG] 47 bytes read
no ex
17:07:18.883 [DBG] Sent 61 bytes
no ex
17:07:18.883 [DBG] Sent 61 bytes
no ex
17:07:19.243 [DBG] 220 bytes read
no ex
17:07:19.259 [DBG] Sent 61 bytes
no ex
17:07:19.259 [DBG] Sent 61 bytes
no ex
17:07:19.259 [DBG] Sent 41 bytes
no ex
17:07:19.260 [DBG] Sent 61 bytes
no ex
17:07:19.260 [DBG] Sent 61 bytes
no ex
17:07:19.626 [DBG] 31 bytes read
no ex
17:07:19.627 [DBG] Sent 30 bytes
no ex
17:07:21.040 [DBG] Sent 184 bytes
no ex
17:07:22.623 [DBG] 31 bytes read
no ex
17:07:22.624 [DBG] Sent 30 bytes
no ex
17:07:25.624 [DBG] 31 bytes read
no ex
17:07:25.624 [DBG] Sent 30 bytes
no ex
17:07:26.042 [DBG] Sent 184 bytes
no ex
17:07:28.622 [DBG] 31 bytes read
no ex
17:07:28.623 [DBG] Sent 30 bytes
no ex
17:07:31.043 [DBG] Sent 184 bytes
no ex
17:07:31.629 [DBG] 31 bytes read
no ex
17:07:31.629 [DBG] Sent 30 bytes
no ex
17:07:34.640 [DBG] 31 bytes read
no ex
17:07:34.640 [DBG] Sent 30 bytes
no ex
17:07:36.046 [DBG] Sent 184 bytes
no ex
17:07:36.420 [INF] RunHandleWinLoss: 0
17:07:36.420 [INF] CalculateResult: 20220805
17:07:36.420 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
17:07:37.625 [DBG] 31 bytes read
no ex
17:07:37.625 [DBG] Sent 30 bytes
no ex
17:07:40.632 [DBG] 31 bytes read
no ex
17:07:40.633 [DBG] Sent 30 bytes
no ex
17:07:41.050 [DBG] Sent 184 bytes
no ex
17:07:43.624 [DBG] 31 bytes read
no ex
17:07:43.624 [DBG] Sent 30 bytes
no ex
17:07:46.049 [DBG] Sent 184 bytes
no ex
17:07:46.631 [DBG] 32 bytes read
no ex
17:07:46.631 [DBG] Sent 31 bytes
no ex
17:07:49.624 [DBG] 32 bytes read
no ex
17:07:49.624 [DBG] Sent 31 bytes
no ex
17:07:51.057 [DBG] Sent 184 bytes
no ex
17:07:52.623 [DBG] 32 bytes read
no ex
17:07:52.624 [DBG] Sent 31 bytes
no ex
17:07:55.625 [DBG] 32 bytes read
no ex
17:07:55.625 [DBG] Sent 31 bytes
no ex
17:07:56.052 [DBG] Sent 184 bytes
no ex
17:07:58.624 [DBG] 32 bytes read
no ex
17:07:58.624 [DBG] Sent 31 bytes
no ex
17:08:01.054 [DBG] Sent 184 bytes
no ex
17:08:01.625 [DBG] 32 bytes read
no ex
17:08:01.625 [DBG] Sent 31 bytes
no ex
17:08:04.624 [DBG] 32 bytes read
no ex
17:08:04.624 [DBG] Sent 31 bytes
no ex
17:08:06.058 [DBG] Sent 184 bytes
no ex
17:08:06.436 [DBG] 48 bytes read
no ex
17:08:06.449 [DBG] Sent 62 bytes
no ex
17:08:07.091 [DBG] 48 bytes read
no ex
17:08:07.098 [DBG] Sent 62 bytes
no ex
17:08:07.489 [DBG] 48 bytes read
no ex
17:08:07.491 [DBG] Sent 62 bytes
no ex
17:08:07.633 [DBG] 32 bytes read
no ex
17:08:07.633 [DBG] Sent 31 bytes
no ex
17:08:08.518 [DBG] 48 bytes read
no ex
17:08:08.533 [DBG] Sent 62 bytes
no ex
17:08:09.032 [DBG] 48 bytes read
no ex
17:08:09.046 [DBG] Sent 62 bytes
no ex
17:08:11.045 [DBG] Sent 184 bytes
no ex
17:08:11.244 [DBG] 32 bytes read
no ex
17:08:11.244 [DBG] Sent 31 bytes
no ex
17:08:14.096 [DBG] 32 bytes read
no ex
17:08:14.096 [DBG] Sent 31 bytes
no ex
17:08:16.049 [DBG] Sent 184 bytes
no ex
17:08:16.626 [DBG] 32 bytes read
no ex
17:08:16.626 [DBG] Sent 31 bytes
no ex
17:08:19.627 [DBG] 32 bytes read
no ex
17:08:19.627 [DBG] Sent 31 bytes
no ex
17:08:21.053 [DBG] Sent 184 bytes
no ex
17:08:22.622 [DBG] 32 bytes read
no ex
17:08:22.623 [DBG] Sent 31 bytes
no ex
17:08:25.628 [DBG] 32 bytes read
no ex
17:08:25.628 [DBG] Sent 31 bytes
no ex
17:08:26.056 [DBG] Sent 184 bytes
no ex
17:08:28.623 [DBG] 32 bytes read
no ex
17:08:28.624 [DBG] Sent 31 bytes
no ex
17:08:31.059 [DBG] Sent 184 bytes
no ex
17:08:31.628 [DBG] 32 bytes read
no ex
17:08:31.628 [DBG] Sent 31 bytes
no ex
17:08:34.627 [DBG] 32 bytes read
no ex
17:08:34.628 [DBG] Sent 31 bytes
no ex
17:08:36.060 [DBG] Sent 184 bytes
no ex
17:08:37.623 [DBG] 32 bytes read
no ex
17:08:37.623 [DBG] Sent 31 bytes
no ex
17:08:40.636 [DBG] 32 bytes read
no ex
17:08:40.636 [DBG] Sent 31 bytes
no ex
17:08:41.065 [DBG] Sent 184 bytes
no ex
17:08:43.622 [DBG] 32 bytes read
no ex
17:08:43.623 [DBG] Sent 31 bytes
no ex
17:08:46.058 [DBG] Sent 184 bytes
no ex
17:08:46.630 [DBG] 32 bytes read
no ex
17:08:46.630 [DBG] Sent 31 bytes
no ex
17:08:49.631 [DBG] 32 bytes read
no ex
17:08:49.632 [DBG] Sent 31 bytes
no ex
17:08:51.056 [DBG] Sent 184 bytes
no ex
17:08:52.624 [DBG] 32 bytes read
no ex
17:08:52.624 [DBG] Sent 31 bytes
no ex
17:08:55.626 [DBG] 32 bytes read
no ex
17:08:55.626 [DBG] Sent 31 bytes
no ex
17:08:56.061 [DBG] Sent 184 bytes
no ex
17:08:58.638 [DBG] 32 bytes read
no ex
17:08:58.638 [DBG] Sent 31 bytes
no ex
17:09:01.067 [DBG] Sent 184 bytes
no ex
17:09:01.634 [DBG] 32 bytes read
no ex
17:09:01.634 [DBG] Sent 31 bytes
no ex
17:09:04.638 [DBG] 32 bytes read
no ex
17:09:04.638 [DBG] Sent 31 bytes
no ex
17:09:06.055 [DBG] Sent 184 bytes
no ex
17:09:07.629 [DBG] 32 bytes read
no ex
17:09:07.629 [DBG] Sent 31 bytes
no ex
17:09:10.636 [DBG] 32 bytes read
no ex
17:09:10.636 [DBG] Sent 31 bytes
no ex
17:09:11.070 [DBG] Sent 184 bytes
no ex
17:09:13.636 [DBG] 32 bytes read
no ex
17:09:13.636 [DBG] Sent 31 bytes
no ex
17:09:16.067 [DBG] Sent 184 bytes
no ex
17:09:16.627 [DBG] 32 bytes read
no ex
17:09:16.628 [DBG] Sent 31 bytes
no ex
17:09:19.627 [DBG] 32 bytes read
no ex
17:09:19.627 [DBG] Sent 31 bytes
no ex
17:09:21.070 [DBG] Sent 184 bytes
no ex
17:09:22.625 [DBG] 32 bytes read
no ex
17:09:22.625 [DBG] Sent 31 bytes
no ex
17:09:25.627 [DBG] 32 bytes read
no ex
17:09:25.627 [DBG] Sent 31 bytes
no ex
17:09:26.059 [DBG] Sent 184 bytes
no ex
17:09:28.635 [DBG] 32 bytes read
no ex
17:09:28.635 [DBG] Sent 31 bytes
no ex
17:09:31.066 [DBG] Sent 184 bytes
no ex
17:09:31.635 [DBG] 32 bytes read
no ex
17:09:31.636 [DBG] Sent 31 bytes
no ex
17:09:34.629 [DBG] 32 bytes read
no ex
17:09:34.630 [DBG] Sent 31 bytes
no ex
17:09:36.070 [DBG] Sent 184 bytes
no ex
17:09:37.942 [DBG] 32 bytes read
no ex
17:09:37.942 [DBG] Sent 31 bytes
no ex
17:09:40.634 [DBG] 32 bytes read
no ex
17:09:40.634 [DBG] Sent 31 bytes
no ex
17:09:41.075 [DBG] Sent 184 bytes
no ex
17:09:43.633 [DBG] 32 bytes read
no ex
17:09:43.633 [DBG] Sent 31 bytes
no ex
17:09:46.074 [DBG] Sent 184 bytes
no ex
17:09:46.628 [DBG] 32 bytes read
no ex
17:09:46.629 [DBG] Sent 31 bytes
no ex
17:09:49.634 [DBG] 32 bytes read
no ex
17:09:49.634 [DBG] Sent 31 bytes
no ex
17:09:51.064 [DBG] Sent 184 bytes
no ex
17:09:52.628 [DBG] 32 bytes read
no ex
17:09:52.628 [DBG] Sent 31 bytes
no ex
17:09:55.628 [DBG] 32 bytes read
no ex
17:09:55.628 [DBG] Sent 31 bytes
no ex
17:09:56.072 [DBG] Sent 184 bytes
no ex
17:09:58.630 [DBG] 32 bytes read
no ex
17:09:58.631 [DBG] Sent 31 bytes
no ex
17:10:01.073 [DBG] Sent 184 bytes
no ex
17:10:01.691 [DBG] 32 bytes read
no ex
17:10:01.692 [DBG] Sent 31 bytes
no ex
17:10:04.623 [DBG] 32 bytes read
no ex
17:10:04.623 [DBG] Sent 31 bytes
no ex
17:10:06.062 [DBG] Sent 184 bytes
no ex
17:10:07.625 [DBG] 32 bytes read
no ex
17:10:07.625 [DBG] Sent 31 bytes
no ex
17:10:10.627 [DBG] 32 bytes read
no ex
17:10:10.627 [DBG] Sent 31 bytes
no ex
17:10:11.067 [DBG] Sent 184 bytes
no ex
17:10:13.624 [DBG] 32 bytes read
no ex
17:10:13.624 [DBG] Sent 31 bytes
no ex
17:10:16.069 [DBG] Sent 184 bytes
no ex
17:10:17.247 [DBG] 32 bytes read
no ex
17:10:17.247 [DBG] Sent 31 bytes
no ex
17:10:19.624 [DBG] 32 bytes read
no ex
17:10:19.624 [DBG] Sent 31 bytes
no ex
17:10:21.071 [DBG] Sent 184 bytes
no ex
17:10:22.624 [DBG] 32 bytes read
no ex
17:10:22.624 [DBG] Sent 31 bytes
no ex
17:10:25.624 [DBG] 32 bytes read
no ex
17:10:25.625 [DBG] Sent 31 bytes
no ex
17:10:26.077 [DBG] Sent 184 bytes
no ex
17:10:28.625 [DBG] 32 bytes read
no ex
17:10:28.625 [DBG] Sent 31 bytes
no ex
17:10:31.079 [DBG] Sent 184 bytes
no ex
17:10:31.624 [DBG] 32 bytes read
no ex
17:10:31.624 [DBG] Sent 31 bytes
no ex
17:10:34.640 [DBG] 32 bytes read
no ex
17:10:34.640 [DBG] Sent 31 bytes
no ex
17:10:36.078 [DBG] Sent 184 bytes
no ex
17:10:37.627 [DBG] 32 bytes read
no ex
17:10:37.627 [DBG] Sent 31 bytes
no ex
17:10:40.633 [DBG] 32 bytes read
no ex
17:10:40.633 [DBG] Sent 31 bytes
no ex
17:10:41.080 [DBG] Sent 184 bytes
no ex
17:10:43.625 [DBG] 32 bytes read
no ex
17:10:43.625 [DBG] Sent 31 bytes
no ex
17:10:46.073 [DBG] Sent 184 bytes
no ex
17:10:46.625 [DBG] 32 bytes read
no ex
17:10:46.625 [DBG] Sent 31 bytes
no ex
17:10:49.625 [DBG] 32 bytes read
no ex
17:10:49.626 [DBG] Sent 31 bytes
no ex
17:10:51.076 [DBG] Sent 184 bytes
no ex
17:10:52.624 [DBG] 32 bytes read
no ex
17:10:52.624 [DBG] Sent 31 bytes
no ex
17:10:55.940 [DBG] 32 bytes read
no ex
17:10:55.940 [DBG] Sent 31 bytes
no ex
17:10:56.083 [DBG] Sent 184 bytes
no ex
17:10:58.624 [DBG] 32 bytes read
no ex
17:10:58.624 [DBG] Sent 31 bytes
no ex
17:11:01.082 [DBG] Sent 184 bytes
no ex
17:11:01.623 [DBG] 32 bytes read
no ex
17:11:01.624 [DBG] Sent 31 bytes
no ex
17:11:04.626 [DBG] 32 bytes read
no ex
17:11:04.626 [DBG] Sent 31 bytes
no ex
17:11:06.076 [DBG] Sent 184 bytes
no ex
17:11:07.623 [DBG] 32 bytes read
no ex
17:11:07.623 [DBG] Sent 31 bytes
no ex
17:11:10.623 [DBG] 32 bytes read
no ex
17:11:10.624 [DBG] Sent 31 bytes
no ex
17:11:11.077 [DBG] Sent 184 bytes
no ex
17:11:13.932 [DBG] 32 bytes read
no ex
17:11:13.932 [DBG] Sent 31 bytes
no ex
17:11:16.071 [DBG] Sent 184 bytes
no ex
17:11:17.103 [DBG] 32 bytes read
no ex
17:11:17.104 [DBG] Sent 31 bytes
no ex
17:11:19.622 [DBG] 32 bytes read
no ex
17:11:19.623 [DBG] Sent 31 bytes
no ex
17:11:20.688 [DBG] 8 bytes read
no ex
17:11:20.688 [DBG] Sent 4 bytes
no ex
17:11:35.165 [INF] RunScan: 0
17:11:35.165 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:11)","Data":null,"DataObj":null}
17:12:36.421 [INF] RunHandleWinLoss: 0
17:12:36.421 [INF] CalculateResult: 20220805
17:12:36.421 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
17:16:35.165 [INF] RunScan: 0
17:16:35.165 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:16)","Data":null,"DataObj":null}
17:17:36.423 [INF] RunHandleWinLoss: 0
17:17:36.423 [INF] CalculateResult: 20220805
17:17:36.423 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
17:21:35.165 [INF] RunScan: 0
17:21:35.165 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:21)","Data":null,"DataObj":null}
17:22:36.424 [INF] RunHandleWinLoss: 0
17:22:36.424 [INF] CalculateResult: 20220805
17:22:36.424 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
17:26:35.166 [INF] RunScan: 0
17:26:35.166 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:26)","Data":null,"DataObj":null}
17:27:36.426 [INF] RunHandleWinLoss: 0
17:27:36.426 [INF] CalculateResult: 20220805
17:27:36.426 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
17:31:35.166 [INF] RunScan: 0
17:31:35.166 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:31)","Data":null,"DataObj":null}
17:32:26.438 [DBG] Client connected from 43.129.219.189:43568
no ex
17:32:26.438 [DBG] 128 bytes read
no ex
17:32:36.403 [DBG] 0 bytes read. Closing.
no ex
17:32:36.427 [INF] RunHandleWinLoss: 0
17:32:36.427 [INF] CalculateResult: 20220805
17:32:36.427 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
17:32:36.440 [DBG] Client connected from 43.129.219.189:56790
no ex
17:32:36.440 [DBG] 427 bytes read
no ex
17:32:46.440 [DBG] 0 bytes read. Closing.
no ex
17:32:46.475 [DBG] Client connected from 43.129.219.189:36808
no ex
17:32:46.475 [DBG] 427 bytes read
no ex
17:32:56.476 [DBG] 0 bytes read. Closing.
no ex
17:32:56.513 [DBG] Client connected from 43.129.219.189:42180
no ex
17:32:56.513 [DBG] 348 bytes read
no ex
17:33:06.514 [DBG] 0 bytes read. Closing.
no ex
17:33:06.551 [DBG] Client connected from 43.129.219.189:46402
no ex
17:33:06.551 [DBG] 334 bytes read
no ex
17:33:16.552 [DBG] 0 bytes read. Closing.
no ex
17:33:16.590 [DBG] Client connected from 43.129.219.189:49672
no ex
17:33:16.590 [DBG] 415 bytes read
no ex
17:33:26.591 [DBG] 0 bytes read. Closing.
no ex
17:33:26.626 [DBG] Client connected from 43.129.219.189:52498
no ex
17:33:26.627 [DBG] 416 bytes read
no ex
17:33:36.626 [DBG] 0 bytes read. Closing.
no ex
17:33:36.664 [DBG] Client connected from 43.129.219.189:55086
no ex
17:33:36.664 [DBG] 429 bytes read
no ex
17:33:46.665 [DBG] 0 bytes read. Closing.
no ex
17:33:46.698 [DBG] Client connected from 43.129.219.189:57342
no ex
17:33:46.699 [DBG] 429 bytes read
no ex
17:33:56.698 [DBG] 0 bytes read. Closing.
no ex
17:33:56.730 [DBG] Client connected from 43.129.219.189:59434
no ex
17:33:56.731 [DBG] 419 bytes read
no ex
17:34:06.731 [DBG] 0 bytes read. Closing.
no ex
17:34:06.771 [DBG] Client connected from 43.129.219.189:33334
no ex
17:34:06.771 [DBG] 442 bytes read
no ex
17:34:16.772 [DBG] 0 bytes read. Closing.
no ex
17:36:35.166 [INF] RunScan: 0
17:36:35.166 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:36)","Data":null,"DataObj":null}
17:37:36.429 [INF] RunHandleWinLoss: 0
17:37:36.429 [INF] CalculateResult: 20220805
17:37:36.429 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
17:40:07.279 [DBG] Client connected from 127.0.0.1:44570
no ex
17:40:07.279 [DBG] 988 bytes read
no ex
17:40:07.280 [DBG] Building Hybi-14 Response
no ex
17:40:07.280 [DBG] Sent 129 bytes
no ex
17:40:11.323 [DBG] Sent 184 bytes
no ex
17:40:12.302 [DBG] Sent 4 bytes
no ex
17:40:12.304 [DBG] Swallowing ObjectDisposedException
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
17:40:17.471 [DBG] Client connected from 127.0.0.1:44582
no ex
17:40:17.471 [DBG] 988 bytes read
no ex
17:40:17.471 [DBG] Building Hybi-14 Response
no ex
17:40:17.471 [DBG] Sent 129 bytes
no ex
17:40:17.599 [DBG] 31 bytes read
no ex
17:40:17.599 [DBG] Sent 30 bytes
no ex
17:40:20.601 [DBG] 31 bytes read
no ex
17:40:20.601 [DBG] Sent 30 bytes
no ex
17:40:21.322 [DBG] Sent 184 bytes
no ex
17:40:23.607 [DBG] 31 bytes read
no ex
17:40:23.607 [DBG] Sent 30 bytes
no ex
17:40:26.316 [DBG] Sent 184 bytes
no ex
17:40:26.601 [DBG] 31 bytes read
no ex
17:40:26.602 [DBG] Sent 30 bytes
no ex
17:40:29.612 [DBG] 31 bytes read
no ex
17:40:29.612 [DBG] Sent 30 bytes
no ex
17:40:31.323 [DBG] Sent 184 bytes
no ex
17:40:32.695 [DBG] 31 bytes read
no ex
17:40:32.695 [DBG] Sent 30 bytes
no ex
17:40:34.463 [DBG] 0 bytes read. Closing.
no ex
17:40:36.963 [DBG] Client connected from 127.0.0.1:44618
no ex
17:40:36.963 [DBG] 988 bytes read
no ex
17:40:36.963 [DBG] Building Hybi-14 Response
no ex
17:40:36.963 [DBG] Sent 129 bytes
no ex
17:40:37.053 [DBG] 31 bytes read
no ex
17:40:37.054 [DBG] Sent 30 bytes
no ex
17:40:40.050 [DBG] 31 bytes read
no ex
17:40:40.050 [DBG] Sent 30 bytes
no ex
17:40:41.332 [DBG] Sent 184 bytes
no ex
17:40:42.265 [DBG] 47 bytes read
no ex
17:40:42.266 [DBG] Sent 25 bytes
no ex
17:40:43.055 [DBG] 31 bytes read
no ex
17:40:43.055 [DBG] Sent 30 bytes
no ex
17:40:43.228 [INF] 8888 bot: 2, peer: 1, ccu: 0
17:40:43.423 [DBG] 94 bytes read
no ex
17:40:43.423 [DBG] Sent 25 bytes
no ex
17:40:43.424 [DBG] Sent 25 bytes
no ex
17:40:46.052 [DBG] 31 bytes read
no ex
17:40:46.052 [DBG] Sent 30 bytes
no ex
17:40:46.319 [DBG] Sent 184 bytes
no ex
17:40:49.052 [DBG] 31 bytes read
no ex
17:40:49.052 [DBG] Sent 30 bytes
no ex
17:40:51.324 [DBG] Sent 184 bytes
no ex
17:40:52.091 [DBG] 31 bytes read
no ex
17:40:52.091 [DBG] Sent 30 bytes
no ex
17:40:55.088 [DBG] 31 bytes read
no ex
17:40:55.088 [DBG] Sent 30 bytes
no ex
17:40:56.327 [DBG] Sent 184 bytes
no ex
17:40:58.102 [DBG] 31 bytes read
no ex
17:40:58.102 [DBG] Sent 30 bytes
no ex
17:41:01.104 [DBG] 31 bytes read
no ex
17:41:01.104 [DBG] Sent 30 bytes
no ex
17:41:01.334 [DBG] Sent 184 bytes
no ex
17:41:04.098 [DBG] 31 bytes read
no ex
17:41:04.098 [DBG] Sent 30 bytes
no ex
17:41:06.336 [DBG] Sent 184 bytes
no ex
17:41:07.110 [DBG] 31 bytes read
no ex
17:41:07.110 [DBG] Sent 30 bytes
no ex
17:41:10.105 [DBG] 31 bytes read
no ex
17:41:10.106 [DBG] Sent 30 bytes
no ex
17:41:11.324 [DBG] Sent 184 bytes
no ex
17:41:13.109 [DBG] 31 bytes read
no ex
17:41:13.109 [DBG] Sent 30 bytes
no ex
17:41:16.109 [DBG] 31 bytes read
no ex
17:41:16.110 [DBG] Sent 30 bytes
no ex
17:41:16.326 [DBG] Sent 184 bytes
no ex
17:41:19.112 [DBG] 31 bytes read
no ex
17:41:19.112 [DBG] Sent 30 bytes
no ex
17:41:21.329 [DBG] Sent 184 bytes
no ex
17:41:22.104 [DBG] 31 bytes read
no ex
17:41:22.104 [DBG] Sent 30 bytes
no ex
17:41:25.109 [DBG] 31 bytes read
no ex
17:41:25.110 [DBG] Sent 30 bytes
no ex
17:41:26.332 [DBG] Sent 184 bytes
no ex
17:41:28.105 [DBG] 31 bytes read
no ex
17:41:28.106 [DBG] Sent 30 bytes
no ex
17:41:31.129 [DBG] 31 bytes read
no ex
17:41:31.130 [DBG] Sent 30 bytes
no ex
17:41:31.337 [DBG] Sent 184 bytes
no ex
17:41:34.107 [DBG] 31 bytes read
no ex
17:41:34.107 [DBG] Sent 30 bytes
no ex
17:41:35.166 [INF] RunScan: 0
17:41:35.166 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:41)","Data":null,"DataObj":null}
17:41:36.338 [DBG] Sent 184 bytes
no ex
17:41:37.109 [DBG] 31 bytes read
no ex
17:41:37.110 [DBG] Sent 30 bytes
no ex
17:41:40.106 [DBG] 31 bytes read
no ex
17:41:40.106 [DBG] Sent 30 bytes
no ex
17:41:41.339 [DBG] Sent 184 bytes
no ex
17:41:43.121 [DBG] 31 bytes read
no ex
17:41:43.121 [DBG] Sent 30 bytes
no ex
17:41:46.115 [DBG] 31 bytes read
no ex
17:41:46.115 [DBG] Sent 30 bytes
no ex
17:41:46.326 [DBG] Sent 184 bytes
no ex
17:41:49.118 [DBG] 31 bytes read
no ex
17:41:49.119 [DBG] Sent 30 bytes
no ex
17:41:51.342 [DBG] Sent 184 bytes
no ex
17:41:52.114 [DBG] 31 bytes read
no ex
17:41:52.115 [DBG] Sent 30 bytes
no ex
17:41:55.112 [DBG] 31 bytes read
no ex
17:41:55.112 [DBG] Sent 30 bytes
no ex
17:41:56.326 [DBG] Sent 184 bytes
no ex
17:41:58.110 [DBG] 31 bytes read
no ex
17:41:58.110 [DBG] Sent 30 bytes
no ex
17:42:01.114 [DBG] 31 bytes read
no ex
17:42:01.114 [DBG] Sent 30 bytes
no ex
17:42:01.330 [DBG] Sent 184 bytes
no ex
17:42:04.118 [DBG] 31 bytes read
no ex
17:42:04.118 [DBG] Sent 30 bytes
no ex
17:42:06.339 [DBG] Sent 184 bytes
no ex
17:42:07.117 [DBG] 31 bytes read
no ex
17:42:07.118 [DBG] Sent 30 bytes
no ex
17:42:10.114 [DBG] 31 bytes read
no ex
17:42:10.114 [DBG] Sent 30 bytes
no ex
17:42:11.330 [DBG] Sent 184 bytes
no ex
17:42:13.643 [DBG] 31 bytes read
no ex
17:42:13.643 [DBG] Sent 30 bytes
no ex
17:42:16.335 [DBG] Sent 184 bytes
no ex
17:42:17.632 [DBG] 31 bytes read
no ex
17:42:17.632 [DBG] Sent 30 bytes
no ex
17:42:21.345 [DBG] Sent 184 bytes
no ex
17:42:21.646 [DBG] 31 bytes read
no ex
17:42:21.646 [DBG] Sent 30 bytes
no ex
17:42:22.760 [DBG] 0 bytes read. Closing.
no ex
17:42:22.760 [INF] 8888 bot: 2, peer: 0, ccu: 0
17:42:36.430 [INF] RunHandleWinLoss: 0
17:42:36.430 [INF] CalculateResult: 20220805
17:42:36.430 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
17:46:35.166 [INF] RunScan: 0
17:46:35.167 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:46)","Data":null,"DataObj":null}
17:47:36.432 [INF] RunHandleWinLoss: 0
17:47:36.432 [INF] CalculateResult: 20220805
17:47:36.432 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
17:51:35.167 [INF] RunScan: 0
17:51:35.167 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:51)","Data":null,"DataObj":null}
17:52:36.434 [INF] RunHandleWinLoss: 0
17:52:36.434 [INF] CalculateResult: 20220805
17:52:36.434 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
17:56:35.167 [INF] RunScan: 0
17:56:35.167 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:56)","Data":null,"DataObj":null}
17:57:36.436 [INF] RunHandleWinLoss: 0
17:57:36.436 [INF] CalculateResult: 20220805
17:57:36.436 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
18:01:35.167 [INF] RunScan: 0
18:01:35.167 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:1)","Data":null,"DataObj":null}
18:02:36.437 [INF] RunHandleWinLoss: 0
18:02:36.437 [INF] CalculateResult: 20220805
18:02:36.437 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
18:06:35.167 [INF] RunScan: 0
18:06:35.168 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:6)","Data":null,"DataObj":null}
18:07:36.439 [INF] RunHandleWinLoss: 0
18:07:36.439 [INF] CalculateResult: 20220805
18:07:36.439 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
18:11:35.168 [INF] RunScan: 0
18:11:35.168 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:11)","Data":null,"DataObj":null}
18:12:36.441 [INF] RunHandleWinLoss: 0
18:12:36.441 [INF] CalculateResult: 20220805
18:12:36.441 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
18:16:35.168 [INF] RunScan: 0
18:16:35.168 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:16)","Data":null,"DataObj":null}
18:17:36.444 [INF] RunHandleWinLoss: 0
18:17:36.444 [INF] CalculateResult: 20220805
18:17:36.444 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
18:21:35.168 [INF] RunScan: 0
18:21:35.168 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:21)","Data":null,"DataObj":null}
18:22:36.447 [INF] RunHandleWinLoss: 0
18:22:36.447 [INF] CalculateResult: 20220805
18:22:36.447 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
18:26:35.168 [INF] RunScan: 0
18:26:35.168 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:26)","Data":null,"DataObj":null}
18:27:36.449 [INF] RunHandleWinLoss: 0
18:27:36.449 [INF] CalculateResult: 20220805
18:27:36.449 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
18:31:35.168 [INF] RunScan: 0
18:31:35.169 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:31)","Data":null,"DataObj":null}
18:32:36.450 [INF] RunHandleWinLoss: 0
18:32:36.450 [INF] CalculateResult: 20220805
18:32:36.450 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
18:36:35.169 [INF] RunScan: 0
18:36:35.169 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:36)","Data":null,"DataObj":null}
18:37:36.452 [INF] RunHandleWinLoss: 0
18:37:36.452 [INF] CalculateResult: 20220805
18:37:36.452 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
18:41:35.169 [INF] RunScan: 0
18:41:35.414 [INF] ScanXskt result: 49513-91374-13523-80169-21823-68922-93413-71315-35384-07823-0393-0045-1630-7989-5811-7088-8901-1847-2888-9731-840-991-794-73-16-14-67
18:41:35.414 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng 5.","Data":null,"DataObj":null}
18:42:36.453 [INF] RunHandleWinLoss: 0
18:42:36.453 [INF] CalculateResult: 20220805
18:42:36.453 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
18:46:35.414 [INF] RunScan: 0
18:46:35.415 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
18:47:36.454 [INF] RunHandleWinLoss: 0
18:47:36.455 [INF] CalculateResult: 20220805
18:47:36.455 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
18:51:35.415 [INF] RunScan: 0
18:51:35.416 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
18:52:36.456 [INF] RunHandleWinLoss: 0
18:52:36.456 [INF] CalculateResult: 20220805
18:52:36.456 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
18:56:35.416 [INF] RunScan: 0
18:56:35.416 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
18:57:36.459 [INF] RunHandleWinLoss: 0
18:57:36.459 [INF] CalculateResult: 20220805
18:57:36.459 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
19:01:35.416 [INF] RunScan: 0
19:01:35.416 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:02:36.460 [INF] RunHandleWinLoss: 0
19:02:36.460 [INF] CalculateResult: 20220805
19:02:36.460 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
19:06:35.416 [INF] RunScan: 0
19:06:35.416 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:07:36.462 [INF] RunHandleWinLoss: 0
19:07:36.462 [INF] CalculateResult: 20220805
19:07:36.462 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
19:11:35.416 [INF] RunScan: 0
19:11:35.417 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:12:36.464 [INF] RunHandleWinLoss: 0
19:12:36.464 [INF] CalculateResult: 20220805
19:12:36.464 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
19:16:35.417 [INF] RunScan: 0
19:16:35.417 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:17:36.465 [INF] RunHandleWinLoss: 0
19:17:36.465 [INF] CalculateResult: 20220805
19:17:36.465 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
19:21:35.417 [INF] RunScan: 0
19:21:35.417 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:22:36.467 [INF] RunHandleWinLoss: 0
19:22:36.467 [INF] CalculateResult: 20220805
19:22:36.467 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
19:26:35.418 [INF] RunScan: 0
19:26:35.418 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:27:36.469 [INF] RunHandleWinLoss: 0
19:27:36.469 [INF] CalculateResult: 20220805
19:27:36.469 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
19:31:35.418 [INF] RunScan: 0
19:31:35.418 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:32:36.470 [INF] RunHandleWinLoss: 0
19:32:36.470 [INF] CalculateResult: 20220805
19:32:36.470 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
19:36:35.418 [INF] RunScan: 0
19:36:35.418 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:37:36.472 [INF] RunHandleWinLoss: 0
19:37:36.472 [INF] CalculateResult: 20220805
19:37:36.472 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
19:41:35.418 [INF] RunScan: 0
19:41:35.418 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:42:36.473 [INF] RunHandleWinLoss: 0
19:42:36.473 [INF] CalculateResult: 20220805
19:42:36.473 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
19:46:35.419 [INF] RunScan: 0
19:46:35.419 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:47:36.474 [INF] RunHandleWinLoss: 0
19:47:36.475 [INF] CalculateResult: 20220805
19:47:36.475 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
19:51:35.419 [INF] RunScan: 0
19:51:35.419 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:52:36.476 [INF] RunHandleWinLoss: 0
19:52:36.476 [INF] CalculateResult: 20220805
19:52:36.476 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
19:56:35.419 [INF] RunScan: 0
19:56:35.419 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:57:36.479 [INF] RunHandleWinLoss: 0
19:57:36.479 [INF] CalculateResult: 20220805
19:57:36.479 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
20:01:35.419 [INF] RunScan: 0
20:01:35.419 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:1)","Data":null,"DataObj":null}
20:02:36.481 [INF] RunHandleWinLoss: 0
20:02:36.481 [INF] CalculateResult: 20220805
20:02:36.481 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
20:06:35.419 [INF] RunScan: 0
20:06:35.420 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:6)","Data":null,"DataObj":null}
20:07:36.482 [INF] RunHandleWinLoss: 0
20:07:36.482 [INF] CalculateResult: 20220805
20:07:36.482 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
20:11:35.420 [INF] RunScan: 0
20:11:35.420 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:11)","Data":null,"DataObj":null}
20:12:36.484 [INF] RunHandleWinLoss: 0
20:12:36.484 [INF] CalculateResult: 20220805
20:12:36.484 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
20:16:35.420 [INF] RunScan: 0
20:16:35.420 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:16)","Data":null,"DataObj":null}
20:17:36.487 [INF] RunHandleWinLoss: 0
20:17:36.487 [INF] CalculateResult: 20220805
20:17:36.487 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
20:21:35.420 [INF] RunScan: 0
20:21:35.420 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:21)","Data":null,"DataObj":null}
20:22:36.490 [INF] RunHandleWinLoss: 0
20:22:36.490 [INF] CalculateResult: 20220805
20:22:36.490 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
20:26:35.420 [INF] RunScan: 0
20:26:35.420 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:26)","Data":null,"DataObj":null}
20:27:36.491 [INF] RunHandleWinLoss: 0
20:27:36.491 [INF] CalculateResult: 20220805
20:27:36.491 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
20:31:35.421 [INF] RunScan: 0
20:31:35.421 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:31)","Data":null,"DataObj":null}
20:32:36.492 [INF] RunHandleWinLoss: 0
20:32:36.492 [INF] CalculateResult: 20220805
20:32:36.493 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
20:36:35.421 [INF] RunScan: 0
20:36:35.421 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:36)","Data":null,"DataObj":null}
20:37:36.494 [INF] RunHandleWinLoss: 0
20:37:36.494 [INF] CalculateResult: 20220805
20:37:36.494 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
20:41:35.421 [INF] RunScan: 0
20:41:35.421 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:41)","Data":null,"DataObj":null}
20:42:36.496 [INF] RunHandleWinLoss: 0
20:42:36.496 [INF] CalculateResult: 20220805
20:42:36.496 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
20:46:35.421 [INF] RunScan: 0
20:46:35.421 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:46)","Data":null,"DataObj":null}
20:47:36.497 [INF] RunHandleWinLoss: 0
20:47:36.498 [INF] CalculateResult: 20220805
20:47:36.498 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
20:51:35.421 [INF] RunScan: 0
20:51:35.421 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:51)","Data":null,"DataObj":null}
20:52:36.499 [INF] RunHandleWinLoss: 0
20:52:36.499 [INF] CalculateResult: 20220805
20:52:36.499 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
20:56:35.422 [INF] RunScan: 0
20:56:35.422 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:56)","Data":null,"DataObj":null}
20:57:36.500 [INF] RunHandleWinLoss: 0
20:57:36.501 [INF] CalculateResult: 20220805
20:57:36.501 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
21:01:35.422 [INF] RunScan: 0
21:01:35.422 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:1)","Data":null,"DataObj":null}
21:02:36.502 [INF] RunHandleWinLoss: 0
21:02:36.502 [INF] CalculateResult: 20220805
21:02:36.502 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
21:06:35.422 [INF] RunScan: 0
21:06:35.422 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:6)","Data":null,"DataObj":null}
21:07:36.504 [INF] RunHandleWinLoss: 0
21:07:36.504 [INF] CalculateResult: 20220805
21:07:36.504 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
21:11:35.422 [INF] RunScan: 0
21:11:35.422 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:11)","Data":null,"DataObj":null}
21:12:36.506 [INF] RunHandleWinLoss: 0
21:12:36.506 [INF] CalculateResult: 20220805
21:12:36.506 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
21:16:35.422 [INF] RunScan: 0
21:16:35.422 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:16)","Data":null,"DataObj":null}
21:17:36.507 [INF] RunHandleWinLoss: 0
21:17:36.507 [INF] CalculateResult: 20220805
21:17:36.507 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
21:21:35.423 [INF] RunScan: 0
21:21:35.423 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:21)","Data":null,"DataObj":null}
21:22:36.509 [INF] RunHandleWinLoss: 0
21:22:36.509 [INF] CalculateResult: 20220805
21:22:36.509 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
21:26:35.423 [INF] RunScan: 0
21:26:35.423 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:26)","Data":null,"DataObj":null}
21:27:36.511 [INF] RunHandleWinLoss: 0
21:27:36.511 [INF] CalculateResult: 20220805
21:27:36.511 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
21:31:35.423 [INF] RunScan: 0
21:31:35.423 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:31)","Data":null,"DataObj":null}
21:32:36.515 [INF] RunHandleWinLoss: 0
21:32:36.515 [INF] CalculateResult: 20220805
21:32:36.515 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
21:36:35.423 [INF] RunScan: 0
21:36:35.423 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:36)","Data":null,"DataObj":null}
21:37:36.517 [INF] RunHandleWinLoss: 0
21:37:36.517 [INF] CalculateResult: 20220805
21:37:36.517 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
21:41:35.423 [INF] RunScan: 0
21:41:35.423 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:41)","Data":null,"DataObj":null}
21:42:36.518 [INF] RunHandleWinLoss: 0
21:42:36.518 [INF] CalculateResult: 20220805
21:42:36.518 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
21:46:35.424 [INF] RunScan: 0
21:46:35.424 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:46)","Data":null,"DataObj":null}
21:47:36.521 [INF] RunHandleWinLoss: 0
21:47:36.521 [INF] CalculateResult: 20220805
21:47:36.521 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
21:51:35.424 [INF] RunScan: 0
21:51:35.424 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:51)","Data":null,"DataObj":null}
21:52:36.523 [INF] RunHandleWinLoss: 0
21:52:36.523 [INF] CalculateResult: 20220805
21:52:36.523 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
21:56:35.424 [INF] RunScan: 0
21:56:35.424 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:56)","Data":null,"DataObj":null}
21:57:36.524 [INF] RunHandleWinLoss: 0
21:57:36.524 [INF] CalculateResult: 20220805
21:57:36.524 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
22:01:35.424 [INF] RunScan: 0
22:01:35.424 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:1)","Data":null,"DataObj":null}
22:02:36.528 [INF] RunHandleWinLoss: 0
22:02:36.528 [INF] CalculateResult: 20220805
22:02:36.528 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
22:06:35.424 [INF] RunScan: 0
22:06:35.425 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:6)","Data":null,"DataObj":null}
22:07:36.529 [INF] RunHandleWinLoss: 0
22:07:36.529 [INF] CalculateResult: 20220805
22:07:36.529 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
22:11:35.425 [INF] RunScan: 0
22:11:35.425 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:11)","Data":null,"DataObj":null}
22:12:36.535 [INF] RunHandleWinLoss: 0
22:12:36.535 [INF] CalculateResult: 20220805
22:12:36.535 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
22:16:35.425 [INF] RunScan: 0
22:16:35.425 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:16)","Data":null,"DataObj":null}
22:17:36.537 [INF] RunHandleWinLoss: 0
22:17:36.537 [INF] CalculateResult: 20220805
22:17:36.537 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
22:21:35.425 [INF] RunScan: 0
22:21:35.425 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:21)","Data":null,"DataObj":null}
22:22:36.538 [INF] RunHandleWinLoss: 0
22:22:36.538 [INF] CalculateResult: 20220805
22:22:36.538 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
22:26:35.425 [INF] RunScan: 0
22:26:35.425 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:26)","Data":null,"DataObj":null}
22:27:36.539 [INF] RunHandleWinLoss: 0
22:27:36.539 [INF] CalculateResult: 20220805
22:27:36.539 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
22:31:35.425 [INF] RunScan: 0
22:31:35.426 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:31)","Data":null,"DataObj":null}
22:32:36.541 [INF] RunHandleWinLoss: 0
22:32:36.541 [INF] CalculateResult: 20220805
22:32:36.541 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
22:36:35.426 [INF] RunScan: 0
22:36:35.426 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:36)","Data":null,"DataObj":null}
22:37:36.543 [INF] RunHandleWinLoss: 0
22:37:36.543 [INF] CalculateResult: 20220805
22:37:36.543 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
22:41:35.426 [INF] RunScan: 0
22:41:35.426 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:41)","Data":null,"DataObj":null}
22:42:36.544 [INF] RunHandleWinLoss: 0
22:42:36.544 [INF] CalculateResult: 20220805
22:42:36.544 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
22:46:35.427 [INF] RunScan: 0
22:46:35.428 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:46)","Data":null,"DataObj":null}
22:47:36.546 [INF] RunHandleWinLoss: 0
22:47:36.546 [INF] CalculateResult: 20220805
22:47:36.546 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
22:51:35.428 [INF] RunScan: 0
22:51:35.428 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:51)","Data":null,"DataObj":null}
22:52:36.547 [INF] RunHandleWinLoss: 0
22:52:36.547 [INF] CalculateResult: 20220805
22:52:36.547 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
22:56:35.428 [INF] RunScan: 0
22:56:35.428 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:56)","Data":null,"DataObj":null}
22:57:36.549 [INF] RunHandleWinLoss: 0
22:57:36.549 [INF] CalculateResult: 20220805
22:57:36.549 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
23:01:35.428 [INF] RunScan: 0
23:01:35.429 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:1)","Data":null,"DataObj":null}
23:02:36.551 [INF] RunHandleWinLoss: 0
23:02:36.551 [INF] CalculateResult: 20220805
23:02:36.551 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
23:06:35.429 [INF] RunScan: 0
23:06:35.429 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:6)","Data":null,"DataObj":null}
23:07:36.552 [INF] RunHandleWinLoss: 0
23:07:36.552 [INF] CalculateResult: 20220805
23:07:36.552 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
23:11:35.429 [INF] RunScan: 0
23:11:35.429 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:11)","Data":null,"DataObj":null}
23:12:36.554 [INF] RunHandleWinLoss: 0
23:12:36.554 [INF] CalculateResult: 20220805
23:12:36.554 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
23:16:35.429 [INF] RunScan: 0
23:16:35.429 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:16)","Data":null,"DataObj":null}
23:17:36.555 [INF] RunHandleWinLoss: 0
23:17:36.556 [INF] CalculateResult: 20220805
23:17:36.556 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
23:21:35.429 [INF] RunScan: 0
23:21:35.429 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:21)","Data":null,"DataObj":null}
23:22:36.557 [INF] RunHandleWinLoss: 0
23:22:36.557 [INF] CalculateResult: 20220805
23:22:36.557 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
23:26:35.429 [INF] RunScan: 0
23:26:35.430 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:26)","Data":null,"DataObj":null}
23:27:36.558 [INF] RunHandleWinLoss: 0
23:27:36.558 [INF] CalculateResult: 20220805
23:27:36.558 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
23:31:35.430 [INF] RunScan: 0
23:31:35.430 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:31)","Data":null,"DataObj":null}
23:32:36.560 [INF] RunHandleWinLoss: 0
23:32:36.560 [INF] CalculateResult: 20220805
23:32:36.560 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
23:36:35.430 [INF] RunScan: 0
23:36:35.430 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:36)","Data":null,"DataObj":null}
23:37:36.561 [INF] RunHandleWinLoss: 0
23:37:36.561 [INF] CalculateResult: 20220805
23:37:36.562 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
23:41:35.430 [INF] RunScan: 0
23:41:35.430 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:41)","Data":null,"DataObj":null}
23:42:36.563 [INF] RunHandleWinLoss: 0
23:42:36.563 [INF] CalculateResult: 20220805
23:42:36.563 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
23:46:35.430 [INF] RunScan: 0
23:46:35.430 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:46)","Data":null,"DataObj":null}
23:47:36.564 [INF] RunHandleWinLoss: 0
23:47:36.565 [INF] CalculateResult: 20220805
23:47:36.565 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
23:51:35.430 [INF] RunScan: 0
23:51:35.430 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:51)","Data":null,"DataObj":null}
23:52:36.566 [INF] RunHandleWinLoss: 0
23:52:36.566 [INF] CalculateResult: 20220805
23:52:36.566 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
23:56:35.431 [INF] RunScan: 0
23:56:35.431 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:56)","Data":null,"DataObj":null}
23:57:36.570 [INF] RunHandleWinLoss: 0
23:57:36.570 [INF] CalculateResult: 20220805
23:57:36.570 [INF] CalculateResult 2: select * from loto_request where Session=20220805 AND NOT Status='1'
