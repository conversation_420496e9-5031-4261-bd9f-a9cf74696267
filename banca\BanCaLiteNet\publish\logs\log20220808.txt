00:00:07.954 [INF] Logging new weekly leaderboard...
00:00:07.959 [INF] This week prize []
00:00:07.960 [INF] This week give 0 prize 
00:00:07.960 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"mupidolll7","Score":319093461,"Rank":0,"Prize":""}}
00:00:07.962 [INF] This week give 0 prize 
00:00:07.962 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"tfhrdghc33","Score":235509157,"Rank":1,"Prize":""}}
00:00:07.963 [INF] This week give 0 prize 
00:00:07.963 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"210kh6rhj1rb72","Score":222203792,"Rank":2,"Prize":""}}
00:00:07.964 [INF] This week give 0 prize 
00:00:07.964 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"<PERSON>name":"binzi1232","Score":198765385,"Rank":3,"Prize":""}}
00:00:07.965 [INF] This week give 0 prize 
00:00:07.965 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"hyhuuii95","Score":189572076,"Rank":4,"Prize":""}}
00:00:07.966 [INF] This week give 0 prize 
00:00:07.966 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"toeloeee36","Score":171487507,"Rank":5,"Prize":""}}
00:00:07.967 [INF] This week give 0 prize 
00:00:07.967 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"tranghai199840","Score":170954248,"Rank":6,"Prize":""}}
00:00:07.968 [INF] This week give 0 prize 
00:00:07.968 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"bancalob25","Score":167630199,"Rank":7,"Prize":""}}
00:00:07.969 [INF] This week give 0 prize 
00:00:07.969 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"hhhhhqqqq90","Score":166884920,"Rank":8,"Prize":""}}
00:00:07.970 [INF] This week give 0 prize 
00:00:07.970 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"zalobo8","Score":166180870,"Rank":9,"Prize":""}}
00:00:07.971 [INF] This week give 0 prize 
00:00:07.971 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"nxbcbbcbc82","Score":166048646,"Rank":10,"Prize":""}}
00:00:07.972 [INF] This week give 0 prize 
00:00:07.972 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"ttee9045","Score":165479218,"Rank":11,"Prize":""}}
00:00:07.973 [INF] This week give 0 prize 
00:00:07.973 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"fsdgfsdfasdf42","Score":162164848,"Rank":12,"Prize":""}}
00:00:07.974 [INF] This week give 0 prize 
00:00:07.974 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"tool79910","Score":161969396,"Rank":13,"Prize":""}}
00:00:07.975 [INF] This week give 0 prize 
00:00:07.975 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"heksjsjbj43","Score":161804330,"Rank":14,"Prize":""}}
00:00:07.976 [INF] This week give 0 prize 
00:00:07.976 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"xincaihumini21","Score":159139030,"Rank":15,"Prize":""}}
00:00:07.977 [INF] This week give 0 prize 
00:00:07.977 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"ncjjcjdjffj94","Score":158980722,"Rank":16,"Prize":""}}
00:00:07.978 [INF] This week give 0 prize 
00:00:07.978 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"jznnsjsj21","Score":155166789,"Rank":17,"Prize":""}}
00:00:07.979 [INF] This week give 0 prize 
00:00:07.979 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"090708569490","Score":154493442,"Rank":18,"Prize":""}}
00:00:07.980 [INF] This week give 0 prize 
00:00:07.980 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"choithu87","Score":153173532,"Rank":19,"Prize":""}}
00:00:07.981 [INF] This week give 0 prize 
00:00:07.981 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"cuibap66684","Score":152828290,"Rank":20,"Prize":""}}
00:00:07.982 [INF] This week give 0 prize 
00:00:07.982 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"baloxa38","Score":152586582,"Rank":21,"Prize":""}}
00:00:07.983 [INF] This week give 0 prize 
00:00:07.983 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"khanhnkhanh1","Score":152349942,"Rank":22,"Prize":""}}
00:00:07.984 [INF] This week give 0 prize 
00:00:07.984 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"yhbcchggghjj98","Score":152226415,"Rank":23,"Prize":""}}
00:00:07.985 [INF] This week give 0 prize 
00:00:07.985 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"muni2318","Score":152201200,"Rank":24,"Prize":""}}
00:00:07.986 [INF] This week give 0 prize 
00:00:07.986 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"hcbfhrhdmjvh11","Score":152063962,"Rank":25,"Prize":""}}
00:00:07.987 [INF] This week give 0 prize 
00:00:07.987 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"duyhoang2k160","Score":151430328,"Rank":26,"Prize":""}}
00:00:07.988 [INF] This week give 0 prize 
00:00:07.988 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"hahabahak67","Score":150130902,"Rank":27,"Prize":""}}
00:00:07.989 [INF] This week give 0 prize 
00:00:07.989 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"tuanhai39993","Score":149534230,"Rank":28,"Prize":""}}
00:00:07.990 [INF] This week give 0 prize 
00:00:07.990 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"dbdbdvdhh33","Score":149184680,"Rank":29,"Prize":""}}
00:00:07.991 [INF] This week give 0 prize 
00:00:07.991 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"huuuuuu8","Score":148907891,"Rank":30,"Prize":""}}
00:00:07.992 [INF] This week give 0 prize 
00:00:07.992 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"vzhzy89888","Score":148594928,"Rank":31,"Prize":""}}
00:00:07.993 [INF] This week give 0 prize 
00:00:07.993 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"nftgtnfntn1","Score":148561770,"Rank":32,"Prize":""}}
00:00:07.995 [INF] This week give 0 prize 
00:00:07.995 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"zaghkggx56","Score":148416805,"Rank":33,"Prize":""}}
00:00:07.996 [INF] This week give 0 prize 
00:00:07.996 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"daoktvn29","Score":125912030,"Rank":34,"Prize":""}}
00:00:07.997 [INF] This week give 0 prize 
00:00:07.997 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"mumu7886t93","Score":115100513,"Rank":35,"Prize":""}}
00:00:07.998 [INF] This week give 0 prize 
00:00:07.998 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"zetland70","Score":111427995,"Rank":36,"Prize":""}}
00:00:07.999 [INF] This week give 0 prize 
00:00:07.999 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"hgffyhfth43","Score":108895226,"Rank":37,"Prize":""}}
00:00:08.000 [INF] This week give 0 prize 
00:00:08.000 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"ssshhhhii15","Score":108029188,"Rank":38,"Prize":""}}
00:00:08.001 [INF] This week give 0 prize 
00:00:08.001 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"ducht198498","Score":103802469,"Rank":39,"Prize":""}}
00:00:08.002 [INF] This week give 0 prize 
00:00:08.002 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"heonakij16","Score":101621486,"Rank":40,"Prize":""}}
00:00:08.003 [INF] This week give 0 prize 
00:00:08.003 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"etgsdhgdsh68","Score":100694549,"Rank":41,"Prize":""}}
00:00:08.004 [INF] This week give 0 prize 
00:00:08.004 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"tyteddy222573","Score":100262702,"Rank":42,"Prize":""}}
00:00:08.005 [INF] This week give 0 prize 
00:00:08.005 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"cocofoocof50","Score":99548694,"Rank":43,"Prize":""}}
00:00:08.006 [INF] This week give 0 prize 
00:00:08.006 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"thtrh5h581","Score":94073269,"Rank":44,"Prize":""}}
00:00:08.007 [INF] This week give 0 prize 
00:00:08.007 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"mimi4459","Score":93821366,"Rank":45,"Prize":""}}
00:00:08.008 [INF] This week give 0 prize 
00:00:08.008 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"fgffggghh69","Score":93383750,"Rank":46,"Prize":""}}
00:00:08.009 [INF] This week give 0 prize 
00:00:08.009 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"sucjdkdkz32","Score":93142107,"Rank":47,"Prize":""}}
00:00:08.010 [INF] This week give 0 prize 
00:00:08.010 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"aabbbvvvv12384","Score":92710324,"Rank":48,"Prize":""}}
00:00:08.011 [INF] This week give 0 prize 
00:00:08.011 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"thooc1234","Score":90904237,"Rank":49,"Prize":""}}
00:00:08.012 [INF] This week give 0 prize 
00:00:08.012 [INF] Queue pending message 0 message {"type":2,"data":{"UserId":0,"Nickname":"hhdgdhhd19","Score":89853175,"Rank":50,"Prize":""}}
00:00:08.012 [INF] Logging new weekly leaderboard successfully
00:01:36.053 [INF] RunScan: 0
00:01:36.054 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:1)","Data":null,"DataObj":null}
00:02:37.571 [INF] RunHandleWinLoss: 0
00:02:37.571 [INF] CalculateResult: 20220808
00:02:37.571 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
00:06:36.054 [INF] RunScan: 0
00:06:36.054 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:6)","Data":null,"DataObj":null}
00:07:37.573 [INF] RunHandleWinLoss: 0
00:07:37.573 [INF] CalculateResult: 20220808
00:07:37.573 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
00:11:36.054 [INF] RunScan: 0
00:11:36.054 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:11)","Data":null,"DataObj":null}
00:12:37.575 [INF] RunHandleWinLoss: 0
00:12:37.575 [INF] CalculateResult: 20220808
00:12:37.575 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
00:16:36.054 [INF] RunScan: 0
00:16:36.054 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:16)","Data":null,"DataObj":null}
00:17:37.577 [INF] RunHandleWinLoss: 0
00:17:37.577 [INF] CalculateResult: 20220808
00:17:37.577 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
00:21:36.054 [INF] RunScan: 0
00:21:36.054 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:21)","Data":null,"DataObj":null}
00:22:37.578 [INF] RunHandleWinLoss: 0
00:22:37.578 [INF] CalculateResult: 20220808
00:22:37.578 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
00:26:36.055 [INF] RunScan: 0
00:26:36.055 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:26)","Data":null,"DataObj":null}
00:27:37.580 [INF] RunHandleWinLoss: 0
00:27:37.580 [INF] CalculateResult: 20220808
00:27:37.580 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
00:31:36.055 [INF] RunScan: 0
00:31:36.055 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:31)","Data":null,"DataObj":null}
00:32:37.581 [INF] RunHandleWinLoss: 0
00:32:37.581 [INF] CalculateResult: 20220808
00:32:37.581 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
00:36:36.055 [INF] RunScan: 0
00:36:36.055 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:36)","Data":null,"DataObj":null}
00:37:37.583 [INF] RunHandleWinLoss: 0
00:37:37.583 [INF] CalculateResult: 20220808
00:37:37.583 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
00:41:36.055 [INF] RunScan: 0
00:41:36.055 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:41)","Data":null,"DataObj":null}
00:42:37.584 [INF] RunHandleWinLoss: 0
00:42:37.584 [INF] CalculateResult: 20220808
00:42:37.584 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
00:46:36.056 [INF] RunScan: 0
00:46:36.056 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:46)","Data":null,"DataObj":null}
00:47:37.585 [INF] RunHandleWinLoss: 0
00:47:37.586 [INF] CalculateResult: 20220808
00:47:37.586 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
00:51:36.056 [INF] RunScan: 0
00:51:36.056 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:51)","Data":null,"DataObj":null}
00:52:37.587 [INF] RunHandleWinLoss: 0
00:52:37.587 [INF] CalculateResult: 20220808
00:52:37.587 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
00:56:36.056 [INF] RunScan: 0
00:56:36.056 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(0:56)","Data":null,"DataObj":null}
00:57:37.588 [INF] RunHandleWinLoss: 0
00:57:37.588 [INF] CalculateResult: 20220808
00:57:37.588 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
01:01:36.056 [INF] RunScan: 0
01:01:36.056 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:1)","Data":null,"DataObj":null}
01:02:37.590 [INF] RunHandleWinLoss: 0
01:02:37.590 [INF] CalculateResult: 20220808
01:02:37.590 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
01:06:36.056 [INF] RunScan: 0
01:06:36.056 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:6)","Data":null,"DataObj":null}
01:07:37.591 [INF] RunHandleWinLoss: 0
01:07:37.592 [INF] CalculateResult: 20220808
01:07:37.592 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
01:11:36.057 [INF] RunScan: 0
01:11:36.057 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:11)","Data":null,"DataObj":null}
01:12:37.593 [INF] RunHandleWinLoss: 0
01:12:37.593 [INF] CalculateResult: 20220808
01:12:37.593 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
01:16:36.057 [INF] RunScan: 0
01:16:36.057 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:16)","Data":null,"DataObj":null}
01:17:37.596 [INF] RunHandleWinLoss: 0
01:17:37.596 [INF] CalculateResult: 20220808
01:17:37.596 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
01:21:36.057 [INF] RunScan: 0
01:21:36.057 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:21)","Data":null,"DataObj":null}
01:22:37.597 [INF] RunHandleWinLoss: 0
01:22:37.597 [INF] CalculateResult: 20220808
01:22:37.597 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
01:26:36.057 [INF] RunScan: 0
01:26:36.057 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:26)","Data":null,"DataObj":null}
01:27:37.598 [INF] RunHandleWinLoss: 0
01:27:37.599 [INF] CalculateResult: 20220808
01:27:37.599 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
01:31:36.057 [INF] RunScan: 0
01:31:36.057 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:31)","Data":null,"DataObj":null}
01:32:37.600 [INF] RunHandleWinLoss: 0
01:32:37.600 [INF] CalculateResult: 20220808
01:32:37.600 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
01:36:36.058 [INF] RunScan: 0
01:36:36.058 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:36)","Data":null,"DataObj":null}
01:37:37.602 [INF] RunHandleWinLoss: 0
01:37:37.602 [INF] CalculateResult: 20220808
01:37:37.602 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
01:41:36.058 [INF] RunScan: 0
01:41:36.058 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:41)","Data":null,"DataObj":null}
01:42:37.603 [INF] RunHandleWinLoss: 0
01:42:37.603 [INF] CalculateResult: 20220808
01:42:37.603 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
01:46:36.058 [INF] RunScan: 0
01:46:36.058 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:46)","Data":null,"DataObj":null}
01:47:37.605 [INF] RunHandleWinLoss: 0
01:47:37.605 [INF] CalculateResult: 20220808
01:47:37.605 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
01:51:36.058 [INF] RunScan: 0
01:51:36.058 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:51)","Data":null,"DataObj":null}
01:52:37.606 [INF] RunHandleWinLoss: 0
01:52:37.606 [INF] CalculateResult: 20220808
01:52:37.606 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
01:56:36.059 [INF] RunScan: 0
01:56:36.059 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(1:56)","Data":null,"DataObj":null}
01:57:37.607 [INF] RunHandleWinLoss: 0
01:57:37.607 [INF] CalculateResult: 20220808
01:57:37.607 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
02:01:36.059 [INF] RunScan: 0
02:01:36.059 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:1)","Data":null,"DataObj":null}
02:02:37.609 [INF] RunHandleWinLoss: 0
02:02:37.609 [INF] CalculateResult: 20220808
02:02:37.609 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
02:02:41.695 [DBG] Client connected from 192.241.208.180:32904
no ex
02:02:41.695 [DBG] 26 bytes read
no ex
02:02:51.695 [DBG] 0 bytes read. Closing.
no ex
02:06:36.059 [INF] RunScan: 0
02:06:36.059 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:6)","Data":null,"DataObj":null}
02:07:37.611 [INF] RunHandleWinLoss: 0
02:07:37.611 [INF] CalculateResult: 20220808
02:07:37.611 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
02:11:36.059 [INF] RunScan: 0
02:11:36.060 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:11)","Data":null,"DataObj":null}
02:12:37.612 [INF] RunHandleWinLoss: 0
02:12:37.612 [INF] CalculateResult: 20220808
02:12:37.612 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
02:16:36.060 [INF] RunScan: 0
02:16:36.060 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:16)","Data":null,"DataObj":null}
02:17:37.614 [INF] RunHandleWinLoss: 0
02:17:37.614 [INF] CalculateResult: 20220808
02:17:37.614 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
02:21:36.061 [INF] RunScan: 0
02:21:36.061 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:21)","Data":null,"DataObj":null}
02:22:37.615 [INF] RunHandleWinLoss: 0
02:22:37.615 [INF] CalculateResult: 20220808
02:22:37.615 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
02:26:36.061 [INF] RunScan: 0
02:26:36.061 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:26)","Data":null,"DataObj":null}
02:27:37.617 [INF] RunHandleWinLoss: 0
02:27:37.617 [INF] CalculateResult: 20220808
02:27:37.617 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
02:31:36.062 [INF] RunScan: 0
02:31:36.063 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:31)","Data":null,"DataObj":null}
02:32:37.618 [INF] RunHandleWinLoss: 0
02:32:37.618 [INF] CalculateResult: 20220808
02:32:37.618 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
02:36:36.063 [INF] RunScan: 0
02:36:36.063 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:36)","Data":null,"DataObj":null}
02:37:37.619 [INF] RunHandleWinLoss: 0
02:37:37.620 [INF] CalculateResult: 20220808
02:37:37.620 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
02:41:36.063 [INF] RunScan: 0
02:41:36.063 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:41)","Data":null,"DataObj":null}
02:42:37.621 [INF] RunHandleWinLoss: 0
02:42:37.621 [INF] CalculateResult: 20220808
02:42:37.621 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
02:46:36.064 [INF] RunScan: 0
02:46:36.064 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:46)","Data":null,"DataObj":null}
02:47:37.624 [INF] RunHandleWinLoss: 0
02:47:37.624 [INF] CalculateResult: 20220808
02:47:37.624 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
02:51:36.064 [INF] RunScan: 0
02:51:36.064 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:51)","Data":null,"DataObj":null}
02:52:37.626 [INF] RunHandleWinLoss: 0
02:52:37.626 [INF] CalculateResult: 20220808
02:52:37.626 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
02:56:36.064 [INF] RunScan: 0
02:56:36.064 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(2:56)","Data":null,"DataObj":null}
02:57:37.627 [INF] RunHandleWinLoss: 0
02:57:37.627 [INF] CalculateResult: 20220808
02:57:37.627 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
03:01:36.064 [INF] RunScan: 0
03:01:36.064 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:1)","Data":null,"DataObj":null}
03:02:37.628 [INF] RunHandleWinLoss: 0
03:02:37.629 [INF] CalculateResult: 20220808
03:02:37.629 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
03:06:36.064 [INF] RunScan: 0
03:06:36.064 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:6)","Data":null,"DataObj":null}
03:07:37.630 [INF] RunHandleWinLoss: 0
03:07:37.630 [INF] CalculateResult: 20220808
03:07:37.630 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
03:11:36.065 [INF] RunScan: 0
03:11:36.065 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:11)","Data":null,"DataObj":null}
03:12:37.631 [INF] RunHandleWinLoss: 0
03:12:37.631 [INF] CalculateResult: 20220808
03:12:37.631 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
03:16:36.065 [INF] RunScan: 0
03:16:36.065 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:16)","Data":null,"DataObj":null}
03:17:37.633 [INF] RunHandleWinLoss: 0
03:17:37.633 [INF] CalculateResult: 20220808
03:17:37.633 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
03:21:36.065 [INF] RunScan: 0
03:21:36.065 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:21)","Data":null,"DataObj":null}
03:22:37.636 [INF] RunHandleWinLoss: 0
03:22:37.636 [INF] CalculateResult: 20220808
03:22:37.636 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
03:26:36.065 [INF] RunScan: 0
03:26:36.065 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:26)","Data":null,"DataObj":null}
03:27:37.637 [INF] RunHandleWinLoss: 0
03:27:37.637 [INF] CalculateResult: 20220808
03:27:37.637 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
03:31:36.066 [INF] RunScan: 0
03:31:36.066 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:31)","Data":null,"DataObj":null}
03:32:37.638 [INF] RunHandleWinLoss: 0
03:32:37.638 [INF] CalculateResult: 20220808
03:32:37.638 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
03:36:36.066 [INF] RunScan: 0
03:36:36.066 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:36)","Data":null,"DataObj":null}
03:37:37.640 [INF] RunHandleWinLoss: 0
03:37:37.640 [INF] CalculateResult: 20220808
03:37:37.640 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
03:41:36.066 [INF] RunScan: 0
03:41:36.066 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:41)","Data":null,"DataObj":null}
03:42:37.641 [INF] RunHandleWinLoss: 0
03:42:37.641 [INF] CalculateResult: 20220808
03:42:37.641 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
03:46:36.066 [INF] RunScan: 0
03:46:36.066 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:46)","Data":null,"DataObj":null}
03:47:37.642 [INF] RunHandleWinLoss: 0
03:47:37.643 [INF] CalculateResult: 20220808
03:47:37.643 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
03:51:36.066 [INF] RunScan: 0
03:51:36.066 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:51)","Data":null,"DataObj":null}
03:52:37.644 [INF] RunHandleWinLoss: 0
03:52:37.644 [INF] CalculateResult: 20220808
03:52:37.644 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
03:56:36.067 [INF] RunScan: 0
03:56:36.067 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(3:56)","Data":null,"DataObj":null}
03:57:37.645 [INF] RunHandleWinLoss: 0
03:57:37.645 [INF] CalculateResult: 20220808
03:57:37.645 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
04:01:36.067 [INF] RunScan: 0
04:01:36.067 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:1)","Data":null,"DataObj":null}
04:02:37.647 [INF] RunHandleWinLoss: 0
04:02:37.647 [INF] CalculateResult: 20220808
04:02:37.647 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
04:06:36.067 [INF] RunScan: 0
04:06:36.067 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:6)","Data":null,"DataObj":null}
04:07:37.649 [INF] RunHandleWinLoss: 0
04:07:37.649 [INF] CalculateResult: 20220808
04:07:37.649 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
04:11:36.067 [INF] RunScan: 0
04:11:36.069 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:11)","Data":null,"DataObj":null}
04:12:37.651 [INF] RunHandleWinLoss: 0
04:12:37.651 [INF] CalculateResult: 20220808
04:12:37.651 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
04:16:36.069 [INF] RunScan: 0
04:16:36.069 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:16)","Data":null,"DataObj":null}
04:17:37.652 [INF] RunHandleWinLoss: 0
04:17:37.653 [INF] CalculateResult: 20220808
04:17:37.653 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
04:21:36.069 [INF] RunScan: 0
04:21:36.069 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:21)","Data":null,"DataObj":null}
04:22:37.654 [INF] RunHandleWinLoss: 0
04:22:37.654 [INF] CalculateResult: 20220808
04:22:37.654 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
04:26:36.069 [INF] RunScan: 0
04:26:36.069 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:26)","Data":null,"DataObj":null}
04:27:37.655 [INF] RunHandleWinLoss: 0
04:27:37.655 [INF] CalculateResult: 20220808
04:27:37.656 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
04:31:36.069 [INF] RunScan: 0
04:31:36.069 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:31)","Data":null,"DataObj":null}
04:32:37.657 [INF] RunHandleWinLoss: 0
04:32:37.657 [INF] CalculateResult: 20220808
04:32:37.657 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
04:36:36.069 [INF] RunScan: 0
04:36:36.071 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:36)","Data":null,"DataObj":null}
04:37:37.658 [INF] RunHandleWinLoss: 0
04:37:37.658 [INF] CalculateResult: 20220808
04:37:37.658 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
04:41:36.071 [INF] RunScan: 0
04:41:36.071 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:41)","Data":null,"DataObj":null}
04:42:37.660 [INF] RunHandleWinLoss: 0
04:42:37.660 [INF] CalculateResult: 20220808
04:42:37.660 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
04:46:36.071 [INF] RunScan: 0
04:46:36.071 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:46)","Data":null,"DataObj":null}
04:47:37.661 [INF] RunHandleWinLoss: 0
04:47:37.661 [INF] CalculateResult: 20220808
04:47:37.661 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
04:51:36.071 [INF] RunScan: 0
04:51:36.072 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:51)","Data":null,"DataObj":null}
04:52:37.662 [INF] RunHandleWinLoss: 0
04:52:37.662 [INF] CalculateResult: 20220808
04:52:37.662 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
04:56:36.072 [INF] RunScan: 0
04:56:36.072 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(4:56)","Data":null,"DataObj":null}
04:57:37.663 [INF] RunHandleWinLoss: 0
04:57:37.663 [INF] CalculateResult: 20220808
04:57:37.664 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
05:01:36.072 [INF] RunScan: 0
05:01:36.072 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:1)","Data":null,"DataObj":null}
05:02:37.665 [INF] RunHandleWinLoss: 0
05:02:37.665 [INF] CalculateResult: 20220808
05:02:37.665 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
05:06:36.072 [INF] RunScan: 0
05:06:36.072 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:6)","Data":null,"DataObj":null}
05:07:37.666 [INF] RunHandleWinLoss: 0
05:07:37.666 [INF] CalculateResult: 20220808
05:07:37.666 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
05:11:36.072 [INF] RunScan: 0
05:11:36.072 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:11)","Data":null,"DataObj":null}
05:12:37.667 [INF] RunHandleWinLoss: 0
05:12:37.668 [INF] CalculateResult: 20220808
05:12:37.668 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
05:16:36.073 [INF] RunScan: 0
05:16:36.073 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:16)","Data":null,"DataObj":null}
05:17:37.669 [INF] RunHandleWinLoss: 0
05:17:37.669 [INF] CalculateResult: 20220808
05:17:37.669 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
05:21:36.073 [INF] RunScan: 0
05:21:36.073 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:21)","Data":null,"DataObj":null}
05:22:37.670 [INF] RunHandleWinLoss: 0
05:22:37.670 [INF] CalculateResult: 20220808
05:22:37.670 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
05:26:36.073 [INF] RunScan: 0
05:26:36.073 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:26)","Data":null,"DataObj":null}
05:27:37.672 [INF] RunHandleWinLoss: 0
05:27:37.672 [INF] CalculateResult: 20220808
05:27:37.672 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
05:31:36.073 [INF] RunScan: 0
05:31:36.073 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:31)","Data":null,"DataObj":null}
05:32:37.674 [INF] RunHandleWinLoss: 0
05:32:37.674 [INF] CalculateResult: 20220808
05:32:37.674 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
05:36:36.073 [INF] RunScan: 0
05:36:36.073 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:36)","Data":null,"DataObj":null}
05:37:37.675 [INF] RunHandleWinLoss: 0
05:37:37.675 [INF] CalculateResult: 20220808
05:37:37.675 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
05:41:36.074 [INF] RunScan: 0
05:41:36.074 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:41)","Data":null,"DataObj":null}
05:42:37.676 [INF] RunHandleWinLoss: 0
05:42:37.676 [INF] CalculateResult: 20220808
05:42:37.676 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
05:46:36.074 [INF] RunScan: 0
05:46:36.074 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:46)","Data":null,"DataObj":null}
05:47:37.677 [INF] RunHandleWinLoss: 0
05:47:37.677 [INF] CalculateResult: 20220808
05:47:37.677 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
05:51:36.074 [INF] RunScan: 0
05:51:36.074 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:51)","Data":null,"DataObj":null}
05:52:37.679 [INF] RunHandleWinLoss: 0
05:52:37.679 [INF] CalculateResult: 20220808
05:52:37.679 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
05:56:36.074 [INF] RunScan: 0
05:56:36.074 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(5:56)","Data":null,"DataObj":null}
05:57:37.680 [INF] RunHandleWinLoss: 0
05:57:37.680 [INF] CalculateResult: 20220808
05:57:37.680 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
06:01:36.075 [INF] RunScan: 0
06:01:36.075 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:1)","Data":null,"DataObj":null}
06:02:37.681 [INF] RunHandleWinLoss: 0
06:02:37.681 [INF] CalculateResult: 20220808
06:02:37.681 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
06:06:36.077 [INF] RunScan: 0
06:06:36.077 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:6)","Data":null,"DataObj":null}
06:07:37.683 [INF] RunHandleWinLoss: 0
06:07:37.683 [INF] CalculateResult: 20220808
06:07:37.683 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
06:11:36.077 [INF] RunScan: 0
06:11:36.078 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:11)","Data":null,"DataObj":null}
06:12:37.684 [INF] RunHandleWinLoss: 0
06:12:37.684 [INF] CalculateResult: 20220808
06:12:37.684 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
06:16:36.078 [INF] RunScan: 0
06:16:36.078 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:16)","Data":null,"DataObj":null}
06:17:37.686 [INF] RunHandleWinLoss: 0
06:17:37.686 [INF] CalculateResult: 20220808
06:17:37.686 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
06:21:36.078 [INF] RunScan: 0
06:21:36.078 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:21)","Data":null,"DataObj":null}
06:22:37.687 [INF] RunHandleWinLoss: 0
06:22:37.688 [INF] CalculateResult: 20220808
06:22:37.688 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
06:26:36.078 [INF] RunScan: 0
06:26:36.078 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:26)","Data":null,"DataObj":null}
06:27:37.690 [INF] RunHandleWinLoss: 0
06:27:37.690 [INF] CalculateResult: 20220808
06:27:37.690 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
06:31:36.078 [INF] RunScan: 0
06:31:36.079 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:31)","Data":null,"DataObj":null}
06:32:37.694 [INF] RunHandleWinLoss: 0
06:32:37.694 [INF] CalculateResult: 20220808
06:32:37.694 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
06:36:36.079 [INF] RunScan: 0
06:36:36.079 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:36)","Data":null,"DataObj":null}
06:37:37.695 [INF] RunHandleWinLoss: 0
06:37:37.695 [INF] CalculateResult: 20220808
06:37:37.696 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
06:41:36.079 [INF] RunScan: 0
06:41:36.079 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:41)","Data":null,"DataObj":null}
06:42:37.698 [INF] RunHandleWinLoss: 0
06:42:37.698 [INF] CalculateResult: 20220808
06:42:37.699 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
06:46:36.079 [INF] RunScan: 0
06:46:36.079 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:46)","Data":null,"DataObj":null}
06:47:37.700 [INF] RunHandleWinLoss: 0
06:47:37.700 [INF] CalculateResult: 20220808
06:47:37.700 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
06:51:36.079 [INF] RunScan: 0
06:51:36.079 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:51)","Data":null,"DataObj":null}
06:52:37.702 [INF] RunHandleWinLoss: 0
06:52:37.702 [INF] CalculateResult: 20220808
06:52:37.702 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
06:56:36.080 [INF] RunScan: 0
06:56:36.080 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(6:56)","Data":null,"DataObj":null}
06:57:37.703 [INF] RunHandleWinLoss: 0
06:57:37.703 [INF] CalculateResult: 20220808
06:57:37.703 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
07:01:36.080 [INF] RunScan: 0
07:01:36.080 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:1)","Data":null,"DataObj":null}
07:02:37.705 [INF] RunHandleWinLoss: 0
07:02:37.705 [INF] CalculateResult: 20220808
07:02:37.705 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
07:06:36.080 [INF] RunScan: 0
07:06:36.080 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:6)","Data":null,"DataObj":null}
07:07:37.707 [INF] RunHandleWinLoss: 0
07:07:37.707 [INF] CalculateResult: 20220808
07:07:37.707 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
07:11:36.081 [INF] RunScan: 0
07:11:36.081 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:11)","Data":null,"DataObj":null}
07:12:37.708 [INF] RunHandleWinLoss: 0
07:12:37.708 [INF] CalculateResult: 20220808
07:12:37.708 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
07:16:36.081 [INF] RunScan: 0
07:16:36.081 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:16)","Data":null,"DataObj":null}
07:17:37.710 [INF] RunHandleWinLoss: 0
07:17:37.710 [INF] CalculateResult: 20220808
07:17:37.710 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
07:21:36.081 [INF] RunScan: 0
07:21:36.081 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:21)","Data":null,"DataObj":null}
07:22:37.711 [INF] RunHandleWinLoss: 0
07:22:37.711 [INF] CalculateResult: 20220808
07:22:37.711 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
07:26:36.081 [INF] RunScan: 0
07:26:36.082 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:26)","Data":null,"DataObj":null}
07:27:37.713 [INF] RunHandleWinLoss: 0
07:27:37.713 [INF] CalculateResult: 20220808
07:27:37.713 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
07:31:36.082 [INF] RunScan: 0
07:31:36.082 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:31)","Data":null,"DataObj":null}
07:32:37.714 [INF] RunHandleWinLoss: 0
07:32:37.714 [INF] CalculateResult: 20220808
07:32:37.714 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
07:36:36.082 [INF] RunScan: 0
07:36:36.082 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:36)","Data":null,"DataObj":null}
07:37:37.716 [INF] RunHandleWinLoss: 0
07:37:37.716 [INF] CalculateResult: 20220808
07:37:37.716 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
07:41:36.082 [INF] RunScan: 0
07:41:36.082 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:41)","Data":null,"DataObj":null}
07:42:37.717 [INF] RunHandleWinLoss: 0
07:42:37.717 [INF] CalculateResult: 20220808
07:42:37.717 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
07:46:36.082 [INF] RunScan: 0
07:46:36.083 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:46)","Data":null,"DataObj":null}
07:47:37.719 [INF] RunHandleWinLoss: 0
07:47:37.719 [INF] CalculateResult: 20220808
07:47:37.719 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
07:51:36.083 [INF] RunScan: 0
07:51:36.083 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:51)","Data":null,"DataObj":null}
07:52:37.720 [INF] RunHandleWinLoss: 0
07:52:37.720 [INF] CalculateResult: 20220808
07:52:37.720 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
07:56:36.083 [INF] RunScan: 0
07:56:36.083 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(7:56)","Data":null,"DataObj":null}
07:57:37.722 [INF] RunHandleWinLoss: 0
07:57:37.722 [INF] CalculateResult: 20220808
07:57:37.722 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
08:01:36.084 [INF] RunScan: 0
08:01:36.084 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:1)","Data":null,"DataObj":null}
08:02:37.723 [INF] RunHandleWinLoss: 0
08:02:37.723 [INF] CalculateResult: 20220808
08:02:37.723 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
08:06:36.084 [INF] RunScan: 0
08:06:36.084 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:6)","Data":null,"DataObj":null}
08:07:37.725 [INF] RunHandleWinLoss: 0
08:07:37.725 [INF] CalculateResult: 20220808
08:07:37.725 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
08:11:36.084 [INF] RunScan: 0
08:11:36.084 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:11)","Data":null,"DataObj":null}
08:12:37.726 [INF] RunHandleWinLoss: 0
08:12:37.726 [INF] CalculateResult: 20220808
08:12:37.726 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
08:16:36.084 [INF] RunScan: 0
08:16:36.084 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:16)","Data":null,"DataObj":null}
08:17:37.727 [INF] RunHandleWinLoss: 0
08:17:37.727 [INF] CalculateResult: 20220808
08:17:37.727 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
08:21:36.084 [INF] RunScan: 0
08:21:36.085 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:21)","Data":null,"DataObj":null}
08:22:37.729 [INF] RunHandleWinLoss: 0
08:22:37.729 [INF] CalculateResult: 20220808
08:22:37.729 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
08:26:36.085 [INF] RunScan: 0
08:26:36.085 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:26)","Data":null,"DataObj":null}
08:27:37.730 [INF] RunHandleWinLoss: 0
08:27:37.730 [INF] CalculateResult: 20220808
08:27:37.730 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
08:31:36.085 [INF] RunScan: 0
08:31:36.085 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:31)","Data":null,"DataObj":null}
08:32:37.731 [INF] RunHandleWinLoss: 0
08:32:37.731 [INF] CalculateResult: 20220808
08:32:37.731 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
08:36:36.085 [INF] RunScan: 0
08:36:36.085 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:36)","Data":null,"DataObj":null}
08:37:37.733 [INF] RunHandleWinLoss: 0
08:37:37.733 [INF] CalculateResult: 20220808
08:37:37.733 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
08:41:36.085 [INF] RunScan: 0
08:41:36.086 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:41)","Data":null,"DataObj":null}
08:42:37.734 [INF] RunHandleWinLoss: 0
08:42:37.735 [INF] CalculateResult: 20220808
08:42:37.735 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
08:46:36.086 [INF] RunScan: 0
08:46:36.086 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:46)","Data":null,"DataObj":null}
08:47:37.736 [INF] RunHandleWinLoss: 0
08:47:37.736 [INF] CalculateResult: 20220808
08:47:37.736 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
08:51:36.086 [INF] RunScan: 0
08:51:36.086 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:51)","Data":null,"DataObj":null}
08:52:37.738 [INF] RunHandleWinLoss: 0
08:52:37.738 [INF] CalculateResult: 20220808
08:52:37.738 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
08:56:36.087 [INF] RunScan: 0
08:56:36.087 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(8:56)","Data":null,"DataObj":null}
08:57:37.739 [INF] RunHandleWinLoss: 0
08:57:37.739 [INF] CalculateResult: 20220808
08:57:37.739 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
09:01:36.087 [INF] RunScan: 0
09:01:36.087 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:1)","Data":null,"DataObj":null}
09:02:37.741 [INF] RunHandleWinLoss: 0
09:02:37.741 [INF] CalculateResult: 20220808
09:02:37.741 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
09:06:36.088 [INF] RunScan: 0
09:06:36.088 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:6)","Data":null,"DataObj":null}
09:07:37.743 [INF] RunHandleWinLoss: 0
09:07:37.743 [INF] CalculateResult: 20220808
09:07:37.743 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
09:10:32.093 [DBG] Client connected from 127.0.0.1:44742
no ex
09:10:32.093 [DBG] 886 bytes read
no ex
09:10:32.093 [DBG] Building Hybi-14 Response
no ex
09:10:32.093 [DBG] Sent 129 bytes
no ex
09:10:33.523 [DBG] Sent 184 bytes
no ex
09:10:37.119 [DBG] Sent 4 bytes
no ex
09:10:37.119 [DBG] Swallowing ObjectDisposedException
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
09:10:41.516 [DBG] Client connected from 127.0.0.1:44754
no ex
09:10:41.516 [DBG] 886 bytes read
no ex
09:10:41.516 [DBG] Building Hybi-14 Response
no ex
09:10:41.516 [DBG] Sent 129 bytes
no ex
09:10:41.750 [DBG] 31 bytes read
no ex
09:10:41.751 [DBG] Sent 30 bytes
no ex
09:10:43.522 [DBG] Sent 184 bytes
no ex
09:10:44.789 [DBG] 31 bytes read
no ex
09:10:44.789 [DBG] Sent 30 bytes
no ex
09:10:47.735 [DBG] 31 bytes read
no ex
09:10:47.736 [DBG] Sent 30 bytes
no ex
09:10:48.527 [DBG] Sent 184 bytes
no ex
09:10:49.772 [INF] 8888 bot: 2, peer: 1, ccu: 0
09:10:50.726 [DBG] 31 bytes read
no ex
09:10:50.726 [DBG] Sent 30 bytes
no ex
09:10:53.525 [DBG] Sent 184 bytes
no ex
09:10:53.737 [DBG] 31 bytes read
no ex
09:10:53.738 [DBG] Sent 30 bytes
no ex
09:10:56.775 [DBG] 31 bytes read
no ex
09:10:56.775 [DBG] Sent 30 bytes
no ex
09:10:58.512 [DBG] Sent 184 bytes
no ex
09:10:59.725 [DBG] 31 bytes read
no ex
09:10:59.725 [DBG] Sent 30 bytes
no ex
09:11:02.738 [DBG] 31 bytes read
no ex
09:11:02.738 [DBG] Sent 30 bytes
no ex
09:11:03.527 [DBG] Sent 184 bytes
no ex
09:11:05.756 [DBG] 31 bytes read
no ex
09:11:05.756 [DBG] Sent 30 bytes
no ex
09:11:08.519 [DBG] Sent 184 bytes
no ex
09:11:10.101 [DBG] Client connected from 127.0.0.1:44790
no ex
09:11:10.101 [DBG] 838 bytes read
no ex
09:11:10.101 [DBG] Building Hybi-14 Response
no ex
09:11:10.102 [DBG] Sent 129 bytes
no ex
09:11:10.291 [DBG] 31 bytes read
no ex
09:11:10.291 [DBG] Sent 30 bytes
no ex
09:11:10.774 [DBG] Sent 4 bytes
no ex
09:11:10.775 [DBG] Swallowing ObjectDisposedException
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
09:11:13.305 [DBG] 31 bytes read
no ex
09:11:13.305 [DBG] Sent 30 bytes
no ex
09:11:13.517 [DBG] Sent 184 bytes
no ex
09:11:16.311 [DBG] 31 bytes read
no ex
09:11:16.311 [DBG] Sent 30 bytes
no ex
09:11:18.516 [DBG] Sent 184 bytes
no ex
09:11:19.311 [DBG] 31 bytes read
no ex
09:11:19.311 [DBG] Sent 30 bytes
no ex
09:11:22.315 [DBG] 31 bytes read
no ex
09:11:22.315 [DBG] Sent 30 bytes
no ex
09:11:23.526 [DBG] Sent 184 bytes
no ex
09:11:25.319 [DBG] 31 bytes read
no ex
09:11:25.319 [DBG] Sent 30 bytes
no ex
09:11:28.313 [DBG] 31 bytes read
no ex
09:11:28.313 [DBG] Sent 30 bytes
no ex
09:11:28.526 [DBG] Sent 184 bytes
no ex
09:11:30.594 [DBG] 47 bytes read
no ex
09:11:30.595 [DBG] Sent 25 bytes
no ex
09:11:31.347 [DBG] 31 bytes read
no ex
09:11:31.348 [DBG] Sent 30 bytes
no ex
09:11:33.527 [DBG] Sent 184 bytes
no ex
09:11:34.322 [DBG] 31 bytes read
no ex
09:11:34.322 [DBG] Sent 30 bytes
no ex
09:11:36.088 [INF] RunScan: 0
09:11:36.088 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:11)","Data":null,"DataObj":null}
09:11:36.688 [DBG] 47 bytes read
no ex
09:11:36.689 [DBG] Sent 25 bytes
no ex
09:11:36.715 [DBG] 47 bytes read
no ex
09:11:36.716 [DBG] Sent 25 bytes
no ex
09:11:38.530 [DBG] Sent 184 bytes
no ex
09:11:38.886 [DBG] 47 bytes read
no ex
09:11:38.886 [DBG] Sent 25 bytes
no ex
09:11:39.086 [DBG] 31 bytes read
no ex
09:11:39.087 [DBG] Sent 30 bytes
no ex
09:11:40.311 [DBG] 31 bytes read
no ex
09:11:40.312 [DBG] Sent 30 bytes
no ex
09:11:43.483 [DBG] 31 bytes read
no ex
09:11:43.483 [DBG] Sent 30 bytes
no ex
09:11:43.519 [DBG] Sent 184 bytes
no ex
09:11:46.421 [DBG] 31 bytes read
no ex
09:11:46.421 [DBG] Sent 30 bytes
no ex
09:11:48.520 [DBG] Sent 184 bytes
no ex
09:11:49.414 [DBG] 31 bytes read
no ex
09:11:49.415 [DBG] Sent 30 bytes
no ex
09:11:52.366 [DBG] 31 bytes read
no ex
09:11:52.366 [DBG] Sent 30 bytes
no ex
09:11:53.534 [DBG] Sent 184 bytes
no ex
09:11:55.422 [DBG] 31 bytes read
no ex
09:11:55.423 [DBG] Sent 30 bytes
no ex
09:11:58.523 [DBG] 31 bytes read
no ex
09:11:58.523 [DBG] Sent 30 bytes
no ex
09:11:58.526 [DBG] Sent 184 bytes
no ex
09:12:01.422 [DBG] 31 bytes read
no ex
09:12:01.422 [DBG] Sent 30 bytes
no ex
09:12:03.521 [DBG] Sent 184 bytes
no ex
09:12:04.428 [DBG] 31 bytes read
no ex
09:12:04.428 [DBG] Sent 30 bytes
no ex
09:12:07.390 [DBG] 31 bytes read
no ex
09:12:07.390 [DBG] Sent 30 bytes
no ex
09:12:08.522 [DBG] Sent 184 bytes
no ex
09:12:10.297 [DBG] 47 bytes read
no ex
09:12:10.297 [DBG] Sent 25 bytes
no ex
09:12:10.453 [DBG] 31 bytes read
no ex
09:12:10.454 [DBG] Sent 30 bytes
no ex
09:12:13.436 [DBG] 31 bytes read
no ex
09:12:13.437 [DBG] Sent 30 bytes
no ex
09:12:13.530 [DBG] Sent 184 bytes
no ex
09:12:15.520 [DBG] 47 bytes read
no ex
09:12:15.520 [DBG] Sent 25 bytes
no ex
09:12:16.322 [DBG] 31 bytes read
no ex
09:12:16.322 [DBG] Sent 30 bytes
no ex
09:12:18.539 [DBG] Sent 184 bytes
no ex
09:12:19.388 [DBG] 31 bytes read
no ex
09:12:19.389 [DBG] Sent 30 bytes
no ex
09:12:22.319 [DBG] 31 bytes read
no ex
09:12:22.319 [DBG] Sent 30 bytes
no ex
09:12:23.525 [DBG] Sent 184 bytes
no ex
09:12:25.330 [DBG] 31 bytes read
no ex
09:12:25.332 [DBG] Sent 30 bytes
no ex
09:12:28.334 [DBG] 31 bytes read
no ex
09:12:28.334 [DBG] Sent 30 bytes
no ex
09:12:28.525 [DBG] Sent 184 bytes
no ex
09:12:31.315 [DBG] 31 bytes read
no ex
09:12:31.315 [DBG] Sent 30 bytes
no ex
09:12:33.541 [DBG] Sent 184 bytes
no ex
09:12:34.323 [DBG] 31 bytes read
no ex
09:12:34.323 [DBG] Sent 30 bytes
no ex
09:12:37.307 [DBG] 31 bytes read
no ex
09:12:37.307 [DBG] Sent 30 bytes
no ex
09:12:37.744 [INF] RunHandleWinLoss: 0
09:12:37.744 [INF] CalculateResult: 20220808
09:12:37.744 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
09:12:38.531 [DBG] Sent 184 bytes
no ex
09:12:40.299 [DBG] 31 bytes read
no ex
09:12:40.299 [DBG] Sent 30 bytes
no ex
09:12:43.341 [DBG] 31 bytes read
no ex
09:12:43.341 [DBG] Sent 30 bytes
no ex
09:12:43.537 [DBG] Sent 184 bytes
no ex
09:12:46.314 [DBG] 31 bytes read
no ex
09:12:46.314 [DBG] Sent 30 bytes
no ex
09:12:47.284 [DBG] 47 bytes read
no ex
09:12:47.285 [DBG] Sent 25 bytes
no ex
09:12:48.528 [DBG] Sent 184 bytes
no ex
09:12:49.335 [DBG] 31 bytes read
no ex
09:12:49.335 [DBG] Sent 30 bytes
no ex
09:12:52.332 [DBG] 31 bytes read
no ex
09:12:52.332 [DBG] Sent 30 bytes
no ex
09:12:53.531 [DBG] Sent 184 bytes
no ex
09:12:55.338 [DBG] 31 bytes read
no ex
09:12:55.339 [DBG] Sent 30 bytes
no ex
09:12:58.326 [DBG] 31 bytes read
no ex
09:12:58.326 [DBG] Sent 30 bytes
no ex
09:12:58.536 [DBG] Sent 184 bytes
no ex
09:13:01.315 [DBG] 31 bytes read
no ex
09:13:01.315 [DBG] Sent 30 bytes
no ex
09:13:02.215 [DBG] 47 bytes read
no ex
09:13:02.216 [DBG] Sent 25 bytes
no ex
09:13:03.544 [DBG] Sent 184 bytes
no ex
09:13:04.329 [DBG] 31 bytes read
no ex
09:13:04.329 [DBG] Sent 30 bytes
no ex
09:13:07.302 [DBG] 31 bytes read
no ex
09:13:07.302 [DBG] Sent 30 bytes
no ex
09:13:08.536 [DBG] Sent 184 bytes
no ex
09:13:10.299 [DBG] 31 bytes read
no ex
09:13:10.299 [DBG] Sent 30 bytes
no ex
09:13:13.326 [DBG] 31 bytes read
no ex
09:13:13.326 [DBG] Sent 30 bytes
no ex
09:13:13.541 [DBG] Sent 184 bytes
no ex
09:13:17.708 [DBG] 31 bytes read
no ex
09:13:17.708 [DBG] Sent 30 bytes
no ex
09:13:18.544 [DBG] Sent 184 bytes
no ex
09:13:19.319 [DBG] 31 bytes read
no ex
09:13:19.319 [DBG] Sent 30 bytes
no ex
09:13:22.305 [DBG] 31 bytes read
no ex
09:13:22.305 [DBG] Sent 30 bytes
no ex
09:13:23.540 [DBG] Sent 184 bytes
no ex
09:13:25.313 [DBG] 31 bytes read
no ex
09:13:25.313 [DBG] Sent 30 bytes
no ex
09:13:28.340 [DBG] 31 bytes read
no ex
09:13:28.340 [DBG] Sent 30 bytes
no ex
09:13:28.544 [DBG] Sent 184 bytes
no ex
09:13:31.344 [DBG] 31 bytes read
no ex
09:13:31.345 [DBG] Sent 30 bytes
no ex
09:13:33.241 [DBG] 47 bytes read
no ex
09:13:33.241 [DBG] Sent 25 bytes
no ex
09:13:33.547 [DBG] Sent 184 bytes
no ex
09:13:34.306 [DBG] 31 bytes read
no ex
09:13:34.307 [DBG] Sent 30 bytes
no ex
09:13:37.302 [DBG] 31 bytes read
no ex
09:13:37.302 [DBG] Sent 30 bytes
no ex
09:13:37.541 [DBG] Sent 72 bytes
no ex
09:13:38.548 [DBG] Sent 184 bytes
no ex
09:13:40.318 [DBG] 31 bytes read
no ex
09:13:40.318 [DBG] Sent 30 bytes
no ex
09:13:43.333 [DBG] 31 bytes read
no ex
09:13:43.333 [DBG] Sent 30 bytes
no ex
09:13:43.534 [DBG] Sent 184 bytes
no ex
09:13:46.343 [DBG] 31 bytes read
no ex
09:13:46.343 [DBG] Sent 30 bytes
no ex
09:13:48.535 [DBG] Sent 184 bytes
no ex
09:13:49.362 [DBG] 31 bytes read
no ex
09:13:49.362 [DBG] Sent 30 bytes
no ex
09:13:52.322 [DBG] 31 bytes read
no ex
09:13:52.323 [DBG] Sent 30 bytes
no ex
09:13:53.544 [DBG] Sent 184 bytes
no ex
09:13:55.309 [DBG] 31 bytes read
no ex
09:13:55.310 [DBG] Sent 30 bytes
no ex
09:13:58.316 [DBG] 31 bytes read
no ex
09:13:58.316 [DBG] Sent 30 bytes
no ex
09:13:58.549 [DBG] Sent 184 bytes
no ex
09:14:02.652 [DBG] 31 bytes read
no ex
09:14:02.652 [DBG] Sent 30 bytes
no ex
09:14:03.539 [DBG] Sent 184 bytes
no ex
09:14:04.417 [DBG] 31 bytes read
no ex
09:14:04.417 [DBG] Sent 30 bytes
no ex
09:14:07.452 [DBG] 31 bytes read
no ex
09:14:07.452 [DBG] Sent 30 bytes
no ex
09:14:08.542 [DBG] Sent 184 bytes
no ex
09:14:10.399 [DBG] 31 bytes read
no ex
09:14:10.399 [DBG] Sent 30 bytes
no ex
09:14:13.393 [DBG] 31 bytes read
no ex
09:14:13.393 [DBG] Sent 30 bytes
no ex
09:14:13.551 [DBG] Sent 184 bytes
no ex
09:14:16.427 [DBG] 31 bytes read
no ex
09:14:16.427 [DBG] Sent 30 bytes
no ex
09:14:18.539 [DBG] Sent 184 bytes
no ex
09:14:21.459 [INF] 8888 bot: 2, peer: 0, ccu: 0
09:14:21.459 [DBG] Sent 4 bytes
no ex
09:14:21.460 [DBG] Swallowing ObjectDisposedException
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
09:14:25.532 [DBG] Client connected from 127.0.0.1:45016
no ex
09:14:25.532 [DBG] 838 bytes read
no ex
09:14:25.532 [DBG] Building Hybi-14 Response
no ex
09:14:25.532 [DBG] Sent 129 bytes
no ex
09:14:25.714 [DBG] 31 bytes read
no ex
09:14:25.714 [DBG] Sent 30 bytes
no ex
09:14:28.548 [DBG] Sent 184 bytes
no ex
09:14:28.738 [DBG] 31 bytes read
no ex
09:14:28.738 [DBG] Sent 30 bytes
no ex
09:14:31.739 [DBG] 31 bytes read
no ex
09:14:31.739 [DBG] Sent 30 bytes
no ex
09:14:33.553 [DBG] Sent 184 bytes
no ex
09:14:34.732 [DBG] 31 bytes read
no ex
09:14:34.733 [DBG] Sent 30 bytes
no ex
09:14:37.759 [DBG] 31 bytes read
no ex
09:14:37.760 [DBG] Sent 30 bytes
no ex
09:14:38.553 [DBG] Sent 184 bytes
no ex
09:14:40.731 [DBG] 31 bytes read
no ex
09:14:40.731 [DBG] Sent 30 bytes
no ex
09:14:43.541 [DBG] Sent 184 bytes
no ex
09:14:43.729 [DBG] 31 bytes read
no ex
09:14:43.729 [DBG] Sent 30 bytes
no ex
09:14:46.798 [DBG] 31 bytes read
no ex
09:14:46.798 [DBG] Sent 30 bytes
no ex
09:14:48.552 [DBG] Sent 184 bytes
no ex
09:14:49.739 [DBG] 31 bytes read
no ex
09:14:49.739 [DBG] Sent 30 bytes
no ex
09:14:49.848 [INF] 8888 bot: 2, peer: 1, ccu: 0
09:14:50.548 [DBG] Sent 72 bytes
no ex
09:14:50.548 [DBG] Sent 73 bytes
no ex
09:14:52.751 [DBG] 31 bytes read
no ex
09:14:52.751 [DBG] Sent 30 bytes
no ex
09:14:53.543 [DBG] Sent 184 bytes
no ex
09:14:55.750 [DBG] 31 bytes read
no ex
09:14:55.750 [DBG] Sent 30 bytes
no ex
09:14:58.550 [DBG] Sent 184 bytes
no ex
09:14:58.735 [DBG] 31 bytes read
no ex
09:14:58.735 [DBG] Sent 30 bytes
no ex
09:15:01.733 [DBG] 31 bytes read
no ex
09:15:01.733 [DBG] Sent 30 bytes
no ex
09:15:03.557 [DBG] Sent 184 bytes
no ex
09:15:04.732 [DBG] 31 bytes read
no ex
09:15:04.732 [DBG] Sent 30 bytes
no ex
09:15:07.750 [DBG] 31 bytes read
no ex
09:15:07.751 [DBG] Sent 30 bytes
no ex
09:15:08.552 [DBG] Sent 184 bytes
no ex
09:15:10.727 [DBG] 31 bytes read
no ex
09:15:10.727 [DBG] Sent 30 bytes
no ex
09:15:13.553 [DBG] Sent 184 bytes
no ex
09:15:13.747 [DBG] 31 bytes read
no ex
09:15:13.747 [DBG] Sent 30 bytes
no ex
09:15:16.747 [DBG] 31 bytes read
no ex
09:15:16.747 [DBG] Sent 30 bytes
no ex
09:15:18.553 [DBG] Sent 184 bytes
no ex
09:15:19.719 [DBG] 31 bytes read
no ex
09:15:19.720 [DBG] Sent 30 bytes
no ex
09:15:22.734 [DBG] 31 bytes read
no ex
09:15:22.734 [DBG] Sent 30 bytes
no ex
09:15:23.563 [DBG] Sent 184 bytes
no ex
09:15:25.735 [DBG] 31 bytes read
no ex
09:15:25.735 [DBG] Sent 30 bytes
no ex
09:15:28.556 [DBG] Sent 184 bytes
no ex
09:15:28.731 [DBG] 31 bytes read
no ex
09:15:28.731 [DBG] Sent 30 bytes
no ex
09:15:30.561 [DBG] Sent 72 bytes
no ex
09:15:31.740 [DBG] 31 bytes read
no ex
09:15:31.740 [DBG] Sent 30 bytes
no ex
09:15:33.556 [DBG] Sent 184 bytes
no ex
09:15:34.722 [DBG] 31 bytes read
no ex
09:15:34.722 [DBG] Sent 30 bytes
no ex
09:15:38.163 [DBG] 31 bytes read
no ex
09:15:38.163 [DBG] Sent 30 bytes
no ex
09:15:38.563 [DBG] Sent 184 bytes
no ex
09:15:40.739 [DBG] 31 bytes read
no ex
09:15:40.739 [DBG] Sent 30 bytes
no ex
09:15:43.556 [DBG] Sent 184 bytes
no ex
09:15:43.757 [DBG] 31 bytes read
no ex
09:15:43.757 [DBG] Sent 30 bytes
no ex
09:15:44.949 [DBG] 8 bytes read
no ex
09:15:44.949 [DBG] Sent 4 bytes
no ex
09:15:44.963 [INF] 8888 bot: 2, peer: 0, ccu: 0
09:16:36.088 [INF] RunScan: 0
09:16:36.088 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:16)","Data":null,"DataObj":null}
09:17:28.646 [DBG] Client connected from 127.0.0.1:45278
no ex
09:17:28.646 [DBG] 838 bytes read
no ex
09:17:28.646 [DBG] Building Hybi-14 Response
no ex
09:17:28.646 [DBG] Sent 129 bytes
no ex
09:17:33.571 [DBG] Sent 184 bytes
no ex
09:17:33.674 [DBG] Sent 4 bytes
no ex
09:17:33.674 [DBG] Swallowing ObjectDisposedException
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
09:17:37.672 [DBG] Client connected from 127.0.0.1:45288
no ex
09:17:37.672 [DBG] 838 bytes read
no ex
09:17:37.672 [DBG] Building Hybi-14 Response
no ex
09:17:37.672 [DBG] Sent 129 bytes
no ex
09:17:37.746 [INF] RunHandleWinLoss: 0
09:17:37.746 [INF] CalculateResult: 20220808
09:17:37.746 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
09:17:37.840 [DBG] 31 bytes read
no ex
09:17:37.840 [DBG] Sent 30 bytes
no ex
09:17:38.576 [DBG] Sent 184 bytes
no ex
09:17:40.848 [DBG] 31 bytes read
no ex
09:17:40.848 [DBG] Sent 30 bytes
no ex
09:17:43.578 [DBG] Sent 184 bytes
no ex
09:17:43.869 [DBG] 31 bytes read
no ex
09:17:43.869 [DBG] Sent 30 bytes
no ex
09:17:46.892 [DBG] 31 bytes read
no ex
09:17:46.892 [DBG] Sent 30 bytes
no ex
09:17:48.582 [DBG] Sent 184 bytes
no ex
09:17:49.880 [DBG] 31 bytes read
no ex
09:17:49.880 [DBG] Sent 30 bytes
no ex
09:17:49.913 [INF] 8888 bot: 2, peer: 1, ccu: 0
09:17:52.885 [DBG] 31 bytes read
no ex
09:17:52.886 [DBG] Sent 30 bytes
no ex
09:17:53.583 [DBG] Sent 184 bytes
no ex
09:17:55.884 [DBG] 31 bytes read
no ex
09:17:55.884 [DBG] Sent 30 bytes
no ex
09:17:58.571 [DBG] Sent 184 bytes
no ex
09:17:58.933 [DBG] 31 bytes read
no ex
09:17:58.933 [DBG] Sent 30 bytes
no ex
09:18:03.572 [DBG] Sent 184 bytes
no ex
09:18:03.966 [DBG] Sent 4 bytes
no ex
09:18:03.966 [INF] 8888 bot: 2, peer: 0, ccu: 0
09:18:03.966 [DBG] Swallowing ObjectDisposedException
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
09:18:05.345 [DBG] Client connected from 127.0.0.1:45324
no ex
09:18:05.345 [DBG] 887 bytes read
no ex
09:18:05.345 [DBG] Building Hybi-14 Response
no ex
09:18:05.346 [DBG] Sent 129 bytes
no ex
09:18:05.579 [DBG] 31 bytes read
no ex
09:18:05.580 [DBG] Sent 30 bytes
no ex
09:18:08.581 [DBG] Sent 184 bytes
no ex
09:18:10.469 [DBG] Client connected from 127.0.0.1:45334
no ex
09:18:10.469 [DBG] 838 bytes read
no ex
09:18:10.469 [DBG] Building Hybi-14 Response
no ex
09:18:10.469 [DBG] Sent 129 bytes
no ex
09:18:10.556 [DBG] 31 bytes read
no ex
09:18:10.556 [DBG] Sent 30 bytes
no ex
09:18:10.612 [INF] 8888 bot: 2, peer: 1, ccu: 0
09:18:10.612 [DBG] Sent 4 bytes
no ex
09:18:10.613 [DBG] Swallowing ObjectDisposedException
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
09:18:13.317 [DBG] 47 bytes read
no ex
09:18:13.317 [DBG] Sent 25 bytes
no ex
09:18:13.584 [DBG] Sent 184 bytes
no ex
09:18:13.588 [DBG] 31 bytes read
no ex
09:18:13.588 [DBG] Sent 30 bytes
no ex
09:18:15.117 [DBG] 47 bytes read
no ex
09:18:15.118 [DBG] Sent 25 bytes
no ex
09:18:16.574 [DBG] 31 bytes read
no ex
09:18:16.575 [DBG] Sent 30 bytes
no ex
09:18:18.573 [DBG] Sent 184 bytes
no ex
09:18:19.599 [DBG] 31 bytes read
no ex
09:18:19.599 [DBG] Sent 30 bytes
no ex
09:18:22.603 [DBG] 31 bytes read
no ex
09:18:22.603 [DBG] Sent 30 bytes
no ex
09:18:23.574 [DBG] Sent 184 bytes
no ex
09:18:25.572 [DBG] 31 bytes read
no ex
09:18:25.573 [DBG] Sent 30 bytes
no ex
09:18:28.584 [DBG] Sent 184 bytes
no ex
09:18:28.591 [DBG] 31 bytes read
no ex
09:18:28.591 [DBG] Sent 30 bytes
no ex
09:18:31.579 [DBG] 31 bytes read
no ex
09:18:31.579 [DBG] Sent 30 bytes
no ex
09:18:33.582 [DBG] Sent 184 bytes
no ex
09:18:34.579 [DBG] 31 bytes read
no ex
09:18:34.580 [DBG] Sent 30 bytes
no ex
09:18:37.491 [DBG] 47 bytes read
no ex
09:18:37.492 [DBG] Sent 25 bytes
no ex
09:18:37.575 [DBG] 31 bytes read
no ex
09:18:37.575 [DBG] Sent 30 bytes
no ex
09:18:38.578 [DBG] Sent 184 bytes
no ex
09:18:40.587 [DBG] 31 bytes read
no ex
09:18:40.587 [DBG] Sent 30 bytes
no ex
09:18:43.190 [DBG] 47 bytes read
no ex
09:18:43.190 [DBG] Sent 25 bytes
no ex
09:18:43.567 [DBG] 31 bytes read
no ex
09:18:43.567 [DBG] Sent 30 bytes
no ex
09:18:43.580 [DBG] Sent 184 bytes
no ex
09:18:46.573 [DBG] 31 bytes read
no ex
09:18:46.573 [DBG] Sent 30 bytes
no ex
09:18:48.586 [DBG] Sent 184 bytes
no ex
09:18:49.577 [DBG] 31 bytes read
no ex
09:18:49.578 [DBG] Sent 30 bytes
no ex
09:18:50.784 [DBG] 49 bytes read
no ex
09:18:50.784 [DBG] Sent 25 bytes
no ex
09:18:52.584 [DBG] 31 bytes read
no ex
09:18:52.584 [DBG] Sent 30 bytes
no ex
09:18:53.578 [DBG] Sent 184 bytes
no ex
09:18:55.575 [DBG] 31 bytes read
no ex
09:18:55.576 [DBG] Sent 30 bytes
no ex
09:18:58.580 [DBG] 31 bytes read
no ex
09:18:58.580 [DBG] Sent 30 bytes
no ex
09:18:58.581 [DBG] Sent 184 bytes
no ex
09:19:01.580 [DBG] 31 bytes read
no ex
09:19:01.580 [DBG] Sent 30 bytes
no ex
09:19:03.588 [DBG] Sent 184 bytes
no ex
09:19:04.566 [DBG] 31 bytes read
no ex
09:19:04.567 [DBG] Sent 30 bytes
no ex
09:19:07.579 [DBG] 31 bytes read
no ex
09:19:07.579 [DBG] Sent 30 bytes
no ex
09:19:08.594 [DBG] Sent 184 bytes
no ex
09:19:09.781 [DBG] 47 bytes read
no ex
09:19:09.781 [DBG] Sent 25 bytes
no ex
09:19:10.572 [DBG] 31 bytes read
no ex
09:19:10.572 [DBG] Sent 30 bytes
no ex
09:19:13.577 [DBG] 31 bytes read
no ex
09:19:13.577 [DBG] Sent 30 bytes
no ex
09:19:13.584 [DBG] Sent 184 bytes
no ex
09:19:16.569 [DBG] 31 bytes read
no ex
09:19:16.570 [DBG] Sent 30 bytes
no ex
09:19:18.588 [DBG] Sent 184 bytes
no ex
09:19:19.575 [DBG] 31 bytes read
no ex
09:19:19.575 [DBG] Sent 30 bytes
no ex
09:19:22.604 [DBG] 31 bytes read
no ex
09:19:22.604 [DBG] Sent 30 bytes
no ex
09:19:23.590 [DBG] Sent 184 bytes
no ex
09:19:25.571 [DBG] 31 bytes read
no ex
09:19:25.572 [DBG] Sent 30 bytes
no ex
09:19:28.576 [DBG] 31 bytes read
no ex
09:19:28.577 [DBG] Sent 30 bytes
no ex
09:19:28.599 [DBG] Sent 184 bytes
no ex
09:19:30.509 [DBG] 47 bytes read
no ex
09:19:30.509 [DBG] Sent 25 bytes
no ex
09:19:31.048 [DBG] 94 bytes read
no ex
09:19:31.048 [DBG] Sent 25 bytes
no ex
09:19:31.048 [DBG] Sent 25 bytes
no ex
09:19:31.552 [DBG] 31 bytes read
no ex
09:19:31.552 [DBG] Sent 30 bytes
no ex
09:19:33.587 [DBG] Sent 184 bytes
no ex
09:19:34.568 [DBG] 31 bytes read
no ex
09:19:34.568 [DBG] Sent 30 bytes
no ex
09:19:37.555 [DBG] 31 bytes read
no ex
09:19:37.556 [DBG] Sent 30 bytes
no ex
09:19:38.597 [DBG] Sent 184 bytes
no ex
09:19:40.576 [DBG] 31 bytes read
no ex
09:19:40.577 [DBG] Sent 30 bytes
no ex
09:19:43.130 [DBG] 47 bytes read
no ex
09:19:43.131 [DBG] Sent 25 bytes
no ex
09:19:43.595 [DBG] Sent 184 bytes
no ex
09:19:45.446 [DBG] 31 bytes read
no ex
09:19:45.446 [DBG] Sent 30 bytes
no ex
09:19:46.559 [DBG] 31 bytes read
no ex
09:19:46.560 [DBG] Sent 30 bytes
no ex
09:19:48.601 [DBG] Sent 184 bytes
no ex
09:19:49.691 [DBG] 31 bytes read
no ex
09:19:49.691 [DBG] Sent 30 bytes
no ex
09:19:53.476 [DBG] 31 bytes read
no ex
09:19:53.477 [DBG] Sent 30 bytes
no ex
09:19:53.593 [DBG] Sent 184 bytes
no ex
09:19:55.587 [DBG] 31 bytes read
no ex
09:19:55.587 [DBG] Sent 30 bytes
no ex
09:19:58.592 [DBG] Sent 184 bytes
no ex
09:19:58.732 [DBG] 31 bytes read
no ex
09:19:58.733 [DBG] Sent 30 bytes
no ex
09:20:01.672 [DBG] 31 bytes read
no ex
09:20:01.672 [DBG] Sent 30 bytes
no ex
09:20:03.600 [DBG] Sent 184 bytes
no ex
09:20:04.655 [DBG] 31 bytes read
no ex
09:20:04.656 [DBG] Sent 30 bytes
no ex
09:20:07.703 [DBG] 31 bytes read
no ex
09:20:07.704 [DBG] Sent 30 bytes
no ex
09:20:08.589 [DBG] Sent 184 bytes
no ex
09:20:10.648 [DBG] 31 bytes read
no ex
09:20:10.648 [DBG] Sent 30 bytes
no ex
09:20:13.595 [DBG] Sent 184 bytes
no ex
09:20:13.700 [DBG] 31 bytes read
no ex
09:20:13.700 [DBG] Sent 30 bytes
no ex
09:20:16.691 [DBG] 31 bytes read
no ex
09:20:16.691 [DBG] Sent 30 bytes
no ex
09:20:18.603 [DBG] Sent 184 bytes
no ex
09:20:19.666 [DBG] 31 bytes read
no ex
09:20:19.666 [DBG] Sent 30 bytes
no ex
09:20:22.652 [DBG] 31 bytes read
no ex
09:20:22.653 [DBG] Sent 30 bytes
no ex
09:20:23.589 [DBG] Sent 184 bytes
no ex
09:20:25.709 [DBG] 31 bytes read
no ex
09:20:25.709 [DBG] Sent 30 bytes
no ex
09:20:28.594 [DBG] Sent 184 bytes
no ex
09:20:28.663 [DBG] 31 bytes read
no ex
09:20:28.663 [DBG] Sent 30 bytes
no ex
09:20:31.668 [DBG] 31 bytes read
no ex
09:20:31.669 [DBG] Sent 30 bytes
no ex
09:20:33.598 [DBG] Sent 184 bytes
no ex
09:20:34.688 [DBG] 31 bytes read
no ex
09:20:34.688 [DBG] Sent 30 bytes
no ex
09:20:37.695 [DBG] 31 bytes read
no ex
09:20:37.695 [DBG] Sent 30 bytes
no ex
09:20:38.603 [DBG] Sent 184 bytes
no ex
09:20:40.719 [DBG] 31 bytes read
no ex
09:20:40.719 [DBG] Sent 30 bytes
no ex
09:20:41.605 [DBG] Sent 72 bytes
no ex
09:20:43.602 [DBG] Sent 184 bytes
no ex
09:20:43.647 [DBG] 31 bytes read
no ex
09:20:43.647 [DBG] Sent 30 bytes
no ex
09:20:46.670 [DBG] 31 bytes read
no ex
09:20:46.670 [DBG] Sent 30 bytes
no ex
09:20:48.607 [DBG] Sent 184 bytes
no ex
09:20:49.667 [DBG] 31 bytes read
no ex
09:20:49.668 [DBG] Sent 30 bytes
no ex
09:20:52.690 [DBG] 31 bytes read
no ex
09:20:52.690 [DBG] Sent 30 bytes
no ex
09:20:53.604 [DBG] Sent 184 bytes
no ex
09:20:55.659 [DBG] 31 bytes read
no ex
09:20:55.660 [DBG] Sent 30 bytes
no ex
09:20:58.605 [DBG] Sent 184 bytes
no ex
09:20:58.651 [DBG] 31 bytes read
no ex
09:20:58.651 [DBG] Sent 30 bytes
no ex
09:21:01.561 [DBG] 31 bytes read
no ex
09:21:01.561 [DBG] Sent 30 bytes
no ex
09:21:03.605 [DBG] Sent 184 bytes
no ex
09:21:04.704 [DBG] 31 bytes read
no ex
09:21:04.705 [DBG] Sent 30 bytes
no ex
09:21:07.649 [DBG] 31 bytes read
no ex
09:21:07.649 [DBG] Sent 30 bytes
no ex
09:21:08.599 [DBG] Sent 184 bytes
no ex
09:21:10.682 [DBG] 31 bytes read
no ex
09:21:10.682 [DBG] Sent 30 bytes
no ex
09:21:13.602 [DBG] Sent 184 bytes
no ex
09:21:13.902 [DBG] 31 bytes read
no ex
09:21:13.902 [DBG] Sent 30 bytes
no ex
09:21:16.673 [DBG] 31 bytes read
no ex
09:21:16.673 [DBG] Sent 30 bytes
no ex
09:21:18.605 [DBG] Sent 184 bytes
no ex
09:21:19.682 [DBG] 31 bytes read
no ex
09:21:19.682 [DBG] Sent 30 bytes
no ex
09:21:22.758 [DBG] 31 bytes read
no ex
09:21:22.759 [DBG] Sent 30 bytes
no ex
09:21:23.609 [DBG] Sent 184 bytes
no ex
09:21:25.692 [DBG] 31 bytes read
no ex
09:21:25.692 [DBG] Sent 30 bytes
no ex
09:21:28.611 [DBG] Sent 184 bytes
no ex
09:21:28.691 [DBG] 31 bytes read
no ex
09:21:28.691 [DBG] Sent 30 bytes
no ex
09:21:31.708 [DBG] 31 bytes read
no ex
09:21:31.708 [DBG] Sent 30 bytes
no ex
09:21:33.598 [DBG] Sent 184 bytes
no ex
09:21:34.736 [DBG] 31 bytes read
no ex
09:21:34.736 [DBG] Sent 30 bytes
no ex
09:21:36.088 [INF] RunScan: 0
09:21:36.089 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:21)","Data":null,"DataObj":null}
09:21:37.675 [DBG] 31 bytes read
no ex
09:21:37.675 [DBG] Sent 30 bytes
no ex
09:21:38.603 [DBG] Sent 184 bytes
no ex
09:21:40.688 [DBG] 31 bytes read
no ex
09:21:40.688 [DBG] Sent 30 bytes
no ex
09:21:43.603 [DBG] Sent 184 bytes
no ex
09:21:43.775 [DBG] 31 bytes read
no ex
09:21:43.775 [DBG] Sent 30 bytes
no ex
09:21:46.695 [DBG] 31 bytes read
no ex
09:21:46.696 [DBG] Sent 30 bytes
no ex
09:21:48.600 [DBG] Sent 184 bytes
no ex
09:21:49.688 [DBG] 31 bytes read
no ex
09:21:49.689 [DBG] Sent 30 bytes
no ex
09:21:52.675 [DBG] 31 bytes read
no ex
09:21:52.676 [DBG] Sent 30 bytes
no ex
09:21:53.604 [DBG] Sent 184 bytes
no ex
09:21:55.707 [DBG] 31 bytes read
no ex
09:21:55.707 [DBG] Sent 30 bytes
no ex
09:21:58.612 [DBG] Sent 184 bytes
no ex
09:21:58.701 [DBG] 31 bytes read
no ex
09:21:58.701 [DBG] Sent 30 bytes
no ex
09:22:01.675 [DBG] 31 bytes read
no ex
09:22:01.675 [DBG] Sent 30 bytes
no ex
09:22:03.611 [DBG] Sent 184 bytes
no ex
09:22:04.685 [DBG] 31 bytes read
no ex
09:22:04.685 [DBG] Sent 30 bytes
no ex
09:22:07.715 [DBG] 31 bytes read
no ex
09:22:07.715 [DBG] Sent 30 bytes
no ex
09:22:08.604 [DBG] Sent 184 bytes
no ex
09:22:10.700 [DBG] 31 bytes read
no ex
09:22:10.700 [DBG] Sent 30 bytes
no ex
09:22:13.609 [DBG] Sent 184 bytes
no ex
09:22:13.758 [DBG] 31 bytes read
no ex
09:22:13.758 [DBG] Sent 30 bytes
no ex
09:22:16.652 [DBG] 31 bytes read
no ex
09:22:16.652 [DBG] Sent 30 bytes
no ex
09:22:18.605 [DBG] Sent 184 bytes
no ex
09:22:19.675 [DBG] 31 bytes read
no ex
09:22:19.675 [DBG] Sent 30 bytes
no ex
09:22:22.677 [DBG] 31 bytes read
no ex
09:22:22.677 [DBG] Sent 30 bytes
no ex
09:22:23.607 [DBG] Sent 184 bytes
no ex
09:22:25.675 [DBG] 31 bytes read
no ex
09:22:25.675 [DBG] Sent 30 bytes
no ex
09:22:28.621 [DBG] Sent 184 bytes
no ex
09:22:28.695 [DBG] 31 bytes read
no ex
09:22:28.695 [DBG] Sent 30 bytes
no ex
09:22:31.649 [DBG] 31 bytes read
no ex
09:22:31.649 [DBG] Sent 30 bytes
no ex
09:22:33.621 [DBG] Sent 184 bytes
no ex
09:22:34.667 [DBG] 31 bytes read
no ex
09:22:34.668 [DBG] Sent 30 bytes
no ex
09:22:37.703 [DBG] 31 bytes read
no ex
09:22:37.704 [DBG] Sent 30 bytes
no ex
09:22:37.747 [INF] RunHandleWinLoss: 0
09:22:37.747 [INF] CalculateResult: 20220808
09:22:37.747 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
09:22:38.607 [DBG] Sent 184 bytes
no ex
09:22:40.691 [DBG] 31 bytes read
no ex
09:22:40.691 [DBG] Sent 30 bytes
no ex
09:22:43.608 [DBG] Sent 184 bytes
no ex
09:22:43.695 [DBG] 31 bytes read
no ex
09:22:43.695 [DBG] Sent 30 bytes
no ex
09:22:46.687 [DBG] 31 bytes read
no ex
09:22:46.687 [DBG] Sent 30 bytes
no ex
09:22:48.620 [DBG] Sent 184 bytes
no ex
09:22:49.654 [DBG] 31 bytes read
no ex
09:22:49.654 [DBG] Sent 30 bytes
no ex
09:22:52.704 [DBG] 31 bytes read
no ex
09:22:52.704 [DBG] Sent 30 bytes
no ex
09:22:53.622 [DBG] Sent 184 bytes
no ex
09:22:55.634 [DBG] 31 bytes read
no ex
09:22:55.635 [DBG] Sent 30 bytes
no ex
09:22:58.623 [DBG] Sent 184 bytes
no ex
09:22:58.677 [DBG] 31 bytes read
no ex
09:22:58.677 [DBG] Sent 30 bytes
no ex
09:23:01.652 [DBG] 31 bytes read
no ex
09:23:01.652 [DBG] Sent 30 bytes
no ex
09:23:03.613 [DBG] Sent 184 bytes
no ex
09:23:04.693 [DBG] 31 bytes read
no ex
09:23:04.693 [DBG] Sent 30 bytes
no ex
09:23:07.667 [DBG] 31 bytes read
no ex
09:23:07.667 [DBG] Sent 30 bytes
no ex
09:23:08.615 [DBG] Sent 184 bytes
no ex
09:23:10.667 [DBG] 32 bytes read
no ex
09:23:10.667 [DBG] Sent 31 bytes
no ex
09:23:13.623 [DBG] Sent 184 bytes
no ex
09:23:13.675 [DBG] 32 bytes read
no ex
09:23:13.675 [DBG] Sent 31 bytes
no ex
09:23:14.614 [DBG] Sent 71 bytes
no ex
09:23:16.676 [DBG] 32 bytes read
no ex
09:23:16.676 [DBG] Sent 31 bytes
no ex
09:23:18.627 [DBG] Sent 184 bytes
no ex
09:23:19.734 [DBG] 32 bytes read
no ex
09:23:19.734 [DBG] Sent 31 bytes
no ex
09:23:22.961 [DBG] 32 bytes read
no ex
09:23:22.974 [DBG] Sent 31 bytes
no ex
09:23:23.624 [DBG] Sent 184 bytes
no ex
09:23:25.590 [DBG] 32 bytes read
no ex
09:23:25.591 [DBG] Sent 31 bytes
no ex
09:23:28.563 [DBG] 32 bytes read
no ex
09:23:28.564 [DBG] Sent 31 bytes
no ex
09:23:28.614 [DBG] Sent 184 bytes
no ex
09:23:31.579 [DBG] 32 bytes read
no ex
09:23:31.580 [DBG] Sent 31 bytes
no ex
09:23:33.618 [DBG] Sent 184 bytes
no ex
09:23:34.575 [DBG] 32 bytes read
no ex
09:23:34.576 [DBG] Sent 31 bytes
no ex
09:23:37.584 [DBG] 32 bytes read
no ex
09:23:37.584 [DBG] Sent 31 bytes
no ex
09:23:38.626 [DBG] Sent 184 bytes
no ex
09:23:40.576 [DBG] 32 bytes read
no ex
09:23:40.576 [DBG] Sent 31 bytes
no ex
09:23:43.560 [DBG] 32 bytes read
no ex
09:23:43.560 [DBG] Sent 31 bytes
no ex
09:23:43.628 [DBG] Sent 184 bytes
no ex
09:23:46.559 [DBG] 32 bytes read
no ex
09:23:46.559 [DBG] Sent 31 bytes
no ex
09:23:48.625 [DBG] Sent 184 bytes
no ex
09:23:49.590 [DBG] 32 bytes read
no ex
09:23:49.590 [DBG] Sent 31 bytes
no ex
09:23:52.575 [DBG] 32 bytes read
no ex
09:23:52.575 [DBG] Sent 31 bytes
no ex
09:23:53.628 [DBG] Sent 184 bytes
no ex
09:23:55.582 [DBG] 32 bytes read
no ex
09:23:55.582 [DBG] Sent 31 bytes
no ex
09:23:58.559 [DBG] 32 bytes read
no ex
09:23:58.559 [DBG] Sent 31 bytes
no ex
09:23:58.630 [DBG] Sent 184 bytes
no ex
09:24:01.576 [DBG] 32 bytes read
no ex
09:24:01.576 [DBG] Sent 31 bytes
no ex
09:24:03.620 [DBG] Sent 184 bytes
no ex
09:24:04.551 [DBG] 32 bytes read
no ex
09:24:04.552 [DBG] Sent 31 bytes
no ex
09:24:07.567 [DBG] 32 bytes read
no ex
09:24:07.567 [DBG] Sent 31 bytes
no ex
09:24:08.621 [DBG] Sent 184 bytes
no ex
09:24:10.584 [DBG] 32 bytes read
no ex
09:24:10.585 [DBG] Sent 31 bytes
no ex
09:24:13.572 [DBG] 32 bytes read
no ex
09:24:13.573 [DBG] Sent 31 bytes
no ex
09:24:13.626 [DBG] Sent 184 bytes
no ex
09:24:16.572 [DBG] 32 bytes read
no ex
09:24:16.573 [DBG] Sent 31 bytes
no ex
09:24:18.635 [DBG] Sent 184 bytes
no ex
09:24:19.586 [DBG] 32 bytes read
no ex
09:24:19.586 [DBG] Sent 31 bytes
no ex
09:24:22.562 [DBG] 32 bytes read
no ex
09:24:22.562 [DBG] Sent 31 bytes
no ex
09:24:23.623 [DBG] Sent 184 bytes
no ex
09:24:25.566 [DBG] 32 bytes read
no ex
09:24:25.566 [DBG] Sent 31 bytes
no ex
09:24:28.559 [DBG] 32 bytes read
no ex
09:24:28.559 [DBG] Sent 31 bytes
no ex
09:24:28.637 [DBG] Sent 184 bytes
no ex
09:24:31.580 [DBG] 32 bytes read
no ex
09:24:31.580 [DBG] Sent 31 bytes
no ex
09:24:33.623 [DBG] Sent 184 bytes
no ex
09:24:34.563 [DBG] 32 bytes read
no ex
09:24:34.563 [DBG] Sent 31 bytes
no ex
09:24:37.595 [DBG] 32 bytes read
no ex
09:24:37.595 [DBG] Sent 31 bytes
no ex
09:24:38.627 [DBG] Sent 184 bytes
no ex
09:24:40.564 [DBG] 32 bytes read
no ex
09:24:40.564 [DBG] Sent 31 bytes
no ex
09:24:43.570 [DBG] 32 bytes read
no ex
09:24:43.571 [DBG] Sent 31 bytes
no ex
09:24:43.626 [DBG] Sent 184 bytes
no ex
09:24:46.559 [DBG] 32 bytes read
no ex
09:24:46.559 [DBG] Sent 31 bytes
no ex
09:24:48.634 [DBG] Sent 184 bytes
no ex
09:24:49.575 [DBG] 32 bytes read
no ex
09:24:49.575 [DBG] Sent 31 bytes
no ex
09:24:52.575 [DBG] 32 bytes read
no ex
09:24:52.575 [DBG] Sent 31 bytes
no ex
09:24:53.622 [DBG] Sent 184 bytes
no ex
09:24:55.576 [DBG] 32 bytes read
no ex
09:24:55.577 [DBG] Sent 31 bytes
no ex
09:24:58.605 [DBG] 32 bytes read
no ex
09:24:58.607 [DBG] Sent 31 bytes
no ex
09:24:58.628 [DBG] Sent 184 bytes
no ex
09:25:01.581 [DBG] 32 bytes read
no ex
09:25:01.581 [DBG] Sent 31 bytes
no ex
09:25:03.623 [DBG] Sent 184 bytes
no ex
09:25:04.579 [DBG] 32 bytes read
no ex
09:25:04.579 [DBG] Sent 31 bytes
no ex
09:25:07.581 [DBG] 32 bytes read
no ex
09:25:07.581 [DBG] Sent 31 bytes
no ex
09:25:08.629 [DBG] Sent 184 bytes
no ex
09:25:10.563 [DBG] 32 bytes read
no ex
09:25:10.563 [DBG] Sent 31 bytes
no ex
09:25:13.565 [DBG] 32 bytes read
no ex
09:25:13.565 [DBG] Sent 31 bytes
no ex
09:25:13.629 [DBG] Sent 184 bytes
no ex
09:25:16.579 [DBG] 32 bytes read
no ex
09:25:16.579 [DBG] Sent 31 bytes
no ex
09:25:18.632 [DBG] Sent 184 bytes
no ex
09:25:19.591 [DBG] 32 bytes read
no ex
09:25:19.591 [DBG] Sent 31 bytes
no ex
09:25:22.570 [DBG] 32 bytes read
no ex
09:25:22.570 [DBG] Sent 31 bytes
no ex
09:25:23.631 [DBG] Sent 184 bytes
no ex
09:25:25.575 [DBG] 32 bytes read
no ex
09:25:25.575 [DBG] Sent 31 bytes
no ex
09:25:28.575 [DBG] 32 bytes read
no ex
09:25:28.575 [DBG] Sent 31 bytes
no ex
09:25:28.641 [DBG] Sent 184 bytes
no ex
09:25:31.580 [DBG] 32 bytes read
no ex
09:25:31.580 [DBG] Sent 31 bytes
no ex
09:25:33.632 [DBG] Sent 184 bytes
no ex
09:25:34.574 [DBG] 32 bytes read
no ex
09:25:34.575 [DBG] Sent 31 bytes
no ex
09:25:37.568 [DBG] 32 bytes read
no ex
09:25:37.568 [DBG] Sent 31 bytes
no ex
09:25:38.635 [DBG] Sent 184 bytes
no ex
09:25:40.593 [DBG] 32 bytes read
no ex
09:25:40.594 [DBG] Sent 31 bytes
no ex
09:25:43.583 [DBG] 32 bytes read
no ex
09:25:43.583 [DBG] Sent 31 bytes
no ex
09:25:43.636 [DBG] Sent 184 bytes
no ex
09:25:46.563 [DBG] 32 bytes read
no ex
09:25:46.563 [DBG] Sent 31 bytes
no ex
09:25:48.644 [DBG] Sent 184 bytes
no ex
09:25:49.567 [DBG] 32 bytes read
no ex
09:25:49.567 [DBG] Sent 31 bytes
no ex
09:25:52.567 [DBG] 32 bytes read
no ex
09:25:52.567 [DBG] Sent 31 bytes
no ex
09:25:53.644 [DBG] Sent 184 bytes
no ex
09:25:55.574 [DBG] 32 bytes read
no ex
09:25:55.575 [DBG] Sent 31 bytes
no ex
09:25:58.600 [DBG] 32 bytes read
no ex
09:25:58.600 [DBG] Sent 31 bytes
no ex
09:25:58.636 [DBG] Sent 184 bytes
no ex
09:26:01.566 [DBG] 32 bytes read
no ex
09:26:01.567 [DBG] Sent 31 bytes
no ex
09:26:03.637 [DBG] Sent 184 bytes
no ex
09:26:04.593 [DBG] 32 bytes read
no ex
09:26:04.593 [DBG] Sent 31 bytes
no ex
09:26:07.571 [DBG] 32 bytes read
no ex
09:26:07.572 [DBG] Sent 31 bytes
no ex
09:26:08.636 [DBG] Sent 184 bytes
no ex
09:26:10.585 [DBG] 32 bytes read
no ex
09:26:10.585 [DBG] Sent 31 bytes
no ex
09:26:13.579 [DBG] 32 bytes read
no ex
09:26:13.579 [DBG] Sent 31 bytes
no ex
09:26:13.645 [DBG] Sent 184 bytes
no ex
09:26:16.578 [DBG] 32 bytes read
no ex
09:26:16.579 [DBG] Sent 31 bytes
no ex
09:26:18.649 [DBG] Sent 184 bytes
no ex
09:26:19.569 [DBG] 32 bytes read
no ex
09:26:19.569 [DBG] Sent 31 bytes
no ex
09:26:22.567 [DBG] 32 bytes read
no ex
09:26:22.567 [DBG] Sent 31 bytes
no ex
09:26:23.633 [DBG] Sent 184 bytes
no ex
09:26:25.568 [DBG] 32 bytes read
no ex
09:26:25.568 [DBG] Sent 31 bytes
no ex
09:26:28.568 [DBG] 32 bytes read
no ex
09:26:28.568 [DBG] Sent 31 bytes
no ex
09:26:28.638 [DBG] Sent 184 bytes
no ex
09:26:31.563 [DBG] 32 bytes read
no ex
09:26:31.564 [DBG] Sent 31 bytes
no ex
09:26:33.642 [DBG] Sent 184 bytes
no ex
09:26:34.584 [DBG] 32 bytes read
no ex
09:26:34.584 [DBG] Sent 31 bytes
no ex
09:26:36.089 [INF] RunScan: 0
09:26:36.090 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:26)","Data":null,"DataObj":null}
09:26:37.600 [DBG] 32 bytes read
no ex
09:26:37.600 [DBG] Sent 31 bytes
no ex
09:26:38.646 [DBG] Sent 184 bytes
no ex
09:26:40.608 [DBG] 32 bytes read
no ex
09:26:40.608 [DBG] Sent 31 bytes
no ex
09:26:43.567 [DBG] 32 bytes read
no ex
09:26:43.567 [DBG] Sent 31 bytes
no ex
09:26:43.645 [DBG] Sent 184 bytes
no ex
09:26:46.580 [DBG] 32 bytes read
no ex
09:26:46.580 [DBG] Sent 31 bytes
no ex
09:26:48.645 [DBG] Sent 184 bytes
no ex
09:26:49.563 [DBG] 32 bytes read
no ex
09:26:49.564 [DBG] Sent 31 bytes
no ex
09:26:52.563 [DBG] 32 bytes read
no ex
09:26:52.563 [DBG] Sent 31 bytes
no ex
09:26:53.647 [DBG] Sent 184 bytes
no ex
09:26:55.579 [DBG] 32 bytes read
no ex
09:26:55.579 [DBG] Sent 31 bytes
no ex
09:26:58.580 [DBG] 32 bytes read
no ex
09:26:58.581 [DBG] Sent 31 bytes
no ex
09:26:58.637 [DBG] Sent 184 bytes
no ex
09:27:01.580 [DBG] 32 bytes read
no ex
09:27:01.580 [DBG] Sent 31 bytes
no ex
09:27:03.638 [DBG] Sent 184 bytes
no ex
09:27:04.596 [DBG] 32 bytes read
no ex
09:27:04.596 [DBG] Sent 31 bytes
no ex
09:27:07.558 [DBG] 32 bytes read
no ex
09:27:07.559 [DBG] Sent 31 bytes
no ex
09:27:08.650 [DBG] Sent 184 bytes
no ex
09:27:10.609 [DBG] 32 bytes read
no ex
09:27:10.609 [DBG] Sent 31 bytes
no ex
09:27:13.575 [DBG] 32 bytes read
no ex
09:27:13.575 [DBG] Sent 31 bytes
no ex
09:27:13.653 [DBG] Sent 184 bytes
no ex
09:27:16.579 [DBG] 32 bytes read
no ex
09:27:16.579 [DBG] Sent 31 bytes
no ex
09:27:18.648 [DBG] Sent 184 bytes
no ex
09:27:19.559 [DBG] 32 bytes read
no ex
09:27:19.559 [DBG] Sent 31 bytes
no ex
09:27:22.578 [DBG] 32 bytes read
no ex
09:27:22.578 [DBG] Sent 31 bytes
no ex
09:27:23.651 [DBG] Sent 184 bytes
no ex
09:27:25.574 [DBG] 32 bytes read
no ex
09:27:25.574 [DBG] Sent 31 bytes
no ex
09:27:28.575 [DBG] 32 bytes read
no ex
09:27:28.575 [DBG] Sent 31 bytes
no ex
09:27:28.644 [DBG] Sent 184 bytes
no ex
09:27:31.559 [DBG] 32 bytes read
no ex
09:27:31.560 [DBG] Sent 31 bytes
no ex
09:27:33.650 [DBG] Sent 184 bytes
no ex
09:27:34.575 [DBG] 32 bytes read
no ex
09:27:34.575 [DBG] Sent 31 bytes
no ex
09:27:37.573 [DBG] 32 bytes read
no ex
09:27:37.573 [DBG] Sent 31 bytes
no ex
09:27:37.749 [INF] RunHandleWinLoss: 0
09:27:37.749 [INF] CalculateResult: 20220808
09:27:37.749 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
09:27:38.656 [DBG] Sent 184 bytes
no ex
09:27:40.562 [DBG] 32 bytes read
no ex
09:27:40.563 [DBG] Sent 31 bytes
no ex
09:27:43.568 [DBG] 32 bytes read
no ex
09:27:43.569 [DBG] Sent 31 bytes
no ex
09:27:43.650 [DBG] Sent 184 bytes
no ex
09:27:46.561 [DBG] 32 bytes read
no ex
09:27:46.561 [DBG] Sent 31 bytes
no ex
09:27:48.652 [DBG] Sent 184 bytes
no ex
09:27:49.562 [DBG] 32 bytes read
no ex
09:27:49.562 [DBG] Sent 31 bytes
no ex
09:27:52.564 [DBG] 32 bytes read
no ex
09:27:52.565 [DBG] Sent 31 bytes
no ex
09:27:53.646 [DBG] Sent 184 bytes
no ex
09:27:55.569 [DBG] 32 bytes read
no ex
09:27:55.569 [DBG] Sent 31 bytes
no ex
09:27:58.558 [DBG] 32 bytes read
no ex
09:27:58.559 [DBG] Sent 31 bytes
no ex
09:27:58.649 [DBG] Sent 184 bytes
no ex
09:28:01.581 [DBG] 32 bytes read
no ex
09:28:01.581 [DBG] Sent 31 bytes
no ex
09:28:03.651 [DBG] Sent 184 bytes
no ex
09:28:04.611 [DBG] 32 bytes read
no ex
09:28:04.612 [DBG] Sent 31 bytes
no ex
09:28:07.565 [DBG] 32 bytes read
no ex
09:28:07.565 [DBG] Sent 31 bytes
no ex
09:28:08.654 [DBG] Sent 184 bytes
no ex
09:28:10.577 [DBG] 32 bytes read
no ex
09:28:10.577 [DBG] Sent 31 bytes
no ex
09:28:13.562 [DBG] 32 bytes read
no ex
09:28:13.562 [DBG] Sent 31 bytes
no ex
09:28:13.656 [DBG] Sent 184 bytes
no ex
09:28:16.577 [DBG] 32 bytes read
no ex
09:28:16.577 [DBG] Sent 31 bytes
no ex
09:28:18.655 [DBG] Sent 184 bytes
no ex
09:28:19.576 [DBG] 32 bytes read
no ex
09:28:19.576 [DBG] Sent 31 bytes
no ex
09:28:22.570 [DBG] 32 bytes read
no ex
09:28:22.570 [DBG] Sent 31 bytes
no ex
09:28:23.657 [DBG] Sent 184 bytes
no ex
09:28:25.564 [DBG] 32 bytes read
no ex
09:28:25.564 [DBG] Sent 31 bytes
no ex
09:28:28.561 [DBG] 32 bytes read
no ex
09:28:28.561 [DBG] Sent 31 bytes
no ex
09:28:28.650 [DBG] Sent 184 bytes
no ex
09:28:31.601 [DBG] 32 bytes read
no ex
09:28:31.601 [DBG] Sent 31 bytes
no ex
09:28:33.655 [DBG] Sent 184 bytes
no ex
09:28:34.577 [DBG] 32 bytes read
no ex
09:28:34.577 [DBG] Sent 31 bytes
no ex
09:28:37.578 [DBG] 32 bytes read
no ex
09:28:37.578 [DBG] Sent 31 bytes
no ex
09:28:38.663 [DBG] Sent 184 bytes
no ex
09:28:40.607 [DBG] 32 bytes read
no ex
09:28:40.607 [DBG] Sent 31 bytes
no ex
09:28:43.582 [DBG] 32 bytes read
no ex
09:28:43.583 [DBG] Sent 31 bytes
no ex
09:28:43.653 [DBG] Sent 184 bytes
no ex
09:28:46.565 [DBG] 32 bytes read
no ex
09:28:46.565 [DBG] Sent 31 bytes
no ex
09:28:48.651 [DBG] Sent 184 bytes
no ex
09:28:49.845 [DBG] 32 bytes read
no ex
09:28:49.845 [DBG] Sent 31 bytes
no ex
09:28:52.717 [DBG] 32 bytes read
no ex
09:28:52.717 [DBG] Sent 31 bytes
no ex
09:28:53.659 [DBG] Sent 184 bytes
no ex
09:28:55.679 [DBG] 32 bytes read
no ex
09:28:55.679 [DBG] Sent 31 bytes
no ex
09:28:58.655 [DBG] Sent 184 bytes
no ex
09:28:58.737 [DBG] 32 bytes read
no ex
09:28:58.737 [DBG] Sent 31 bytes
no ex
09:29:01.797 [DBG] 32 bytes read
no ex
09:29:01.797 [DBG] Sent 31 bytes
no ex
09:29:03.656 [DBG] Sent 184 bytes
no ex
09:29:04.574 [DBG] 32 bytes read
no ex
09:29:04.575 [DBG] Sent 31 bytes
no ex
09:29:07.569 [DBG] 32 bytes read
no ex
09:29:07.569 [DBG] Sent 31 bytes
no ex
09:29:08.661 [DBG] Sent 184 bytes
no ex
09:29:10.572 [DBG] 32 bytes read
no ex
09:29:10.572 [DBG] Sent 31 bytes
no ex
09:29:13.570 [DBG] 32 bytes read
no ex
09:29:13.570 [DBG] Sent 31 bytes
no ex
09:29:13.663 [DBG] Sent 184 bytes
no ex
09:29:16.578 [DBG] 32 bytes read
no ex
09:29:16.578 [DBG] Sent 31 bytes
no ex
09:29:18.668 [DBG] Sent 184 bytes
no ex
09:29:19.648 [DBG] 32 bytes read
no ex
09:29:19.649 [DBG] Sent 31 bytes
no ex
09:29:22.564 [DBG] 32 bytes read
no ex
09:29:22.564 [DBG] Sent 31 bytes
no ex
09:29:23.653 [DBG] Sent 184 bytes
no ex
09:29:25.570 [DBG] 32 bytes read
no ex
09:29:25.570 [DBG] Sent 31 bytes
no ex
09:29:28.561 [DBG] 32 bytes read
no ex
09:29:28.561 [DBG] Sent 31 bytes
no ex
09:29:28.654 [DBG] Sent 184 bytes
no ex
09:29:31.575 [DBG] 32 bytes read
no ex
09:29:31.575 [DBG] Sent 31 bytes
no ex
09:29:33.660 [DBG] Sent 184 bytes
no ex
09:29:34.588 [DBG] 33 bytes read
no ex
09:29:34.589 [DBG] Sent 32 bytes
no ex
09:29:37.569 [DBG] 33 bytes read
no ex
09:29:37.570 [DBG] Sent 32 bytes
no ex
09:29:38.663 [DBG] Sent 184 bytes
no ex
09:29:40.569 [DBG] 33 bytes read
no ex
09:29:40.569 [DBG] Sent 32 bytes
no ex
09:29:43.593 [DBG] 33 bytes read
no ex
09:29:43.593 [DBG] Sent 32 bytes
no ex
09:29:43.667 [DBG] Sent 184 bytes
no ex
09:29:46.561 [DBG] 33 bytes read
no ex
09:29:46.561 [DBG] Sent 32 bytes
no ex
09:29:48.665 [DBG] Sent 184 bytes
no ex
09:29:49.581 [DBG] 33 bytes read
no ex
09:29:49.582 [DBG] Sent 32 bytes
no ex
09:29:52.564 [DBG] 33 bytes read
no ex
09:29:52.564 [DBG] Sent 32 bytes
no ex
09:29:53.668 [DBG] Sent 184 bytes
no ex
09:29:55.561 [DBG] 33 bytes read
no ex
09:29:55.562 [DBG] Sent 32 bytes
no ex
09:29:58.579 [DBG] 33 bytes read
no ex
09:29:58.579 [DBG] Sent 32 bytes
no ex
09:29:58.672 [DBG] Sent 184 bytes
no ex
09:30:01.564 [DBG] 33 bytes read
no ex
09:30:01.564 [DBG] Sent 32 bytes
no ex
09:30:03.665 [DBG] Sent 184 bytes
no ex
09:30:04.578 [DBG] 33 bytes read
no ex
09:30:04.579 [DBG] Sent 32 bytes
no ex
09:30:07.561 [DBG] 33 bytes read
no ex
09:30:07.561 [DBG] Sent 32 bytes
no ex
09:30:08.671 [DBG] Sent 184 bytes
no ex
09:30:10.577 [DBG] 33 bytes read
no ex
09:30:10.577 [DBG] Sent 32 bytes
no ex
09:30:13.563 [DBG] 33 bytes read
no ex
09:30:13.563 [DBG] Sent 32 bytes
no ex
09:30:13.671 [DBG] Sent 184 bytes
no ex
09:30:16.581 [DBG] 33 bytes read
no ex
09:30:16.582 [DBG] Sent 32 bytes
no ex
09:30:18.660 [DBG] Sent 184 bytes
no ex
09:30:19.572 [DBG] 33 bytes read
no ex
09:30:19.572 [DBG] Sent 32 bytes
no ex
09:30:22.582 [DBG] 33 bytes read
no ex
09:30:22.582 [DBG] Sent 32 bytes
no ex
09:30:23.677 [DBG] Sent 184 bytes
no ex
09:30:25.567 [DBG] 33 bytes read
no ex
09:30:25.567 [DBG] Sent 32 bytes
no ex
09:30:28.561 [DBG] 33 bytes read
no ex
09:30:28.561 [DBG] Sent 32 bytes
no ex
09:30:28.666 [DBG] Sent 184 bytes
no ex
09:30:29.673 [DBG] Sent 75 bytes
no ex
09:30:31.569 [DBG] 33 bytes read
no ex
09:30:31.569 [DBG] Sent 32 bytes
no ex
09:30:33.670 [DBG] Sent 184 bytes
no ex
09:30:34.593 [DBG] 33 bytes read
no ex
09:30:34.593 [DBG] Sent 32 bytes
no ex
09:30:37.638 [DBG] 33 bytes read
no ex
09:30:37.638 [DBG] Sent 32 bytes
no ex
09:30:38.671 [DBG] Sent 184 bytes
no ex
09:30:40.569 [DBG] 33 bytes read
no ex
09:30:40.569 [DBG] Sent 32 bytes
no ex
09:30:43.579 [DBG] 33 bytes read
no ex
09:30:43.580 [DBG] Sent 32 bytes
no ex
09:30:43.670 [DBG] Sent 184 bytes
no ex
09:30:46.573 [DBG] 33 bytes read
no ex
09:30:46.573 [DBG] Sent 32 bytes
no ex
09:30:48.677 [DBG] Sent 184 bytes
no ex
09:30:49.593 [DBG] 33 bytes read
no ex
09:30:49.593 [DBG] Sent 32 bytes
no ex
09:30:52.581 [DBG] 33 bytes read
no ex
09:30:52.582 [DBG] Sent 32 bytes
no ex
09:30:53.663 [DBG] Sent 184 bytes
no ex
09:30:55.625 [DBG] 33 bytes read
no ex
09:30:55.626 [DBG] Sent 32 bytes
no ex
09:30:58.566 [DBG] 33 bytes read
no ex
09:30:58.567 [DBG] Sent 32 bytes
no ex
09:30:58.675 [DBG] Sent 184 bytes
no ex
09:31:01.627 [DBG] 33 bytes read
no ex
09:31:01.627 [DBG] Sent 32 bytes
no ex
09:31:03.677 [DBG] Sent 184 bytes
no ex
09:31:04.568 [DBG] 33 bytes read
no ex
09:31:04.568 [DBG] Sent 32 bytes
no ex
09:31:07.572 [DBG] 33 bytes read
no ex
09:31:07.573 [DBG] Sent 32 bytes
no ex
09:31:08.682 [DBG] Sent 184 bytes
no ex
09:31:10.569 [DBG] 33 bytes read
no ex
09:31:10.569 [DBG] Sent 32 bytes
no ex
09:31:13.681 [DBG] 33 bytes read
no ex
09:31:13.682 [DBG] Sent 32 bytes
no ex
09:31:13.685 [DBG] Sent 184 bytes
no ex
09:31:16.574 [DBG] 33 bytes read
no ex
09:31:16.574 [DBG] Sent 32 bytes
no ex
09:31:18.676 [DBG] Sent 184 bytes
no ex
09:31:19.566 [DBG] 33 bytes read
no ex
09:31:19.566 [DBG] Sent 32 bytes
no ex
09:31:22.610 [DBG] 33 bytes read
no ex
09:31:22.611 [DBG] Sent 32 bytes
no ex
09:31:23.677 [DBG] Sent 184 bytes
no ex
09:31:25.566 [DBG] 33 bytes read
no ex
09:31:25.566 [DBG] Sent 32 bytes
no ex
09:31:28.569 [DBG] 33 bytes read
no ex
09:31:28.569 [DBG] Sent 32 bytes
no ex
09:31:28.680 [DBG] Sent 184 bytes
no ex
09:31:31.565 [DBG] 33 bytes read
no ex
09:31:31.565 [DBG] Sent 32 bytes
no ex
09:31:33.684 [DBG] Sent 184 bytes
no ex
09:31:34.569 [DBG] 33 bytes read
no ex
09:31:34.570 [DBG] Sent 32 bytes
no ex
09:31:36.090 [INF] RunScan: 0
09:31:36.090 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:31)","Data":null,"DataObj":null}
09:31:37.566 [DBG] 33 bytes read
no ex
09:31:37.566 [DBG] Sent 32 bytes
no ex
09:31:38.678 [DBG] Sent 184 bytes
no ex
09:31:40.581 [DBG] 33 bytes read
no ex
09:31:40.582 [DBG] Sent 32 bytes
no ex
09:31:43.567 [DBG] 33 bytes read
no ex
09:31:43.567 [DBG] Sent 32 bytes
no ex
09:31:43.681 [DBG] Sent 184 bytes
no ex
09:31:46.588 [DBG] 33 bytes read
no ex
09:31:46.588 [DBG] Sent 32 bytes
no ex
09:31:48.683 [DBG] Sent 184 bytes
no ex
09:31:49.576 [DBG] 33 bytes read
no ex
09:31:49.576 [DBG] Sent 32 bytes
no ex
09:31:52.566 [DBG] 33 bytes read
no ex
09:31:52.567 [DBG] Sent 32 bytes
no ex
09:31:53.689 [DBG] Sent 184 bytes
no ex
09:31:55.592 [DBG] 33 bytes read
no ex
09:31:55.592 [DBG] Sent 32 bytes
no ex
09:31:58.553 [DBG] 33 bytes read
no ex
09:31:58.553 [DBG] Sent 32 bytes
no ex
09:31:58.683 [DBG] Sent 184 bytes
no ex
09:32:01.578 [DBG] 33 bytes read
no ex
09:32:01.578 [DBG] Sent 32 bytes
no ex
09:32:03.675 [DBG] Sent 184 bytes
no ex
09:32:04.575 [DBG] 33 bytes read
no ex
09:32:04.575 [DBG] Sent 32 bytes
no ex
09:32:07.572 [DBG] 33 bytes read
no ex
09:32:07.572 [DBG] Sent 32 bytes
no ex
09:32:08.678 [DBG] Sent 184 bytes
no ex
09:32:10.568 [DBG] 33 bytes read
no ex
09:32:10.568 [DBG] Sent 32 bytes
no ex
09:32:13.566 [DBG] 33 bytes read
no ex
09:32:13.567 [DBG] Sent 32 bytes
no ex
09:32:13.686 [DBG] Sent 184 bytes
no ex
09:32:16.569 [DBG] 33 bytes read
no ex
09:32:16.569 [DBG] Sent 32 bytes
no ex
09:32:18.691 [DBG] Sent 184 bytes
no ex
09:32:19.605 [DBG] 33 bytes read
no ex
09:32:19.605 [DBG] Sent 32 bytes
no ex
09:32:22.597 [DBG] 33 bytes read
no ex
09:32:22.598 [DBG] Sent 32 bytes
no ex
09:32:23.679 [DBG] Sent 184 bytes
no ex
09:32:25.562 [DBG] 33 bytes read
no ex
09:32:25.562 [DBG] Sent 32 bytes
no ex
09:32:28.558 [DBG] 33 bytes read
no ex
09:32:28.559 [DBG] Sent 32 bytes
no ex
09:32:28.681 [DBG] Sent 184 bytes
no ex
09:32:31.572 [DBG] 33 bytes read
no ex
09:32:31.572 [DBG] Sent 32 bytes
no ex
09:32:33.683 [DBG] Sent 184 bytes
no ex
09:32:34.573 [DBG] 33 bytes read
no ex
09:32:34.574 [DBG] Sent 32 bytes
no ex
09:32:37.570 [DBG] 33 bytes read
no ex
09:32:37.570 [DBG] Sent 32 bytes
no ex
09:32:37.751 [INF] RunHandleWinLoss: 0
09:32:37.751 [INF] CalculateResult: 20220808
09:32:37.751 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
09:32:38.689 [DBG] Sent 184 bytes
no ex
09:32:40.572 [DBG] 33 bytes read
no ex
09:32:40.573 [DBG] Sent 32 bytes
no ex
09:32:43.574 [DBG] 33 bytes read
no ex
09:32:43.574 [DBG] Sent 32 bytes
no ex
09:32:43.693 [DBG] Sent 184 bytes
no ex
09:32:46.566 [DBG] 33 bytes read
no ex
09:32:46.566 [DBG] Sent 32 bytes
no ex
09:32:48.691 [DBG] Sent 184 bytes
no ex
09:32:49.570 [DBG] 33 bytes read
no ex
09:32:49.570 [DBG] Sent 32 bytes
no ex
09:32:52.565 [DBG] 33 bytes read
no ex
09:32:52.566 [DBG] Sent 32 bytes
no ex
09:32:53.694 [DBG] Sent 184 bytes
no ex
09:32:55.570 [DBG] 33 bytes read
no ex
09:32:55.570 [DBG] Sent 32 bytes
no ex
09:32:58.579 [DBG] 33 bytes read
no ex
09:32:58.579 [DBG] Sent 32 bytes
no ex
09:32:58.683 [DBG] Sent 184 bytes
no ex
09:33:01.578 [DBG] 33 bytes read
no ex
09:33:01.579 [DBG] Sent 32 bytes
no ex
09:33:03.690 [DBG] Sent 184 bytes
no ex
09:33:04.569 [DBG] 33 bytes read
no ex
09:33:04.570 [DBG] Sent 32 bytes
no ex
09:33:07.568 [DBG] 33 bytes read
no ex
09:33:07.568 [DBG] Sent 32 bytes
no ex
09:33:08.690 [DBG] Sent 184 bytes
no ex
09:33:10.565 [DBG] 33 bytes read
no ex
09:33:10.565 [DBG] Sent 32 bytes
no ex
09:33:13.619 [DBG] 33 bytes read
no ex
09:33:13.619 [DBG] Sent 32 bytes
no ex
09:33:13.700 [DBG] Sent 184 bytes
no ex
09:33:16.563 [DBG] 33 bytes read
no ex
09:33:16.563 [DBG] Sent 32 bytes
no ex
09:33:18.702 [DBG] Sent 184 bytes
no ex
09:33:19.573 [DBG] 33 bytes read
no ex
09:33:19.574 [DBG] Sent 32 bytes
no ex
09:33:22.567 [DBG] 33 bytes read
no ex
09:33:22.568 [DBG] Sent 32 bytes
no ex
09:33:23.689 [DBG] Sent 184 bytes
no ex
09:33:25.585 [DBG] 33 bytes read
no ex
09:33:25.585 [DBG] Sent 32 bytes
no ex
09:33:28.598 [DBG] 33 bytes read
no ex
09:33:28.598 [DBG] Sent 32 bytes
no ex
09:33:28.691 [DBG] Sent 184 bytes
no ex
09:33:31.573 [DBG] 33 bytes read
no ex
09:33:31.573 [DBG] Sent 32 bytes
no ex
09:33:33.693 [DBG] Sent 184 bytes
no ex
09:33:34.614 [DBG] 33 bytes read
no ex
09:33:34.614 [DBG] Sent 32 bytes
no ex
09:33:37.573 [DBG] 33 bytes read
no ex
09:33:37.573 [DBG] Sent 32 bytes
no ex
09:33:38.687 [DBG] Sent 184 bytes
no ex
09:33:40.565 [DBG] 33 bytes read
no ex
09:33:40.565 [DBG] Sent 32 bytes
no ex
09:33:43.624 [DBG] 33 bytes read
no ex
09:33:43.625 [DBG] Sent 32 bytes
no ex
09:33:43.690 [DBG] Sent 184 bytes
no ex
09:33:46.565 [DBG] 33 bytes read
no ex
09:33:46.565 [DBG] Sent 32 bytes
no ex
09:33:48.703 [DBG] Sent 184 bytes
no ex
09:33:49.567 [DBG] 33 bytes read
no ex
09:33:49.567 [DBG] Sent 32 bytes
no ex
09:33:52.561 [DBG] 33 bytes read
no ex
09:33:52.561 [DBG] Sent 32 bytes
no ex
09:33:53.688 [DBG] Sent 184 bytes
no ex
09:33:55.607 [DBG] 33 bytes read
no ex
09:33:55.608 [DBG] Sent 32 bytes
no ex
09:33:58.565 [DBG] 33 bytes read
no ex
09:33:58.565 [DBG] Sent 32 bytes
no ex
09:33:58.690 [DBG] Sent 184 bytes
no ex
09:34:01.568 [DBG] 33 bytes read
no ex
09:34:01.568 [DBG] Sent 32 bytes
no ex
09:34:03.699 [DBG] Sent 184 bytes
no ex
09:34:04.613 [DBG] 33 bytes read
no ex
09:34:04.614 [DBG] Sent 32 bytes
no ex
09:34:07.566 [DBG] 33 bytes read
no ex
09:34:07.566 [DBG] Sent 32 bytes
no ex
09:34:08.698 [DBG] Sent 184 bytes
no ex
09:34:10.565 [DBG] 33 bytes read
no ex
09:34:10.565 [DBG] Sent 32 bytes
no ex
09:34:13.571 [DBG] 33 bytes read
no ex
09:34:13.571 [DBG] Sent 32 bytes
no ex
09:34:13.703 [DBG] Sent 184 bytes
no ex
09:34:16.573 [DBG] 33 bytes read
no ex
09:34:16.573 [DBG] Sent 32 bytes
no ex
09:34:18.711 [DBG] Sent 184 bytes
no ex
09:34:19.581 [DBG] 33 bytes read
no ex
09:34:19.582 [DBG] Sent 32 bytes
no ex
09:34:22.585 [DBG] 33 bytes read
no ex
09:34:22.585 [DBG] Sent 32 bytes
no ex
09:34:23.699 [DBG] Sent 184 bytes
no ex
09:34:25.585 [DBG] 33 bytes read
no ex
09:34:25.585 [DBG] Sent 32 bytes
no ex
09:34:28.587 [DBG] 33 bytes read
no ex
09:34:28.587 [DBG] Sent 32 bytes
no ex
09:34:28.700 [DBG] Sent 184 bytes
no ex
09:34:31.569 [DBG] 33 bytes read
no ex
09:34:31.570 [DBG] Sent 32 bytes
no ex
09:34:33.702 [DBG] Sent 184 bytes
no ex
09:34:34.557 [DBG] 33 bytes read
no ex
09:34:34.557 [DBG] Sent 32 bytes
no ex
09:34:37.570 [DBG] 33 bytes read
no ex
09:34:37.571 [DBG] Sent 32 bytes
no ex
09:34:38.706 [DBG] Sent 184 bytes
no ex
09:34:40.586 [DBG] 33 bytes read
no ex
09:34:40.587 [DBG] Sent 32 bytes
no ex
09:34:43.581 [DBG] 33 bytes read
no ex
09:34:43.581 [DBG] Sent 32 bytes
no ex
09:34:43.712 [DBG] Sent 184 bytes
no ex
09:34:46.570 [DBG] 33 bytes read
no ex
09:34:46.570 [DBG] Sent 32 bytes
no ex
09:34:48.704 [DBG] Sent 184 bytes
no ex
09:34:49.569 [DBG] 33 bytes read
no ex
09:34:49.569 [DBG] Sent 32 bytes
no ex
09:34:52.557 [DBG] 33 bytes read
no ex
09:34:52.557 [DBG] Sent 32 bytes
no ex
09:34:53.707 [DBG] Sent 184 bytes
no ex
09:34:55.568 [DBG] 33 bytes read
no ex
09:34:55.568 [DBG] Sent 32 bytes
no ex
09:34:58.569 [DBG] 33 bytes read
no ex
09:34:58.569 [DBG] Sent 32 bytes
no ex
09:34:58.713 [DBG] Sent 184 bytes
no ex
09:35:01.601 [DBG] 33 bytes read
no ex
09:35:01.601 [DBG] Sent 32 bytes
no ex
09:35:03.708 [DBG] Sent 184 bytes
no ex
09:35:04.565 [DBG] 33 bytes read
no ex
09:35:04.565 [DBG] Sent 32 bytes
no ex
09:35:07.565 [DBG] 33 bytes read
no ex
09:35:07.565 [DBG] Sent 32 bytes
no ex
09:35:08.703 [DBG] Sent 184 bytes
no ex
09:35:10.580 [DBG] 33 bytes read
no ex
09:35:10.581 [DBG] Sent 32 bytes
no ex
09:35:13.571 [DBG] 33 bytes read
no ex
09:35:13.571 [DBG] Sent 32 bytes
no ex
09:35:13.705 [DBG] Sent 184 bytes
no ex
09:35:14.712 [DBG] Sent 73 bytes
no ex
09:35:16.581 [DBG] 33 bytes read
no ex
09:35:16.581 [DBG] Sent 32 bytes
no ex
09:35:18.706 [DBG] Sent 184 bytes
no ex
09:35:19.574 [DBG] 33 bytes read
no ex
09:35:19.575 [DBG] Sent 32 bytes
no ex
09:35:22.557 [DBG] 33 bytes read
no ex
09:35:22.558 [DBG] Sent 32 bytes
no ex
09:35:23.708 [DBG] Sent 184 bytes
no ex
09:35:25.644 [DBG] 33 bytes read
no ex
09:35:25.645 [DBG] Sent 32 bytes
no ex
09:35:28.665 [DBG] 33 bytes read
no ex
09:35:28.665 [DBG] Sent 32 bytes
no ex
09:35:28.712 [DBG] Sent 184 bytes
no ex
09:35:31.755 [DBG] 33 bytes read
no ex
09:35:31.755 [DBG] Sent 32 bytes
no ex
09:35:33.715 [DBG] Sent 184 bytes
no ex
09:35:34.702 [DBG] 33 bytes read
no ex
09:35:34.702 [DBG] Sent 32 bytes
no ex
09:35:37.634 [DBG] 33 bytes read
no ex
09:35:37.634 [DBG] Sent 32 bytes
no ex
09:35:38.718 [DBG] Sent 184 bytes
no ex
09:35:40.601 [DBG] 33 bytes read
no ex
09:35:40.601 [DBG] Sent 32 bytes
no ex
09:35:43.699 [DBG] 33 bytes read
no ex
09:35:43.699 [DBG] Sent 32 bytes
no ex
09:35:43.719 [DBG] Sent 184 bytes
no ex
09:35:46.636 [DBG] 33 bytes read
no ex
09:35:46.636 [DBG] Sent 32 bytes
no ex
09:35:48.714 [DBG] Sent 184 bytes
no ex
09:35:49.654 [DBG] 33 bytes read
no ex
09:35:49.654 [DBG] Sent 32 bytes
no ex
09:35:52.886 [DBG] 33 bytes read
no ex
09:35:52.886 [DBG] Sent 32 bytes
no ex
09:35:53.720 [DBG] Sent 184 bytes
no ex
09:35:55.649 [DBG] 33 bytes read
no ex
09:35:55.650 [DBG] Sent 32 bytes
no ex
09:35:58.708 [DBG] Sent 184 bytes
no ex
09:36:00.672 [INF] 8888 bot: 2, peer: 0, ccu: 0
09:36:00.672 [DBG] Sent 4 bytes
no ex
09:36:00.672 [DBG] Swallowing ObjectDisposedException
System.ObjectDisposedException: Cannot access a disposed object.
Object name: 'System.Net.Sockets.NetworkStream'.
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
09:36:03.369 [DBG] Client connected from 127.0.0.1:46920
no ex
09:36:03.370 [DBG] 838 bytes read
no ex
09:36:03.370 [DBG] Building Hybi-14 Response
no ex
09:36:03.370 [DBG] Sent 129 bytes
no ex
09:36:03.536 [DBG] 33 bytes read
no ex
09:36:03.536 [DBG] Sent 32 bytes
no ex
09:36:03.711 [DBG] Sent 184 bytes
no ex
09:36:04.720 [DBG] Sent 74 bytes
no ex
09:36:06.672 [DBG] 33 bytes read
no ex
09:36:06.672 [DBG] Sent 32 bytes
no ex
09:36:08.709 [DBG] Sent 184 bytes
no ex
09:36:09.646 [DBG] 33 bytes read
no ex
09:36:09.646 [DBG] Sent 32 bytes
no ex
09:36:12.679 [DBG] 33 bytes read
no ex
09:36:12.679 [DBG] Sent 32 bytes
no ex
09:36:13.717 [DBG] Sent 184 bytes
no ex
09:36:15.646 [DBG] 33 bytes read
no ex
09:36:15.647 [DBG] Sent 32 bytes
no ex
09:36:18.650 [DBG] 33 bytes read
no ex
09:36:18.650 [DBG] Sent 32 bytes
no ex
09:36:18.710 [DBG] Sent 184 bytes
no ex
09:36:21.658 [DBG] 33 bytes read
no ex
09:36:21.658 [DBG] Sent 32 bytes
no ex
09:36:23.718 [DBG] Sent 184 bytes
no ex
09:36:24.678 [DBG] 33 bytes read
no ex
09:36:24.679 [DBG] Sent 32 bytes
no ex
09:36:26.726 [DBG] Sent 72 bytes
no ex
09:36:27.634 [DBG] 33 bytes read
no ex
09:36:27.634 [DBG] Sent 32 bytes
no ex
09:36:28.710 [DBG] Sent 184 bytes
no ex
09:36:30.667 [DBG] 33 bytes read
no ex
09:36:30.667 [DBG] Sent 32 bytes
no ex
09:36:33.642 [DBG] 33 bytes read
no ex
09:36:33.643 [DBG] Sent 32 bytes
no ex
09:36:33.727 [DBG] Sent 184 bytes
no ex
09:36:36.090 [INF] RunScan: 0
09:36:36.090 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:36)","Data":null,"DataObj":null}
09:36:36.615 [DBG] 33 bytes read
no ex
09:36:36.615 [DBG] Sent 32 bytes
no ex
09:36:38.719 [DBG] Sent 184 bytes
no ex
09:36:39.666 [DBG] 33 bytes read
no ex
09:36:39.666 [DBG] Sent 32 bytes
no ex
09:36:42.678 [DBG] 33 bytes read
no ex
09:36:42.678 [DBG] Sent 32 bytes
no ex
09:36:43.720 [DBG] Sent 184 bytes
no ex
09:36:45.642 [DBG] 33 bytes read
no ex
09:36:45.642 [DBG] Sent 32 bytes
no ex
09:36:48.649 [DBG] 33 bytes read
no ex
09:36:48.650 [DBG] Sent 32 bytes
no ex
09:36:48.727 [DBG] Sent 184 bytes
no ex
09:36:50.229 [INF] 8888 bot: 2, peer: 1, ccu: 0
09:36:51.666 [DBG] 33 bytes read
no ex
09:36:51.666 [DBG] Sent 32 bytes
no ex
09:36:53.713 [DBG] Sent 184 bytes
no ex
09:36:54.646 [DBG] 33 bytes read
no ex
09:36:54.646 [DBG] Sent 32 bytes
no ex
09:36:57.632 [DBG] 33 bytes read
no ex
09:36:57.632 [DBG] Sent 32 bytes
no ex
09:36:58.721 [DBG] Sent 184 bytes
no ex
09:37:00.623 [DBG] 33 bytes read
no ex
09:37:00.623 [DBG] Sent 32 bytes
no ex
09:37:03.674 [DBG] 33 bytes read
no ex
09:37:03.675 [DBG] Sent 32 bytes
no ex
09:37:03.728 [DBG] Sent 184 bytes
no ex
09:37:06.625 [DBG] 33 bytes read
no ex
09:37:06.625 [DBG] Sent 32 bytes
no ex
09:37:08.718 [DBG] Sent 184 bytes
no ex
09:37:09.664 [DBG] 33 bytes read
no ex
09:37:09.664 [DBG] Sent 32 bytes
no ex
09:37:12.638 [DBG] 33 bytes read
no ex
09:37:12.638 [DBG] Sent 32 bytes
no ex
09:37:13.727 [DBG] Sent 184 bytes
no ex
09:37:15.649 [DBG] 33 bytes read
no ex
09:37:15.649 [DBG] Sent 32 bytes
no ex
09:37:18.621 [DBG] 33 bytes read
no ex
09:37:18.621 [DBG] Sent 32 bytes
no ex
09:37:18.729 [DBG] Sent 184 bytes
no ex
09:37:21.634 [DBG] 33 bytes read
no ex
09:37:21.634 [DBG] Sent 32 bytes
no ex
09:37:23.719 [DBG] Sent 184 bytes
no ex
09:37:24.662 [DBG] 33 bytes read
no ex
09:37:24.662 [DBG] Sent 32 bytes
no ex
09:37:27.655 [DBG] 33 bytes read
no ex
09:37:27.656 [DBG] Sent 32 bytes
no ex
09:37:28.724 [DBG] Sent 184 bytes
no ex
09:37:30.688 [DBG] 33 bytes read
no ex
09:37:30.688 [DBG] Sent 32 bytes
no ex
09:37:33.650 [DBG] 33 bytes read
no ex
09:37:33.650 [DBG] Sent 32 bytes
no ex
09:37:33.728 [DBG] Sent 184 bytes
no ex
09:37:36.654 [DBG] 33 bytes read
no ex
09:37:36.654 [DBG] Sent 32 bytes
no ex
09:37:37.752 [INF] RunHandleWinLoss: 0
09:37:37.752 [INF] CalculateResult: 20220808
09:37:37.752 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
09:37:38.725 [DBG] Sent 184 bytes
no ex
09:37:39.555 [DBG] 33 bytes read
no ex
09:37:39.555 [DBG] Sent 32 bytes
no ex
09:37:42.629 [DBG] 33 bytes read
no ex
09:37:42.630 [DBG] Sent 32 bytes
no ex
09:37:43.728 [DBG] Sent 184 bytes
no ex
09:37:45.702 [DBG] 33 bytes read
no ex
09:37:45.702 [DBG] Sent 32 bytes
no ex
09:37:48.640 [DBG] 33 bytes read
no ex
09:37:48.640 [DBG] Sent 32 bytes
no ex
09:37:48.729 [DBG] Sent 184 bytes
no ex
09:37:51.666 [DBG] 33 bytes read
no ex
09:37:51.666 [DBG] Sent 32 bytes
no ex
09:37:53.730 [DBG] Sent 184 bytes
no ex
09:37:54.614 [DBG] 33 bytes read
no ex
09:37:54.614 [DBG] Sent 32 bytes
no ex
09:37:57.642 [DBG] 33 bytes read
no ex
09:37:57.643 [DBG] Sent 32 bytes
no ex
09:37:58.731 [DBG] Sent 184 bytes
no ex
09:38:00.650 [DBG] 33 bytes read
no ex
09:38:00.650 [DBG] Sent 32 bytes
no ex
09:38:03.648 [DBG] 33 bytes read
no ex
09:38:03.648 [DBG] Sent 32 bytes
no ex
09:38:03.734 [DBG] Sent 184 bytes
no ex
09:38:06.614 [DBG] 33 bytes read
no ex
09:38:06.614 [DBG] Sent 32 bytes
no ex
09:38:08.728 [DBG] Sent 184 bytes
no ex
09:38:09.654 [DBG] 33 bytes read
no ex
09:38:09.654 [DBG] Sent 32 bytes
no ex
09:38:12.642 [DBG] 33 bytes read
no ex
09:38:12.642 [DBG] Sent 32 bytes
no ex
09:38:13.740 [DBG] Sent 184 bytes
no ex
09:38:15.678 [DBG] 33 bytes read
no ex
09:38:15.678 [DBG] Sent 32 bytes
no ex
09:38:18.626 [DBG] 33 bytes read
no ex
09:38:18.626 [DBG] Sent 32 bytes
no ex
09:38:18.732 [DBG] Sent 184 bytes
no ex
09:38:21.650 [DBG] 33 bytes read
no ex
09:38:21.650 [DBG] Sent 32 bytes
no ex
09:38:23.737 [DBG] Sent 184 bytes
no ex
09:38:24.711 [DBG] 33 bytes read
no ex
09:38:24.712 [DBG] Sent 32 bytes
no ex
09:38:27.646 [DBG] 33 bytes read
no ex
09:38:27.646 [DBG] Sent 32 bytes
no ex
09:38:28.739 [DBG] Sent 184 bytes
no ex
09:38:30.655 [DBG] 33 bytes read
no ex
09:38:30.655 [DBG] Sent 32 bytes
no ex
09:38:33.740 [DBG] Sent 184 bytes
no ex
09:38:33.747 [DBG] 33 bytes read
no ex
09:38:33.747 [DBG] Sent 32 bytes
no ex
09:38:36.664 [DBG] 33 bytes read
no ex
09:38:36.664 [DBG] Sent 32 bytes
no ex
09:38:37.734 [DBG] Sent 72 bytes
no ex
09:38:38.742 [DBG] Sent 184 bytes
no ex
09:38:39.654 [DBG] 33 bytes read
no ex
09:38:39.654 [DBG] Sent 32 bytes
no ex
09:38:42.622 [DBG] 33 bytes read
no ex
09:38:42.622 [DBG] Sent 32 bytes
no ex
09:38:43.731 [DBG] Sent 184 bytes
no ex
09:38:45.609 [DBG] 33 bytes read
no ex
09:38:45.610 [DBG] Sent 32 bytes
no ex
09:38:48.617 [DBG] 33 bytes read
no ex
09:38:48.617 [DBG] Sent 32 bytes
no ex
09:38:48.738 [DBG] Sent 184 bytes
no ex
09:38:51.734 [DBG] 33 bytes read
no ex
09:38:51.734 [DBG] Sent 32 bytes
no ex
09:38:53.741 [DBG] Sent 184 bytes
no ex
09:38:54.629 [DBG] 33 bytes read
no ex
09:38:54.630 [DBG] Sent 32 bytes
no ex
09:38:57.617 [DBG] 33 bytes read
no ex
09:38:57.618 [DBG] Sent 32 bytes
no ex
09:38:58.729 [DBG] Sent 184 bytes
no ex
09:39:00.662 [DBG] 33 bytes read
no ex
09:39:00.662 [DBG] Sent 32 bytes
no ex
09:39:03.642 [DBG] 33 bytes read
no ex
09:39:03.642 [DBG] Sent 32 bytes
no ex
09:39:03.736 [DBG] Sent 184 bytes
no ex
09:39:06.678 [DBG] 33 bytes read
no ex
09:39:06.679 [DBG] Sent 32 bytes
no ex
09:39:08.741 [DBG] Sent 184 bytes
no ex
09:39:09.650 [DBG] 33 bytes read
no ex
09:39:09.650 [DBG] Sent 32 bytes
no ex
09:39:12.630 [DBG] 33 bytes read
no ex
09:39:12.630 [DBG] Sent 32 bytes
no ex
09:39:13.742 [DBG] Sent 184 bytes
no ex
09:39:15.706 [DBG] 33 bytes read
no ex
09:39:15.706 [DBG] Sent 32 bytes
no ex
09:39:18.678 [DBG] 33 bytes read
no ex
09:39:18.678 [DBG] Sent 32 bytes
no ex
09:39:18.747 [DBG] Sent 184 bytes
no ex
09:39:21.663 [DBG] 33 bytes read
no ex
09:39:21.663 [DBG] Sent 32 bytes
no ex
09:39:23.735 [DBG] Sent 184 bytes
no ex
09:39:24.637 [DBG] 33 bytes read
no ex
09:39:24.637 [DBG] Sent 32 bytes
no ex
09:39:27.642 [DBG] 33 bytes read
no ex
09:39:27.642 [DBG] Sent 32 bytes
no ex
09:39:28.740 [DBG] Sent 184 bytes
no ex
09:39:30.642 [DBG] 33 bytes read
no ex
09:39:30.642 [DBG] Sent 32 bytes
no ex
09:39:33.726 [DBG] 33 bytes read
no ex
09:39:33.726 [DBG] Sent 32 bytes
no ex
09:39:33.749 [DBG] Sent 184 bytes
no ex
09:39:36.670 [DBG] 33 bytes read
no ex
09:39:36.671 [DBG] Sent 32 bytes
no ex
09:39:38.748 [DBG] Sent 184 bytes
no ex
09:39:39.676 [DBG] 33 bytes read
no ex
09:39:39.676 [DBG] Sent 32 bytes
no ex
09:39:40.745 [DBG] Sent 70 bytes
no ex
09:39:42.668 [DBG] 33 bytes read
no ex
09:39:42.668 [DBG] Sent 32 bytes
no ex
09:39:43.735 [DBG] Sent 184 bytes
no ex
09:39:45.617 [DBG] 33 bytes read
no ex
09:39:45.617 [DBG] Sent 32 bytes
no ex
09:39:48.557 [DBG] 33 bytes read
no ex
09:39:48.557 [DBG] Sent 32 bytes
no ex
09:39:48.739 [DBG] Sent 184 bytes
no ex
09:39:51.667 [DBG] 33 bytes read
no ex
09:39:51.667 [DBG] Sent 32 bytes
no ex
09:39:53.741 [DBG] Sent 184 bytes
no ex
09:39:54.648 [DBG] 33 bytes read
no ex
09:39:54.648 [DBG] Sent 32 bytes
no ex
09:39:57.647 [DBG] 33 bytes read
no ex
09:39:57.647 [DBG] Sent 32 bytes
no ex
09:39:58.743 [DBG] Sent 184 bytes
no ex
09:40:00.625 [DBG] 33 bytes read
no ex
09:40:00.625 [DBG] Sent 32 bytes
no ex
09:40:03.699 [DBG] 33 bytes read
no ex
09:40:03.699 [DBG] Sent 32 bytes
no ex
09:40:03.744 [DBG] Sent 184 bytes
no ex
09:40:06.611 [DBG] 33 bytes read
no ex
09:40:06.611 [DBG] Sent 32 bytes
no ex
09:40:08.747 [DBG] Sent 184 bytes
no ex
09:40:09.623 [DBG] 33 bytes read
no ex
09:40:09.623 [DBG] Sent 32 bytes
no ex
09:40:12.635 [DBG] 33 bytes read
no ex
09:40:12.636 [DBG] Sent 32 bytes
no ex
09:40:13.748 [DBG] Sent 184 bytes
no ex
09:40:15.555 [DBG] 33 bytes read
no ex
09:40:15.555 [DBG] Sent 32 bytes
no ex
09:40:18.621 [DBG] 33 bytes read
no ex
09:40:18.621 [DBG] Sent 32 bytes
no ex
09:40:18.741 [DBG] Sent 184 bytes
no ex
09:40:21.667 [DBG] 33 bytes read
no ex
09:40:21.667 [DBG] Sent 32 bytes
no ex
09:40:23.748 [DBG] Sent 184 bytes
no ex
09:40:24.684 [DBG] 33 bytes read
no ex
09:40:24.684 [DBG] Sent 32 bytes
no ex
09:40:27.680 [DBG] 33 bytes read
no ex
09:40:27.680 [DBG] Sent 32 bytes
no ex
09:40:28.747 [DBG] Sent 184 bytes
no ex
09:40:30.650 [DBG] 33 bytes read
no ex
09:40:30.650 [DBG] Sent 32 bytes
no ex
09:40:33.647 [DBG] 33 bytes read
no ex
09:40:33.648 [DBG] Sent 32 bytes
no ex
09:40:33.752 [DBG] Sent 184 bytes
no ex
09:40:36.643 [DBG] 33 bytes read
no ex
09:40:36.643 [DBG] Sent 32 bytes
no ex
09:40:38.754 [DBG] Sent 184 bytes
no ex
09:40:39.655 [DBG] 33 bytes read
no ex
09:40:39.655 [DBG] Sent 32 bytes
no ex
09:40:42.646 [DBG] 33 bytes read
no ex
09:40:42.646 [DBG] Sent 32 bytes
no ex
09:40:43.742 [DBG] Sent 184 bytes
no ex
09:40:45.671 [DBG] 33 bytes read
no ex
09:40:45.671 [DBG] Sent 32 bytes
no ex
09:40:48.698 [DBG] 33 bytes read
no ex
09:40:48.699 [DBG] Sent 32 bytes
no ex
09:40:48.746 [DBG] Sent 184 bytes
no ex
09:40:51.667 [DBG] 33 bytes read
no ex
09:40:51.667 [DBG] Sent 32 bytes
no ex
09:40:52.741 [DBG] Sent 70 bytes
no ex
09:40:53.749 [DBG] Sent 184 bytes
no ex
09:40:54.627 [DBG] 33 bytes read
no ex
09:40:54.628 [DBG] Sent 32 bytes
no ex
09:40:57.659 [DBG] 33 bytes read
no ex
09:40:57.659 [DBG] Sent 32 bytes
no ex
09:40:58.753 [DBG] Sent 184 bytes
no ex
09:41:00.680 [DBG] 33 bytes read
no ex
09:41:00.680 [DBG] Sent 32 bytes
no ex
09:41:03.623 [DBG] 33 bytes read
no ex
09:41:03.623 [DBG] Sent 32 bytes
no ex
09:41:03.742 [DBG] Sent 184 bytes
no ex
09:41:06.624 [DBG] 33 bytes read
no ex
09:41:06.624 [DBG] Sent 32 bytes
no ex
09:41:08.748 [DBG] Sent 184 bytes
no ex
09:41:09.687 [DBG] 33 bytes read
no ex
09:41:09.687 [DBG] Sent 32 bytes
no ex
09:41:12.643 [DBG] 33 bytes read
no ex
09:41:12.643 [DBG] Sent 32 bytes
no ex
09:41:13.752 [DBG] Sent 184 bytes
no ex
09:41:15.710 [DBG] 33 bytes read
no ex
09:41:15.710 [DBG] Sent 32 bytes
no ex
09:41:18.667 [DBG] 33 bytes read
no ex
09:41:18.668 [DBG] Sent 32 bytes
no ex
09:41:18.761 [DBG] Sent 184 bytes
no ex
09:41:21.656 [DBG] 33 bytes read
no ex
09:41:21.656 [DBG] Sent 32 bytes
no ex
09:41:23.760 [DBG] Sent 184 bytes
no ex
09:41:24.671 [DBG] 33 bytes read
no ex
09:41:24.671 [DBG] Sent 32 bytes
no ex
09:41:27.683 [DBG] 33 bytes read
no ex
09:41:27.683 [DBG] Sent 32 bytes
no ex
09:41:28.754 [DBG] Sent 184 bytes
no ex
09:41:30.671 [DBG] 33 bytes read
no ex
09:41:30.672 [DBG] Sent 32 bytes
no ex
09:41:33.645 [DBG] 33 bytes read
no ex
09:41:33.645 [DBG] Sent 32 bytes
no ex
09:41:33.751 [DBG] Sent 184 bytes
no ex
09:41:36.090 [INF] RunScan: 0
09:41:36.090 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:41)","Data":null,"DataObj":null}
09:41:36.719 [DBG] 33 bytes read
no ex
09:41:36.719 [DBG] Sent 32 bytes
no ex
09:41:38.756 [DBG] Sent 184 bytes
no ex
09:41:39.707 [DBG] 33 bytes read
no ex
09:41:39.707 [DBG] Sent 32 bytes
no ex
09:41:42.620 [DBG] 33 bytes read
no ex
09:41:42.620 [DBG] Sent 32 bytes
no ex
09:41:43.759 [DBG] Sent 184 bytes
no ex
09:41:45.666 [DBG] 33 bytes read
no ex
09:41:45.666 [DBG] Sent 32 bytes
no ex
09:41:48.661 [DBG] 33 bytes read
no ex
09:41:48.661 [DBG] Sent 32 bytes
no ex
09:41:48.748 [DBG] Sent 184 bytes
no ex
09:41:51.616 [DBG] 33 bytes read
no ex
09:41:51.616 [DBG] Sent 32 bytes
no ex
09:41:53.748 [DBG] Sent 184 bytes
no ex
09:41:54.652 [DBG] 33 bytes read
no ex
09:41:54.652 [DBG] Sent 32 bytes
no ex
09:41:57.691 [DBG] 33 bytes read
no ex
09:41:57.691 [DBG] Sent 32 bytes
no ex
09:41:58.749 [DBG] Sent 184 bytes
no ex
09:42:00.652 [DBG] 33 bytes read
no ex
09:42:00.652 [DBG] Sent 32 bytes
no ex
09:42:03.639 [DBG] 33 bytes read
no ex
09:42:03.639 [DBG] Sent 32 bytes
no ex
09:42:03.752 [DBG] Sent 184 bytes
no ex
09:42:06.627 [DBG] 33 bytes read
no ex
09:42:06.627 [DBG] Sent 32 bytes
no ex
09:42:08.757 [DBG] Sent 184 bytes
no ex
09:42:09.652 [DBG] 33 bytes read
no ex
09:42:09.653 [DBG] Sent 32 bytes
no ex
09:42:12.635 [DBG] 33 bytes read
no ex
09:42:12.636 [DBG] Sent 32 bytes
no ex
09:42:13.762 [DBG] Sent 184 bytes
no ex
09:42:16.159 [DBG] 33 bytes read
no ex
09:42:16.159 [DBG] Sent 32 bytes
no ex
09:42:18.691 [DBG] 33 bytes read
no ex
09:42:18.692 [DBG] Sent 32 bytes
no ex
09:42:18.762 [DBG] Sent 184 bytes
no ex
09:42:21.659 [DBG] 33 bytes read
no ex
09:42:21.660 [DBG] Sent 32 bytes
no ex
09:42:23.751 [DBG] Sent 184 bytes
no ex
09:42:24.667 [DBG] 33 bytes read
no ex
09:42:24.667 [DBG] Sent 32 bytes
no ex
09:42:27.671 [DBG] 33 bytes read
no ex
09:42:27.671 [DBG] Sent 32 bytes
no ex
09:42:28.764 [DBG] Sent 184 bytes
no ex
09:42:30.676 [DBG] 33 bytes read
no ex
09:42:30.676 [DBG] Sent 32 bytes
no ex
09:42:33.661 [DBG] 33 bytes read
no ex
09:42:33.661 [DBG] Sent 32 bytes
no ex
09:42:33.766 [DBG] Sent 184 bytes
no ex
09:42:36.707 [DBG] 33 bytes read
no ex
09:42:36.707 [DBG] Sent 32 bytes
no ex
09:42:37.753 [INF] RunHandleWinLoss: 0
09:42:37.753 [INF] CalculateResult: 20220808
09:42:37.754 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
09:42:38.757 [DBG] Sent 184 bytes
no ex
09:42:39.683 [DBG] 33 bytes read
no ex
09:42:39.683 [DBG] Sent 32 bytes
no ex
09:42:42.664 [DBG] 33 bytes read
no ex
09:42:42.664 [DBG] Sent 32 bytes
no ex
09:42:43.761 [DBG] Sent 184 bytes
no ex
09:42:45.660 [DBG] 33 bytes read
no ex
09:42:45.661 [DBG] Sent 32 bytes
no ex
09:42:48.687 [DBG] 33 bytes read
no ex
09:42:48.687 [DBG] Sent 32 bytes
no ex
09:42:48.768 [DBG] Sent 184 bytes
no ex
09:42:51.667 [DBG] 33 bytes read
no ex
09:42:51.668 [DBG] Sent 32 bytes
no ex
09:42:53.756 [DBG] Sent 184 bytes
no ex
09:42:54.627 [DBG] 33 bytes read
no ex
09:42:54.628 [DBG] Sent 32 bytes
no ex
09:42:57.628 [DBG] 33 bytes read
no ex
09:42:57.628 [DBG] Sent 32 bytes
no ex
09:42:58.764 [DBG] Sent 184 bytes
no ex
09:43:00.701 [DBG] 33 bytes read
no ex
09:43:00.701 [DBG] Sent 32 bytes
no ex
09:43:03.667 [DBG] 33 bytes read
no ex
09:43:03.667 [DBG] Sent 32 bytes
no ex
09:43:03.768 [DBG] Sent 184 bytes
no ex
09:43:06.670 [DBG] 33 bytes read
no ex
09:43:06.670 [DBG] Sent 32 bytes
no ex
09:43:08.760 [DBG] Sent 184 bytes
no ex
09:43:09.700 [DBG] 33 bytes read
no ex
09:43:09.700 [DBG] Sent 32 bytes
no ex
09:43:12.663 [DBG] 33 bytes read
no ex
09:43:12.663 [DBG] Sent 32 bytes
no ex
09:43:13.763 [DBG] Sent 184 bytes
no ex
09:43:15.636 [DBG] 33 bytes read
no ex
09:43:15.636 [DBG] Sent 32 bytes
no ex
09:43:18.695 [DBG] 33 bytes read
no ex
09:43:18.695 [DBG] Sent 32 bytes
no ex
09:43:18.767 [DBG] Sent 184 bytes
no ex
09:43:21.648 [DBG] 33 bytes read
no ex
09:43:21.648 [DBG] Sent 32 bytes
no ex
09:43:23.765 [DBG] Sent 184 bytes
no ex
09:43:24.664 [DBG] 33 bytes read
no ex
09:43:24.664 [DBG] Sent 32 bytes
no ex
09:43:27.663 [DBG] 33 bytes read
no ex
09:43:27.663 [DBG] Sent 32 bytes
no ex
09:43:28.770 [DBG] Sent 184 bytes
no ex
09:43:30.683 [DBG] 33 bytes read
no ex
09:43:30.683 [DBG] Sent 32 bytes
no ex
09:43:33.643 [DBG] 33 bytes read
no ex
09:43:33.643 [DBG] Sent 32 bytes
no ex
09:43:33.775 [DBG] Sent 184 bytes
no ex
09:43:36.671 [DBG] 33 bytes read
no ex
09:43:36.671 [DBG] Sent 32 bytes
no ex
09:43:38.775 [DBG] Sent 184 bytes
no ex
09:43:39.703 [DBG] 33 bytes read
no ex
09:43:39.703 [DBG] Sent 32 bytes
no ex
09:43:43.475 [DBG] 33 bytes read
no ex
09:43:43.475 [DBG] Sent 32 bytes
no ex
09:43:43.762 [DBG] Sent 184 bytes
no ex
09:43:45.681 [DBG] 33 bytes read
no ex
09:43:45.681 [DBG] Sent 32 bytes
no ex
09:43:48.652 [DBG] 33 bytes read
no ex
09:43:48.652 [DBG] Sent 32 bytes
no ex
09:43:48.760 [DBG] Sent 184 bytes
no ex
09:43:51.647 [DBG] 33 bytes read
no ex
09:43:51.647 [DBG] Sent 32 bytes
no ex
09:43:53.766 [DBG] Sent 184 bytes
no ex
09:43:54.640 [DBG] 33 bytes read
no ex
09:43:54.641 [DBG] Sent 32 bytes
no ex
09:43:57.671 [DBG] 33 bytes read
no ex
09:43:57.671 [DBG] Sent 32 bytes
no ex
09:43:58.761 [DBG] Sent 184 bytes
no ex
09:44:00.687 [DBG] 33 bytes read
no ex
09:44:00.688 [DBG] Sent 32 bytes
no ex
09:44:03.623 [DBG] 33 bytes read
no ex
09:44:03.623 [DBG] Sent 32 bytes
no ex
09:44:03.764 [DBG] Sent 184 bytes
no ex
09:44:06.668 [DBG] 33 bytes read
no ex
09:44:06.668 [DBG] Sent 32 bytes
no ex
09:44:08.772 [DBG] Sent 184 bytes
no ex
09:44:09.695 [DBG] 33 bytes read
no ex
09:44:09.695 [DBG] Sent 32 bytes
no ex
09:44:12.671 [DBG] 33 bytes read
no ex
09:44:12.672 [DBG] Sent 32 bytes
no ex
09:44:13.781 [DBG] Sent 184 bytes
no ex
09:44:15.636 [DBG] 33 bytes read
no ex
09:44:15.636 [DBG] Sent 32 bytes
no ex
09:44:18.664 [DBG] 33 bytes read
no ex
09:44:18.664 [DBG] Sent 32 bytes
no ex
09:44:18.773 [DBG] Sent 184 bytes
no ex
09:44:21.679 [DBG] 33 bytes read
no ex
09:44:21.679 [DBG] Sent 32 bytes
no ex
09:44:23.777 [DBG] Sent 184 bytes
no ex
09:44:24.696 [DBG] 33 bytes read
no ex
09:44:24.696 [DBG] Sent 32 bytes
no ex
09:44:27.667 [DBG] 33 bytes read
no ex
09:44:27.667 [DBG] Sent 32 bytes
no ex
09:44:28.781 [DBG] Sent 184 bytes
no ex
09:44:30.623 [DBG] 33 bytes read
no ex
09:44:30.623 [DBG] Sent 32 bytes
no ex
09:44:33.695 [DBG] 33 bytes read
no ex
09:44:33.696 [DBG] Sent 32 bytes
no ex
09:44:33.769 [DBG] Sent 184 bytes
no ex
09:44:36.639 [DBG] 33 bytes read
no ex
09:44:36.640 [DBG] Sent 32 bytes
no ex
09:44:38.772 [DBG] Sent 184 bytes
no ex
09:44:39.705 [DBG] 33 bytes read
no ex
09:44:39.706 [DBG] Sent 32 bytes
no ex
09:44:42.635 [DBG] 33 bytes read
no ex
09:44:42.636 [DBG] Sent 32 bytes
no ex
09:44:43.775 [DBG] Sent 184 bytes
no ex
09:44:45.611 [DBG] 33 bytes read
no ex
09:44:45.611 [DBG] Sent 32 bytes
no ex
09:44:48.608 [DBG] 33 bytes read
no ex
09:44:48.608 [DBG] Sent 32 bytes
no ex
09:44:48.780 [DBG] Sent 184 bytes
no ex
09:44:51.691 [DBG] 33 bytes read
no ex
09:44:51.691 [DBG] Sent 32 bytes
no ex
09:44:53.785 [DBG] Sent 184 bytes
no ex
09:44:54.648 [DBG] 33 bytes read
no ex
09:44:54.648 [DBG] Sent 32 bytes
no ex
09:44:57.687 [DBG] 33 bytes read
no ex
09:44:57.687 [DBG] Sent 32 bytes
no ex
09:44:58.770 [DBG] Sent 184 bytes
no ex
09:45:00.635 [DBG] 33 bytes read
no ex
09:45:00.635 [DBG] Sent 32 bytes
no ex
09:45:03.679 [DBG] 33 bytes read
no ex
09:45:03.679 [DBG] Sent 32 bytes
no ex
09:45:03.773 [DBG] Sent 184 bytes
no ex
09:45:06.619 [DBG] 33 bytes read
no ex
09:45:06.619 [DBG] Sent 32 bytes
no ex
09:45:08.776 [DBG] Sent 184 bytes
no ex
09:45:09.731 [DBG] 33 bytes read
no ex
09:45:09.731 [DBG] Sent 32 bytes
no ex
09:45:12.690 [DBG] 33 bytes read
no ex
09:45:12.691 [DBG] Sent 32 bytes
no ex
09:45:13.782 [DBG] Sent 184 bytes
no ex
09:45:15.660 [DBG] 33 bytes read
no ex
09:45:15.660 [DBG] Sent 32 bytes
no ex
09:45:18.723 [DBG] 33 bytes read
no ex
09:45:18.723 [DBG] Sent 32 bytes
no ex
09:45:18.772 [DBG] Sent 184 bytes
no ex
09:45:21.669 [DBG] 33 bytes read
no ex
09:45:21.669 [DBG] Sent 32 bytes
no ex
09:45:23.777 [DBG] Sent 184 bytes
no ex
09:45:24.627 [DBG] 33 bytes read
no ex
09:45:24.627 [DBG] Sent 32 bytes
no ex
09:45:27.675 [DBG] 33 bytes read
no ex
09:45:27.676 [DBG] Sent 32 bytes
no ex
09:45:28.787 [DBG] Sent 184 bytes
no ex
09:45:31.001 [DBG] 33 bytes read
no ex
09:45:31.001 [DBG] Sent 32 bytes
no ex
09:45:33.675 [DBG] 33 bytes read
no ex
09:45:33.675 [DBG] Sent 32 bytes
no ex
09:45:33.788 [DBG] Sent 184 bytes
no ex
09:45:36.639 [DBG] 33 bytes read
no ex
09:45:36.639 [DBG] Sent 32 bytes
no ex
09:45:38.777 [DBG] Sent 184 bytes
no ex
09:45:39.655 [DBG] 33 bytes read
no ex
09:45:39.656 [DBG] Sent 32 bytes
no ex
09:45:42.659 [DBG] 33 bytes read
no ex
09:45:42.659 [DBG] Sent 32 bytes
no ex
09:45:43.783 [DBG] Sent 184 bytes
no ex
09:45:45.651 [DBG] 33 bytes read
no ex
09:45:45.651 [DBG] Sent 32 bytes
no ex
09:45:48.627 [DBG] 33 bytes read
no ex
09:45:48.628 [DBG] Sent 32 bytes
no ex
09:45:48.779 [DBG] Sent 184 bytes
no ex
09:45:51.672 [DBG] 33 bytes read
no ex
09:45:51.672 [DBG] Sent 32 bytes
no ex
09:45:53.784 [DBG] Sent 184 bytes
no ex
09:45:54.669 [DBG] 33 bytes read
no ex
09:45:54.669 [DBG] Sent 32 bytes
no ex
09:45:57.683 [DBG] 33 bytes read
no ex
09:45:57.683 [DBG] Sent 32 bytes
no ex
09:45:58.787 [DBG] Sent 184 bytes
no ex
09:46:00.700 [DBG] 33 bytes read
no ex
09:46:00.700 [DBG] Sent 32 bytes
no ex
09:46:03.665 [DBG] 33 bytes read
no ex
09:46:03.666 [DBG] Sent 32 bytes
no ex
09:46:03.776 [DBG] Sent 184 bytes
no ex
09:46:06.639 [DBG] 33 bytes read
no ex
09:46:06.640 [DBG] Sent 32 bytes
no ex
09:46:08.780 [DBG] Sent 184 bytes
no ex
09:46:09.624 [DBG] 33 bytes read
no ex
09:46:09.624 [DBG] Sent 32 bytes
no ex
09:46:12.677 [DBG] 33 bytes read
no ex
09:46:12.677 [DBG] Sent 32 bytes
no ex
09:46:13.796 [DBG] Sent 184 bytes
no ex
09:46:15.625 [DBG] 33 bytes read
no ex
09:46:15.626 [DBG] Sent 32 bytes
no ex
09:46:18.688 [DBG] 33 bytes read
no ex
09:46:18.688 [DBG] Sent 32 bytes
no ex
09:46:18.795 [DBG] Sent 184 bytes
no ex
09:46:21.643 [DBG] 33 bytes read
no ex
09:46:21.644 [DBG] Sent 32 bytes
no ex
09:46:23.782 [DBG] Sent 184 bytes
no ex
09:46:24.676 [DBG] 33 bytes read
no ex
09:46:24.676 [DBG] Sent 32 bytes
no ex
09:46:27.667 [DBG] 33 bytes read
no ex
09:46:27.668 [DBG] Sent 32 bytes
no ex
09:46:28.788 [DBG] Sent 184 bytes
no ex
09:46:30.684 [DBG] 33 bytes read
no ex
09:46:30.684 [DBG] Sent 32 bytes
no ex
09:46:33.679 [DBG] 33 bytes read
no ex
09:46:33.679 [DBG] Sent 32 bytes
no ex
09:46:33.798 [DBG] Sent 184 bytes
no ex
09:46:35.783 [DBG] Sent 72 bytes
no ex
09:46:36.090 [INF] RunScan: 0
09:46:36.090 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:46)","Data":null,"DataObj":null}
09:46:36.712 [DBG] 33 bytes read
no ex
09:46:36.713 [DBG] Sent 32 bytes
no ex
09:46:38.792 [DBG] Sent 184 bytes
no ex
09:46:39.632 [DBG] 33 bytes read
no ex
09:46:39.632 [DBG] Sent 32 bytes
no ex
09:46:42.655 [DBG] 33 bytes read
no ex
09:46:42.656 [DBG] Sent 32 bytes
no ex
09:46:43.797 [DBG] Sent 184 bytes
no ex
09:46:45.611 [DBG] 33 bytes read
no ex
09:46:45.611 [DBG] Sent 32 bytes
no ex
09:46:48.689 [DBG] 33 bytes read
no ex
09:46:48.689 [DBG] Sent 32 bytes
no ex
09:46:48.801 [DBG] Sent 184 bytes
no ex
09:46:51.639 [DBG] 33 bytes read
no ex
09:46:51.639 [DBG] Sent 32 bytes
no ex
09:46:53.790 [DBG] Sent 184 bytes
no ex
09:46:54.635 [DBG] 33 bytes read
no ex
09:46:54.636 [DBG] Sent 32 bytes
no ex
09:46:57.642 [DBG] 33 bytes read
no ex
09:46:57.643 [DBG] Sent 32 bytes
no ex
09:46:58.797 [DBG] Sent 184 bytes
no ex
09:47:00.745 [DBG] 33 bytes read
no ex
09:47:00.745 [DBG] Sent 32 bytes
no ex
09:47:03.675 [DBG] 33 bytes read
no ex
09:47:03.676 [DBG] Sent 32 bytes
no ex
09:47:03.790 [DBG] Sent 184 bytes
no ex
09:47:06.636 [DBG] 33 bytes read
no ex
09:47:06.636 [DBG] Sent 32 bytes
no ex
09:47:08.792 [DBG] Sent 184 bytes
no ex
09:47:09.635 [DBG] 33 bytes read
no ex
09:47:09.636 [DBG] Sent 32 bytes
no ex
09:47:12.631 [DBG] 33 bytes read
no ex
09:47:12.631 [DBG] Sent 32 bytes
no ex
09:47:13.798 [DBG] Sent 184 bytes
no ex
09:47:15.647 [DBG] 33 bytes read
no ex
09:47:15.647 [DBG] Sent 32 bytes
no ex
09:47:18.655 [DBG] 33 bytes read
no ex
09:47:18.655 [DBG] Sent 32 bytes
no ex
09:47:18.800 [DBG] Sent 184 bytes
no ex
09:47:21.664 [DBG] 33 bytes read
no ex
09:47:21.664 [DBG] Sent 32 bytes
no ex
09:47:23.791 [DBG] Sent 184 bytes
no ex
09:47:24.729 [DBG] 33 bytes read
no ex
09:47:24.729 [DBG] Sent 32 bytes
no ex
09:47:27.680 [DBG] 33 bytes read
no ex
09:47:27.680 [DBG] Sent 32 bytes
no ex
09:47:28.796 [DBG] Sent 184 bytes
no ex
09:47:30.544 [DBG] 33 bytes read
no ex
09:47:30.544 [DBG] Sent 32 bytes
no ex
09:47:33.587 [DBG] 33 bytes read
no ex
09:47:33.587 [DBG] Sent 32 bytes
no ex
09:47:33.806 [DBG] Sent 184 bytes
no ex
09:47:36.580 [DBG] 33 bytes read
no ex
09:47:36.580 [DBG] Sent 32 bytes
no ex
09:47:37.755 [INF] RunHandleWinLoss: 0
09:47:37.755 [INF] CalculateResult: 20220808
09:47:37.755 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
09:47:38.790 [DBG] Sent 184 bytes
no ex
09:47:39.612 [DBG] 33 bytes read
no ex
09:47:39.612 [DBG] Sent 32 bytes
no ex
09:47:42.567 [DBG] 33 bytes read
no ex
09:47:42.567 [DBG] Sent 32 bytes
no ex
09:47:43.807 [DBG] Sent 184 bytes
no ex
09:47:45.575 [DBG] 33 bytes read
no ex
09:47:45.575 [DBG] Sent 32 bytes
no ex
09:47:48.555 [DBG] 33 bytes read
no ex
09:47:48.555 [DBG] Sent 32 bytes
no ex
09:47:48.797 [DBG] Sent 184 bytes
no ex
09:47:51.575 [DBG] 33 bytes read
no ex
09:47:51.575 [DBG] Sent 32 bytes
no ex
09:47:53.801 [DBG] Sent 184 bytes
no ex
09:47:54.567 [DBG] 33 bytes read
no ex
09:47:54.567 [DBG] Sent 32 bytes
no ex
09:47:57.581 [DBG] 33 bytes read
no ex
09:47:57.581 [DBG] Sent 32 bytes
no ex
09:47:58.800 [DBG] Sent 184 bytes
no ex
09:48:00.555 [DBG] 33 bytes read
no ex
09:48:00.556 [DBG] Sent 32 bytes
no ex
09:48:03.563 [DBG] 33 bytes read
no ex
09:48:03.563 [DBG] Sent 32 bytes
no ex
09:48:03.794 [DBG] Sent 184 bytes
no ex
09:48:06.567 [DBG] 33 bytes read
no ex
09:48:06.567 [DBG] Sent 32 bytes
no ex
09:48:08.798 [DBG] Sent 184 bytes
no ex
09:48:09.571 [DBG] 33 bytes read
no ex
09:48:09.571 [DBG] Sent 32 bytes
no ex
09:48:12.598 [DBG] 33 bytes read
no ex
09:48:12.598 [DBG] Sent 32 bytes
no ex
09:48:13.803 [DBG] Sent 184 bytes
no ex
09:48:15.575 [DBG] 33 bytes read
no ex
09:48:15.575 [DBG] Sent 32 bytes
no ex
09:48:18.574 [DBG] 33 bytes read
no ex
09:48:18.574 [DBG] Sent 32 bytes
no ex
09:48:18.807 [DBG] Sent 184 bytes
no ex
09:48:21.567 [DBG] 33 bytes read
no ex
09:48:21.568 [DBG] Sent 32 bytes
no ex
09:48:23.812 [DBG] Sent 184 bytes
no ex
09:48:24.586 [DBG] 33 bytes read
no ex
09:48:24.586 [DBG] Sent 32 bytes
no ex
09:48:27.570 [DBG] 33 bytes read
no ex
09:48:27.570 [DBG] Sent 32 bytes
no ex
09:48:28.814 [DBG] Sent 184 bytes
no ex
09:48:30.582 [DBG] 33 bytes read
no ex
09:48:30.582 [DBG] Sent 32 bytes
no ex
09:48:33.577 [DBG] 33 bytes read
no ex
09:48:33.578 [DBG] Sent 32 bytes
no ex
09:48:33.815 [DBG] Sent 184 bytes
no ex
09:48:36.559 [DBG] 33 bytes read
no ex
09:48:36.559 [DBG] Sent 32 bytes
no ex
09:48:38.801 [DBG] Sent 184 bytes
no ex
09:48:39.571 [DBG] 33 bytes read
no ex
09:48:39.571 [DBG] Sent 32 bytes
no ex
09:48:42.562 [DBG] 33 bytes read
no ex
09:48:42.562 [DBG] Sent 32 bytes
no ex
09:48:43.802 [DBG] Sent 184 bytes
no ex
09:48:45.585 [DBG] 33 bytes read
no ex
09:48:45.585 [DBG] Sent 32 bytes
no ex
09:48:48.578 [DBG] 33 bytes read
no ex
09:48:48.579 [DBG] Sent 32 bytes
no ex
09:48:48.816 [DBG] Sent 184 bytes
no ex
09:48:51.584 [DBG] 33 bytes read
no ex
09:48:51.584 [DBG] Sent 32 bytes
no ex
09:48:53.809 [DBG] Sent 184 bytes
no ex
09:48:54.582 [DBG] 33 bytes read
no ex
09:48:54.582 [DBG] Sent 32 bytes
no ex
09:48:57.566 [DBG] 33 bytes read
no ex
09:48:57.567 [DBG] Sent 32 bytes
no ex
09:48:58.812 [DBG] Sent 184 bytes
no ex
09:49:00.562 [DBG] 33 bytes read
no ex
09:49:00.563 [DBG] Sent 32 bytes
no ex
09:49:03.579 [DBG] 33 bytes read
no ex
09:49:03.580 [DBG] Sent 32 bytes
no ex
09:49:03.816 [DBG] Sent 184 bytes
no ex
09:49:06.577 [DBG] 33 bytes read
no ex
09:49:06.578 [DBG] Sent 32 bytes
no ex
09:49:08.813 [DBG] Sent 184 bytes
no ex
09:49:09.577 [DBG] 33 bytes read
no ex
09:49:09.577 [DBG] Sent 32 bytes
no ex
09:49:12.557 [DBG] 33 bytes read
no ex
09:49:12.558 [DBG] Sent 32 bytes
no ex
09:49:13.819 [DBG] Sent 184 bytes
no ex
09:49:15.578 [DBG] 33 bytes read
no ex
09:49:15.578 [DBG] Sent 32 bytes
no ex
09:49:18.558 [DBG] 33 bytes read
no ex
09:49:18.558 [DBG] Sent 32 bytes
no ex
09:49:18.816 [DBG] Sent 184 bytes
no ex
09:49:21.582 [DBG] 33 bytes read
no ex
09:49:21.582 [DBG] Sent 32 bytes
no ex
09:49:23.822 [DBG] Sent 184 bytes
no ex
09:49:24.589 [DBG] 33 bytes read
no ex
09:49:24.590 [DBG] Sent 32 bytes
no ex
09:49:27.561 [DBG] 33 bytes read
no ex
09:49:27.561 [DBG] Sent 32 bytes
no ex
09:49:28.809 [DBG] Sent 184 bytes
no ex
09:49:30.562 [DBG] 33 bytes read
no ex
09:49:30.562 [DBG] Sent 32 bytes
no ex
09:49:33.541 [DBG] 33 bytes read
no ex
09:49:33.541 [DBG] Sent 32 bytes
no ex
09:49:33.817 [DBG] Sent 184 bytes
no ex
09:49:36.558 [DBG] 33 bytes read
no ex
09:49:36.558 [DBG] Sent 32 bytes
no ex
09:49:38.812 [DBG] Sent 184 bytes
no ex
09:49:39.580 [DBG] 33 bytes read
no ex
09:49:39.580 [DBG] Sent 32 bytes
no ex
09:49:42.589 [DBG] 33 bytes read
no ex
09:49:42.589 [DBG] Sent 32 bytes
no ex
09:49:43.824 [DBG] Sent 184 bytes
no ex
09:49:45.581 [DBG] 33 bytes read
no ex
09:49:45.581 [DBG] Sent 32 bytes
no ex
09:49:48.537 [DBG] 33 bytes read
no ex
09:49:48.537 [DBG] Sent 32 bytes
no ex
09:49:48.827 [DBG] Sent 184 bytes
no ex
09:49:51.617 [DBG] 33 bytes read
no ex
09:49:51.617 [DBG] Sent 32 bytes
no ex
09:49:53.825 [DBG] Sent 184 bytes
no ex
09:49:54.541 [DBG] 33 bytes read
no ex
09:49:54.541 [DBG] Sent 32 bytes
no ex
09:49:57.593 [DBG] 33 bytes read
no ex
09:49:57.593 [DBG] Sent 32 bytes
no ex
09:49:58.817 [DBG] Sent 184 bytes
no ex
09:50:00.576 [DBG] 33 bytes read
no ex
09:50:00.576 [DBG] Sent 32 bytes
no ex
09:50:03.564 [DBG] 33 bytes read
no ex
09:50:03.564 [DBG] Sent 32 bytes
no ex
09:50:03.830 [DBG] Sent 184 bytes
no ex
09:50:06.588 [DBG] 33 bytes read
no ex
09:50:06.589 [DBG] Sent 32 bytes
no ex
09:50:08.830 [DBG] Sent 184 bytes
no ex
09:50:09.581 [DBG] 33 bytes read
no ex
09:50:09.581 [DBG] Sent 32 bytes
no ex
09:50:12.581 [DBG] 33 bytes read
no ex
09:50:12.581 [DBG] Sent 32 bytes
no ex
09:50:13.821 [DBG] Sent 184 bytes
no ex
09:50:15.572 [DBG] 33 bytes read
no ex
09:50:15.572 [DBG] Sent 32 bytes
no ex
09:50:18.568 [DBG] 33 bytes read
no ex
09:50:18.568 [DBG] Sent 32 bytes
no ex
09:50:18.824 [DBG] Sent 184 bytes
no ex
09:50:21.568 [DBG] 33 bytes read
no ex
09:50:21.569 [DBG] Sent 32 bytes
no ex
09:50:23.823 [DBG] Sent 184 bytes
no ex
09:50:24.580 [DBG] 33 bytes read
no ex
09:50:24.580 [DBG] Sent 32 bytes
no ex
09:50:27.560 [DBG] 33 bytes read
no ex
09:50:27.560 [DBG] Sent 32 bytes
no ex
09:50:28.826 [DBG] Sent 184 bytes
no ex
09:50:30.616 [DBG] 33 bytes read
no ex
09:50:30.617 [DBG] Sent 32 bytes
no ex
09:50:33.592 [DBG] 33 bytes read
no ex
09:50:33.592 [DBG] Sent 32 bytes
no ex
09:50:33.827 [DBG] Sent 184 bytes
no ex
09:50:36.583 [DBG] 33 bytes read
no ex
09:50:36.583 [DBG] Sent 32 bytes
no ex
09:50:38.830 [DBG] Sent 184 bytes
no ex
09:50:39.559 [DBG] 33 bytes read
no ex
09:50:39.559 [DBG] Sent 32 bytes
no ex
09:50:42.576 [DBG] 33 bytes read
no ex
09:50:42.577 [DBG] Sent 32 bytes
no ex
09:50:43.835 [DBG] Sent 184 bytes
no ex
09:50:45.576 [DBG] 33 bytes read
no ex
09:50:45.577 [DBG] Sent 32 bytes
no ex
09:50:48.568 [DBG] 33 bytes read
no ex
09:50:48.568 [DBG] Sent 32 bytes
no ex
09:50:48.830 [DBG] Sent 184 bytes
no ex
09:50:50.974 [DBG] 8 bytes read
no ex
09:50:50.974 [DBG] Sent 4 bytes
no ex
09:50:50.985 [INF] 8888 bot: 2, peer: 0, ccu: 0
09:51:36.091 [INF] RunScan: 0
09:51:36.091 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:51)","Data":null,"DataObj":null}
09:52:37.757 [INF] RunHandleWinLoss: 0
09:52:37.757 [INF] CalculateResult: 20220808
09:52:37.757 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
09:56:36.091 [INF] RunScan: 0
09:56:36.091 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(9:56)","Data":null,"DataObj":null}
09:57:37.758 [INF] RunHandleWinLoss: 0
09:57:37.758 [INF] CalculateResult: 20220808
09:57:37.758 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
10:01:36.091 [INF] RunScan: 0
10:01:36.091 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:1)","Data":null,"DataObj":null}
10:02:37.760 [INF] RunHandleWinLoss: 0
10:02:37.760 [INF] CalculateResult: 20220808
10:02:37.760 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
10:06:36.091 [INF] RunScan: 0
10:06:36.091 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:6)","Data":null,"DataObj":null}
10:07:37.762 [INF] RunHandleWinLoss: 0
10:07:37.762 [INF] CalculateResult: 20220808
10:07:37.762 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
10:11:36.092 [INF] RunScan: 0
10:11:36.092 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:11)","Data":null,"DataObj":null}
10:12:37.763 [INF] RunHandleWinLoss: 0
10:12:37.763 [INF] CalculateResult: 20220808
10:12:37.763 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
10:16:36.092 [INF] RunScan: 0
10:16:36.092 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:16)","Data":null,"DataObj":null}
10:17:37.765 [INF] RunHandleWinLoss: 0
10:17:37.765 [INF] CalculateResult: 20220808
10:17:37.765 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
10:21:36.092 [INF] RunScan: 0
10:21:36.092 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:21)","Data":null,"DataObj":null}
10:22:37.804 [INF] RunHandleWinLoss: 0
10:22:37.804 [INF] CalculateResult: 20220808
10:22:37.804 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
10:26:36.092 [INF] RunScan: 0
10:26:36.092 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:26)","Data":null,"DataObj":null}
10:27:37.809 [INF] RunHandleWinLoss: 0
10:27:37.809 [INF] CalculateResult: 20220808
10:27:37.809 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
10:31:36.093 [INF] RunScan: 0
10:31:36.093 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:31)","Data":null,"DataObj":null}
10:32:37.811 [INF] RunHandleWinLoss: 0
10:32:37.811 [INF] CalculateResult: 20220808
10:32:37.811 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
10:36:36.093 [INF] RunScan: 0
10:36:36.093 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:36)","Data":null,"DataObj":null}
10:37:37.813 [INF] RunHandleWinLoss: 0
10:37:37.813 [INF] CalculateResult: 20220808
10:37:37.813 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
10:41:36.094 [INF] RunScan: 0
10:41:36.094 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:41)","Data":null,"DataObj":null}
10:42:37.825 [INF] RunHandleWinLoss: 0
10:42:37.826 [INF] CalculateResult: 20220808
10:42:37.826 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
10:46:36.094 [INF] RunScan: 0
10:46:36.094 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:46)","Data":null,"DataObj":null}
10:47:37.828 [INF] RunHandleWinLoss: 0
10:47:37.828 [INF] CalculateResult: 20220808
10:47:37.828 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
10:51:36.094 [INF] RunScan: 0
10:51:36.095 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:51)","Data":null,"DataObj":null}
10:52:37.829 [INF] RunHandleWinLoss: 0
10:52:37.829 [INF] CalculateResult: 20220808
10:52:37.829 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
10:56:36.095 [INF] RunScan: 0
10:56:36.095 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(10:56)","Data":null,"DataObj":null}
10:57:37.831 [INF] RunHandleWinLoss: 0
10:57:37.831 [INF] CalculateResult: 20220808
10:57:37.831 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
11:01:36.095 [INF] RunScan: 0
11:01:36.096 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:1)","Data":null,"DataObj":null}
11:02:37.832 [INF] RunHandleWinLoss: 0
11:02:37.832 [INF] CalculateResult: 20220808
11:02:37.832 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
11:06:36.096 [INF] RunScan: 0
11:06:36.096 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:6)","Data":null,"DataObj":null}
11:07:37.834 [INF] RunHandleWinLoss: 0
11:07:37.834 [INF] CalculateResult: 20220808
11:07:37.834 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
11:11:36.096 [INF] RunScan: 0
11:11:36.096 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:11)","Data":null,"DataObj":null}
11:12:37.835 [INF] RunHandleWinLoss: 0
11:12:37.835 [INF] CalculateResult: 20220808
11:12:37.835 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
11:16:36.096 [INF] RunScan: 0
11:16:36.096 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:16)","Data":null,"DataObj":null}
11:17:37.836 [INF] RunHandleWinLoss: 0
11:17:37.836 [INF] CalculateResult: 20220808
11:17:37.836 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
11:21:36.097 [INF] RunScan: 0
11:21:36.097 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:21)","Data":null,"DataObj":null}
11:22:37.838 [INF] RunHandleWinLoss: 0
11:22:37.838 [INF] CalculateResult: 20220808
11:22:37.838 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
11:26:36.097 [INF] RunScan: 0
11:26:36.097 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:26)","Data":null,"DataObj":null}
11:27:37.839 [INF] RunHandleWinLoss: 0
11:27:37.839 [INF] CalculateResult: 20220808
11:27:37.839 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
11:31:36.097 [INF] RunScan: 0
11:31:36.097 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:31)","Data":null,"DataObj":null}
11:32:37.842 [INF] RunHandleWinLoss: 0
11:32:37.842 [INF] CalculateResult: 20220808
11:32:37.842 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
11:36:36.097 [INF] RunScan: 0
11:36:36.098 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:36)","Data":null,"DataObj":null}
11:37:37.843 [INF] RunHandleWinLoss: 0
11:37:37.844 [INF] CalculateResult: 20220808
11:37:37.844 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
11:41:36.098 [INF] RunScan: 0
11:41:36.098 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:41)","Data":null,"DataObj":null}
11:42:37.845 [INF] RunHandleWinLoss: 0
11:42:37.845 [INF] CalculateResult: 20220808
11:42:37.845 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
11:46:36.099 [INF] RunScan: 0
11:46:36.099 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:46)","Data":null,"DataObj":null}
11:47:37.847 [INF] RunHandleWinLoss: 0
11:47:37.847 [INF] CalculateResult: 20220808
11:47:37.847 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
11:51:36.099 [INF] RunScan: 0
11:51:36.100 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:51)","Data":null,"DataObj":null}
11:52:37.850 [INF] RunHandleWinLoss: 0
11:52:37.850 [INF] CalculateResult: 20220808
11:52:37.850 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
11:56:36.100 [INF] RunScan: 0
11:56:36.100 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(11:56)","Data":null,"DataObj":null}
11:57:37.852 [INF] RunHandleWinLoss: 0
11:57:37.852 [INF] CalculateResult: 20220808
11:57:37.852 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
12:01:36.101 [INF] RunScan: 0
12:01:36.101 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:1)","Data":null,"DataObj":null}
12:02:37.853 [INF] RunHandleWinLoss: 0
12:02:37.853 [INF] CalculateResult: 20220808
12:02:37.853 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
12:06:36.101 [INF] RunScan: 0
12:06:36.101 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:6)","Data":null,"DataObj":null}
12:07:37.855 [INF] RunHandleWinLoss: 0
12:07:37.855 [INF] CalculateResult: 20220808
12:07:37.855 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
12:11:36.101 [INF] RunScan: 0
12:11:36.101 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:11)","Data":null,"DataObj":null}
12:12:37.856 [INF] RunHandleWinLoss: 0
12:12:37.856 [INF] CalculateResult: 20220808
12:12:37.856 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
12:16:36.101 [INF] RunScan: 0
12:16:36.101 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:16)","Data":null,"DataObj":null}
12:17:37.858 [INF] RunHandleWinLoss: 0
12:17:37.858 [INF] CalculateResult: 20220808
12:17:37.858 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
12:21:36.101 [INF] RunScan: 0
12:21:36.101 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:21)","Data":null,"DataObj":null}
12:22:37.859 [INF] RunHandleWinLoss: 0
12:22:37.859 [INF] CalculateResult: 20220808
12:22:37.859 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
12:26:36.102 [INF] RunScan: 0
12:26:36.102 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:26)","Data":null,"DataObj":null}
12:27:37.861 [INF] RunHandleWinLoss: 0
12:27:37.861 [INF] CalculateResult: 20220808
12:27:37.861 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
12:31:36.102 [INF] RunScan: 0
12:31:36.102 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:31)","Data":null,"DataObj":null}
12:32:37.862 [INF] RunHandleWinLoss: 0
12:32:37.862 [INF] CalculateResult: 20220808
12:32:37.862 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
12:36:36.102 [INF] RunScan: 0
12:36:36.103 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:36)","Data":null,"DataObj":null}
12:37:37.864 [INF] RunHandleWinLoss: 0
12:37:37.864 [INF] CalculateResult: 20220808
12:37:37.864 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
12:41:36.103 [INF] RunScan: 0
12:41:36.103 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:41)","Data":null,"DataObj":null}
12:42:37.865 [INF] RunHandleWinLoss: 0
12:42:37.865 [INF] CalculateResult: 20220808
12:42:37.865 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
12:46:36.103 [INF] RunScan: 0
12:46:36.103 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:46)","Data":null,"DataObj":null}
12:47:37.866 [INF] RunHandleWinLoss: 0
12:47:37.866 [INF] CalculateResult: 20220808
12:47:37.866 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
12:51:36.104 [INF] RunScan: 0
12:51:36.104 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:51)","Data":null,"DataObj":null}
12:52:37.867 [INF] RunHandleWinLoss: 0
12:52:37.867 [INF] CalculateResult: 20220808
12:52:37.867 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
12:56:36.104 [INF] RunScan: 0
12:56:36.104 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(12:56)","Data":null,"DataObj":null}
12:57:37.869 [INF] RunHandleWinLoss: 0
12:57:37.870 [INF] CalculateResult: 20220808
12:57:37.870 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
13:01:36.104 [INF] RunScan: 0
13:01:36.104 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:1)","Data":null,"DataObj":null}
13:02:37.871 [INF] RunHandleWinLoss: 0
13:02:37.871 [INF] CalculateResult: 20220808
13:02:37.871 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
13:06:36.104 [INF] RunScan: 0
13:06:36.105 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:6)","Data":null,"DataObj":null}
13:07:37.872 [INF] RunHandleWinLoss: 0
13:07:37.872 [INF] CalculateResult: 20220808
13:07:37.872 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
13:11:36.105 [INF] RunScan: 0
13:11:36.105 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:11)","Data":null,"DataObj":null}
13:12:37.874 [INF] RunHandleWinLoss: 0
13:12:37.874 [INF] CalculateResult: 20220808
13:12:37.874 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
13:16:36.105 [INF] RunScan: 0
13:16:36.106 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:16)","Data":null,"DataObj":null}
13:17:37.875 [INF] RunHandleWinLoss: 0
13:17:37.875 [INF] CalculateResult: 20220808
13:17:37.875 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
13:21:36.106 [INF] RunScan: 0
13:21:36.106 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:21)","Data":null,"DataObj":null}
13:22:37.877 [INF] RunHandleWinLoss: 0
13:22:37.877 [INF] CalculateResult: 20220808
13:22:37.877 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
13:26:36.106 [INF] RunScan: 0
13:26:36.106 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:26)","Data":null,"DataObj":null}
13:27:37.878 [INF] RunHandleWinLoss: 0
13:27:37.878 [INF] CalculateResult: 20220808
13:27:37.878 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
13:31:36.107 [INF] RunScan: 0
13:31:36.107 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:31)","Data":null,"DataObj":null}
13:32:37.880 [INF] RunHandleWinLoss: 0
13:32:37.880 [INF] CalculateResult: 20220808
13:32:37.880 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
13:36:36.107 [INF] RunScan: 0
13:36:36.107 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:36)","Data":null,"DataObj":null}
13:37:37.882 [INF] RunHandleWinLoss: 0
13:37:37.882 [INF] CalculateResult: 20220808
13:37:37.882 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
13:41:36.107 [INF] RunScan: 0
13:41:36.108 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:41)","Data":null,"DataObj":null}
13:42:37.883 [INF] RunHandleWinLoss: 0
13:42:37.883 [INF] CalculateResult: 20220808
13:42:37.883 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
13:46:36.108 [INF] RunScan: 0
13:46:36.108 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:46)","Data":null,"DataObj":null}
13:47:37.885 [INF] RunHandleWinLoss: 0
13:47:37.885 [INF] CalculateResult: 20220808
13:47:37.885 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
13:51:36.108 [INF] RunScan: 0
13:51:36.109 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:51)","Data":null,"DataObj":null}
13:52:37.888 [INF] RunHandleWinLoss: 0
13:52:37.888 [INF] CalculateResult: 20220808
13:52:37.888 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
13:56:36.109 [INF] RunScan: 0
13:56:36.109 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(13:56)","Data":null,"DataObj":null}
13:57:37.889 [INF] RunHandleWinLoss: 0
13:57:37.889 [INF] CalculateResult: 20220808
13:57:37.889 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
14:01:36.109 [INF] RunScan: 0
14:01:36.109 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:1)","Data":null,"DataObj":null}
14:02:37.891 [INF] RunHandleWinLoss: 0
14:02:37.891 [INF] CalculateResult: 20220808
14:02:37.891 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
14:06:36.109 [INF] RunScan: 0
14:06:36.110 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:6)","Data":null,"DataObj":null}
14:07:37.893 [INF] RunHandleWinLoss: 0
14:07:37.893 [INF] CalculateResult: 20220808
14:07:37.893 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
14:11:36.110 [INF] RunScan: 0
14:11:36.110 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:11)","Data":null,"DataObj":null}
14:12:37.895 [INF] RunHandleWinLoss: 0
14:12:37.895 [INF] CalculateResult: 20220808
14:12:37.895 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
14:16:36.110 [INF] RunScan: 0
14:16:36.110 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:16)","Data":null,"DataObj":null}
14:17:37.898 [INF] RunHandleWinLoss: 0
14:17:37.898 [INF] CalculateResult: 20220808
14:17:37.898 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
14:21:36.110 [INF] RunScan: 0
14:21:36.110 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:21)","Data":null,"DataObj":null}
14:22:37.900 [INF] RunHandleWinLoss: 0
14:22:37.900 [INF] CalculateResult: 20220808
14:22:37.900 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
14:26:36.111 [INF] RunScan: 0
14:26:36.111 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:26)","Data":null,"DataObj":null}
14:27:37.905 [INF] RunHandleWinLoss: 0
14:27:37.905 [INF] CalculateResult: 20220808
14:27:37.905 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
14:31:36.111 [INF] RunScan: 0
14:31:36.111 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:31)","Data":null,"DataObj":null}
14:32:37.906 [INF] RunHandleWinLoss: 0
14:32:37.906 [INF] CalculateResult: 20220808
14:32:37.906 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
14:36:36.112 [INF] RunScan: 0
14:36:36.112 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:36)","Data":null,"DataObj":null}
14:37:37.908 [INF] RunHandleWinLoss: 0
14:37:37.908 [INF] CalculateResult: 20220808
14:37:37.908 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
14:41:36.112 [INF] RunScan: 0
14:41:36.112 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:41)","Data":null,"DataObj":null}
14:42:37.909 [INF] RunHandleWinLoss: 0
14:42:37.909 [INF] CalculateResult: 20220808
14:42:37.909 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
14:46:36.112 [INF] RunScan: 0
14:46:36.113 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:46)","Data":null,"DataObj":null}
14:47:37.911 [INF] RunHandleWinLoss: 0
14:47:37.911 [INF] CalculateResult: 20220808
14:47:37.911 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
14:51:36.113 [INF] RunScan: 0
14:51:36.113 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:51)","Data":null,"DataObj":null}
14:52:37.912 [INF] RunHandleWinLoss: 0
14:52:37.913 [INF] CalculateResult: 20220808
14:52:37.913 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
14:56:36.113 [INF] RunScan: 0
14:56:36.113 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(14:56)","Data":null,"DataObj":null}
14:57:37.914 [INF] RunHandleWinLoss: 0
14:57:37.914 [INF] CalculateResult: 20220808
14:57:37.914 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
15:01:36.113 [INF] RunScan: 0
15:01:36.114 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:1)","Data":null,"DataObj":null}
15:02:37.916 [INF] RunHandleWinLoss: 0
15:02:37.916 [INF] CalculateResult: 20220808
15:02:37.916 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
15:06:36.114 [INF] RunScan: 0
15:06:36.114 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:6)","Data":null,"DataObj":null}
15:07:37.917 [INF] RunHandleWinLoss: 0
15:07:37.917 [INF] CalculateResult: 20220808
15:07:37.917 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
15:11:36.114 [INF] RunScan: 0
15:11:36.115 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:11)","Data":null,"DataObj":null}
15:12:37.919 [INF] RunHandleWinLoss: 0
15:12:37.919 [INF] CalculateResult: 20220808
15:12:37.919 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
15:16:36.116 [INF] RunScan: 0
15:16:36.116 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:16)","Data":null,"DataObj":null}
15:17:37.920 [INF] RunHandleWinLoss: 0
15:17:37.921 [INF] CalculateResult: 20220808
15:17:37.921 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
15:21:36.116 [INF] RunScan: 0
15:21:36.116 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:21)","Data":null,"DataObj":null}
15:22:37.922 [INF] RunHandleWinLoss: 0
15:22:37.922 [INF] CalculateResult: 20220808
15:22:37.922 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
15:26:36.116 [INF] RunScan: 0
15:26:36.116 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:26)","Data":null,"DataObj":null}
15:27:37.924 [INF] RunHandleWinLoss: 0
15:27:37.924 [INF] CalculateResult: 20220808
15:27:37.924 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
15:31:36.116 [INF] RunScan: 0
15:31:36.116 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:31)","Data":null,"DataObj":null}
15:32:37.926 [INF] RunHandleWinLoss: 0
15:32:37.926 [INF] CalculateResult: 20220808
15:32:37.926 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
15:36:36.117 [INF] RunScan: 0
15:36:36.117 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:36)","Data":null,"DataObj":null}
15:37:37.927 [INF] RunHandleWinLoss: 0
15:37:37.927 [INF] CalculateResult: 20220808
15:37:37.927 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
15:41:36.117 [INF] RunScan: 0
15:41:36.117 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:41)","Data":null,"DataObj":null}
15:41:56.659 [INF] Server full speed, frame 39609521, total 18, task 0, engine 0, bot 0, network 18, push 0, avg fps 58.47953
15:42:37.928 [INF] RunHandleWinLoss: 0
15:42:37.928 [INF] CalculateResult: 20220808
15:42:37.928 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
15:46:36.117 [INF] RunScan: 0
15:46:36.117 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:46)","Data":null,"DataObj":null}
15:47:37.929 [INF] RunHandleWinLoss: 0
15:47:37.929 [INF] CalculateResult: 20220808
15:47:37.929 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
15:51:36.117 [INF] RunScan: 0
15:51:36.117 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:51)","Data":null,"DataObj":null}
15:52:37.931 [INF] RunHandleWinLoss: 0
15:52:37.931 [INF] CalculateResult: 20220808
15:52:37.931 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
15:56:36.118 [INF] RunScan: 0
15:56:36.118 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(15:56)","Data":null,"DataObj":null}
15:57:37.932 [INF] RunHandleWinLoss: 0
15:57:37.932 [INF] CalculateResult: 20220808
15:57:37.932 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
16:01:36.118 [INF] RunScan: 0
16:01:36.118 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:1)","Data":null,"DataObj":null}
16:02:37.934 [INF] RunHandleWinLoss: 0
16:02:37.934 [INF] CalculateResult: 20220808
16:02:37.934 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
16:06:36.118 [INF] RunScan: 0
16:06:36.118 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:6)","Data":null,"DataObj":null}
16:07:37.935 [INF] RunHandleWinLoss: 0
16:07:37.935 [INF] CalculateResult: 20220808
16:07:37.935 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
16:11:36.119 [INF] RunScan: 0
16:11:36.119 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:11)","Data":null,"DataObj":null}
16:12:37.937 [INF] RunHandleWinLoss: 0
16:12:37.937 [INF] CalculateResult: 20220808
16:12:37.937 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
16:16:36.119 [INF] RunScan: 0
16:16:36.119 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:16)","Data":null,"DataObj":null}
16:17:37.942 [INF] RunHandleWinLoss: 0
16:17:37.942 [INF] CalculateResult: 20220808
16:17:37.942 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
16:21:36.119 [INF] RunScan: 0
16:21:36.119 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:21)","Data":null,"DataObj":null}
16:22:37.944 [INF] RunHandleWinLoss: 0
16:22:37.944 [INF] CalculateResult: 20220808
16:22:37.944 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
16:26:36.119 [INF] RunScan: 0
16:26:36.119 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:26)","Data":null,"DataObj":null}
16:27:37.946 [INF] RunHandleWinLoss: 0
16:27:37.946 [INF] CalculateResult: 20220808
16:27:37.946 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
16:31:36.119 [INF] RunScan: 0
16:31:36.120 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:31)","Data":null,"DataObj":null}
16:32:37.948 [INF] RunHandleWinLoss: 0
16:32:37.948 [INF] CalculateResult: 20220808
16:32:37.948 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
16:36:36.120 [INF] RunScan: 0
16:36:36.120 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:36)","Data":null,"DataObj":null}
16:37:37.949 [INF] RunHandleWinLoss: 0
16:37:37.950 [INF] CalculateResult: 20220808
16:37:37.950 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
16:41:36.120 [INF] RunScan: 0
16:41:36.120 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:41)","Data":null,"DataObj":null}
16:42:37.951 [INF] RunHandleWinLoss: 0
16:42:37.951 [INF] CalculateResult: 20220808
16:42:37.951 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
16:46:36.120 [INF] RunScan: 0
16:46:36.120 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:46)","Data":null,"DataObj":null}
16:47:37.952 [INF] RunHandleWinLoss: 0
16:47:37.952 [INF] CalculateResult: 20220808
16:47:37.952 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
16:51:36.120 [INF] RunScan: 0
16:51:36.120 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:51)","Data":null,"DataObj":null}
16:52:37.954 [INF] RunHandleWinLoss: 0
16:52:37.954 [INF] CalculateResult: 20220808
16:52:37.954 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
16:56:36.121 [INF] RunScan: 0
16:56:36.121 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:56)","Data":null,"DataObj":null}
16:57:37.955 [INF] RunHandleWinLoss: 0
16:57:37.955 [INF] CalculateResult: 20220808
16:57:37.955 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
17:01:36.121 [INF] RunScan: 0
17:01:36.121 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:1)","Data":null,"DataObj":null}
17:02:37.956 [INF] RunHandleWinLoss: 0
17:02:37.956 [INF] CalculateResult: 20220808
17:02:37.956 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
17:06:36.122 [INF] RunScan: 0
17:06:36.122 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:6)","Data":null,"DataObj":null}
17:07:37.957 [INF] RunHandleWinLoss: 0
17:07:37.958 [INF] CalculateResult: 20220808
17:07:37.958 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
17:11:36.122 [INF] RunScan: 0
17:11:36.123 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:11)","Data":null,"DataObj":null}
17:12:37.959 [INF] RunHandleWinLoss: 0
17:12:37.959 [INF] CalculateResult: 20220808
17:12:37.959 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
17:16:36.123 [INF] RunScan: 0
17:16:36.123 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:16)","Data":null,"DataObj":null}
17:17:37.961 [INF] RunHandleWinLoss: 0
17:17:37.961 [INF] CalculateResult: 20220808
17:17:37.961 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
17:21:36.123 [INF] RunScan: 0
17:21:36.123 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:21)","Data":null,"DataObj":null}
17:22:37.963 [INF] RunHandleWinLoss: 0
17:22:37.963 [INF] CalculateResult: 20220808
17:22:37.963 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
17:26:36.123 [INF] RunScan: 0
17:26:36.124 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:26)","Data":null,"DataObj":null}
17:27:37.972 [INF] RunHandleWinLoss: 0
17:27:37.972 [INF] CalculateResult: 20220808
17:27:37.972 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
17:31:36.124 [INF] RunScan: 0
17:31:36.124 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:31)","Data":null,"DataObj":null}
17:32:37.975 [INF] RunHandleWinLoss: 0
17:32:37.975 [INF] CalculateResult: 20220808
17:32:37.975 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
17:36:36.125 [INF] RunScan: 0
17:36:36.125 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:36)","Data":null,"DataObj":null}
17:37:37.977 [INF] RunHandleWinLoss: 0
17:37:37.977 [INF] CalculateResult: 20220808
17:37:37.977 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
17:41:36.125 [INF] RunScan: 0
17:41:36.125 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:41)","Data":null,"DataObj":null}
17:42:37.978 [INF] RunHandleWinLoss: 0
17:42:37.978 [INF] CalculateResult: 20220808
17:42:37.978 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
17:46:36.126 [INF] RunScan: 0
17:46:36.126 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:46)","Data":null,"DataObj":null}
17:47:37.980 [INF] RunHandleWinLoss: 0
17:47:37.980 [INF] CalculateResult: 20220808
17:47:37.980 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
17:51:36.126 [INF] RunScan: 0
17:51:36.126 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:51)","Data":null,"DataObj":null}
17:52:37.983 [INF] RunHandleWinLoss: 0
17:52:37.983 [INF] CalculateResult: 20220808
17:52:37.983 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
17:56:36.126 [INF] RunScan: 0
17:56:36.127 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:56)","Data":null,"DataObj":null}
17:57:37.986 [INF] RunHandleWinLoss: 0
17:57:37.986 [INF] CalculateResult: 20220808
17:57:37.986 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
18:01:36.127 [INF] RunScan: 0
18:01:36.128 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:1)","Data":null,"DataObj":null}
18:02:37.988 [INF] RunHandleWinLoss: 0
18:02:37.988 [INF] CalculateResult: 20220808
18:02:37.988 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
18:06:36.128 [INF] RunScan: 0
18:06:36.128 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:6)","Data":null,"DataObj":null}
18:07:37.990 [INF] RunHandleWinLoss: 0
18:07:37.990 [INF] CalculateResult: 20220808
18:07:37.990 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
18:11:36.128 [INF] RunScan: 0
18:11:36.128 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:11)","Data":null,"DataObj":null}
18:12:37.991 [INF] RunHandleWinLoss: 0
18:12:37.991 [INF] CalculateResult: 20220808
18:12:37.991 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
18:16:36.128 [INF] RunScan: 0
18:16:36.128 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:16)","Data":null,"DataObj":null}
18:17:37.993 [INF] RunHandleWinLoss: 0
18:17:37.993 [INF] CalculateResult: 20220808
18:17:37.993 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
18:21:36.129 [INF] RunScan: 0
18:21:36.129 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:21)","Data":null,"DataObj":null}
18:22:37.994 [INF] RunHandleWinLoss: 0
18:22:37.994 [INF] CalculateResult: 20220808
18:22:37.994 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
18:26:36.129 [INF] RunScan: 0
18:26:36.129 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:26)","Data":null,"DataObj":null}
18:27:37.996 [INF] RunHandleWinLoss: 0
18:27:37.996 [INF] CalculateResult: 20220808
18:27:37.996 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
18:31:36.129 [INF] RunScan: 0
18:31:36.129 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:31)","Data":null,"DataObj":null}
18:32:37.997 [INF] RunHandleWinLoss: 0
18:32:37.997 [INF] CalculateResult: 20220808
18:32:37.997 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
18:36:36.129 [INF] RunScan: 0
18:36:36.129 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:36)","Data":null,"DataObj":null}
18:37:37.998 [INF] RunHandleWinLoss: 0
18:37:37.999 [INF] CalculateResult: 20220808
18:37:37.999 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
18:41:36.131 [INF] RunScan: 0
18:41:36.429 [INF] ScanXskt result: 76821-26769-25934-27097-43807-18207-37835-64746-96956-71714-9638-3327-2771-8852-5803-4206-1658-8429-7592-1323-531-716-624-26-15-12-43
18:41:36.430 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng 5.","Data":null,"DataObj":null}
18:42:38.002 [INF] RunHandleWinLoss: 0
18:42:38.002 [INF] CalculateResult: 20220808
18:42:38.002 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
18:45:36.502 [DBG] Client connected from 127.0.0.1:33582
no ex
18:45:36.502 [DBG] 1179 bytes read
no ex
18:45:36.504 [DBG] Building Hybi-14 Response
no ex
18:45:36.505 [DBG] Sent 129 bytes
no ex
18:45:37.687 [DBG] 31 bytes read
no ex
18:45:37.688 [DBG] 113 bytes read
no ex
18:45:37.688 [INF] GET: http://127.0.0.1:8081/api?c=3&un=doibac9686&pw=8571816853403089f72daa81569f75c3&pf=web&at=
18:45:37.688 [DBG] Sent 30 bytes
no ex
18:45:37.707 [INF] GET response: {"success":true,"message":null,"errorCode":"0","data":null,"sessionKey":"eyJuaWNrbmFtZSI6ImRvaWJhYzk2ODY4OCIsImF2YXRhciI6IjAiLCJ2aW5Ub3RhbCI6MCwieHVUb3RhbCI6NTAwMDAwLCJ2aXBwb2ludCI6MCwidmlwcG9pbnRTYXZlIjowLCJjcmVhdGVUaW1lIjoiMDgtMDgtMjAyMiIsImlwQWRkcmVzcyI6IjEyNy4wLjAuMSIsImNlcnRpZmljYXRlIjpmYWxzZSwibHVja3lSb3RhdGUiOjAsImRhaUx5IjowLCJtb2JpbGVTZWN1cmUiOjAsImJpcnRoZGF5IjoiIiwiYXBwU2VjdXJlIjowLCJ1c2VybmFtZSI6ImRvaWJhYzk2ODYiLCJlbWFpbCI6MCwiYWRkcmVzcyI6bnVsbCwidmVyaWZ5TW9iaWxlIjpmYWxzZX0=","accessToken":"8a0fea86ae7b343136136545aa25a030"}

18:45:37.717 [DBG] Sent 5729 bytes
no ex
18:45:38.464 [DBG] Sent 184 bytes
no ex
18:45:40.686 [DBG] 31 bytes read
no ex
18:45:40.686 [DBG] Sent 30 bytes
no ex
18:45:43.470 [DBG] Sent 184 bytes
no ex
18:45:43.718 [DBG] 31 bytes read
no ex
18:45:43.719 [DBG] Sent 30 bytes
no ex
18:45:45.192 [DBG] 32 bytes read
no ex
18:45:45.196 [DBG] Sent 41 bytes
no ex
18:45:46.689 [DBG] 31 bytes read
no ex
18:45:46.689 [DBG] Sent 30 bytes
no ex
18:45:48.458 [DBG] Sent 184 bytes
no ex
18:45:49.686 [DBG] 31 bytes read
no ex
18:45:49.686 [DBG] Sent 30 bytes
no ex
18:45:52.706 [DBG] 31 bytes read
no ex
18:45:52.706 [DBG] Sent 30 bytes
no ex
18:45:53.459 [DBG] Sent 184 bytes
no ex
18:45:55.703 [DBG] 31 bytes read
no ex
18:45:55.703 [DBG] Sent 30 bytes
no ex
18:45:58.461 [DBG] Sent 184 bytes
no ex
18:45:58.699 [DBG] 31 bytes read
no ex
18:45:58.700 [DBG] Sent 30 bytes
no ex
18:46:01.704 [DBG] 31 bytes read
no ex
18:46:01.705 [DBG] Sent 30 bytes
no ex
18:46:03.459 [DBG] Sent 184 bytes
no ex
18:46:04.711 [DBG] 31 bytes read
no ex
18:46:04.711 [DBG] Sent 30 bytes
no ex
18:46:07.740 [DBG] 31 bytes read
no ex
18:46:07.741 [DBG] Sent 30 bytes
no ex
18:46:08.465 [DBG] Sent 184 bytes
no ex
18:46:10.686 [DBG] 31 bytes read
no ex
18:46:10.687 [DBG] Sent 30 bytes
no ex
18:46:13.459 [DBG] Sent 184 bytes
no ex
18:46:13.706 [DBG] 31 bytes read
no ex
18:46:13.706 [DBG] Sent 30 bytes
no ex
18:46:16.704 [DBG] 31 bytes read
no ex
18:46:16.704 [DBG] Sent 30 bytes
no ex
18:46:18.465 [DBG] Sent 184 bytes
no ex
18:46:19.700 [DBG] 31 bytes read
no ex
18:46:19.701 [DBG] Sent 30 bytes
no ex
18:46:22.707 [DBG] 31 bytes read
no ex
18:46:22.707 [DBG] Sent 30 bytes
no ex
18:46:23.473 [DBG] Sent 184 bytes
no ex
18:46:25.687 [DBG] 31 bytes read
no ex
18:46:25.687 [DBG] Sent 30 bytes
no ex
18:46:28.475 [DBG] Sent 184 bytes
no ex
18:46:28.687 [DBG] 31 bytes read
no ex
18:46:28.687 [DBG] Sent 30 bytes
no ex
18:46:31.688 [DBG] 31 bytes read
no ex
18:46:31.688 [DBG] Sent 30 bytes
no ex
18:46:33.468 [DBG] Sent 184 bytes
no ex
18:46:34.715 [DBG] 31 bytes read
no ex
18:46:34.715 [DBG] Sent 30 bytes
no ex
18:46:36.430 [INF] RunScan: 0
18:46:36.431 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
18:46:37.688 [DBG] 31 bytes read
no ex
18:46:37.688 [DBG] Sent 30 bytes
no ex
18:46:37.768 [DBG] 8 bytes read
no ex
18:46:37.768 [DBG] Sent 4 bytes
no ex
18:47:38.004 [INF] RunHandleWinLoss: 0
18:47:38.004 [INF] CalculateResult: 20220808
18:47:38.004 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
18:51:36.431 [INF] RunScan: 0
18:51:36.431 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
18:52:38.005 [INF] RunHandleWinLoss: 0
18:52:38.006 [INF] CalculateResult: 20220808
18:52:38.006 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
18:56:36.431 [INF] RunScan: 0
18:56:36.431 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
18:57:38.007 [INF] RunHandleWinLoss: 0
18:57:38.007 [INF] CalculateResult: 20220808
18:57:38.007 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
19:01:36.432 [INF] RunScan: 0
19:01:36.432 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:02:38.008 [INF] RunHandleWinLoss: 0
19:02:38.008 [INF] CalculateResult: 20220808
19:02:38.008 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
19:06:36.434 [INF] RunScan: 0
19:06:36.434 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:07:38.010 [INF] RunHandleWinLoss: 0
19:07:38.010 [INF] CalculateResult: 20220808
19:07:38.010 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
19:11:36.434 [INF] RunScan: 0
19:11:36.434 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:12:38.011 [INF] RunHandleWinLoss: 0
19:12:38.011 [INF] CalculateResult: 20220808
19:12:38.011 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
19:16:36.434 [INF] RunScan: 0
19:16:36.434 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:17:38.013 [INF] RunHandleWinLoss: 0
19:17:38.013 [INF] CalculateResult: 20220808
19:17:38.013 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
19:21:36.434 [INF] RunScan: 0
19:21:36.434 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:22:38.015 [INF] RunHandleWinLoss: 0
19:22:38.015 [INF] CalculateResult: 20220808
19:22:38.015 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
19:26:36.435 [INF] RunScan: 0
19:26:36.435 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:27:38.017 [INF] RunHandleWinLoss: 0
19:27:38.017 [INF] CalculateResult: 20220808
19:27:38.017 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
19:31:36.435 [INF] RunScan: 0
19:31:36.435 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:32:38.024 [INF] RunHandleWinLoss: 0
19:32:38.024 [INF] CalculateResult: 20220808
19:32:38.024 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
19:36:36.435 [INF] RunScan: 0
19:36:36.435 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:37:38.026 [INF] RunHandleWinLoss: 0
19:37:38.026 [INF] CalculateResult: 20220808
19:37:38.026 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
19:41:36.435 [INF] RunScan: 0
19:41:36.435 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:42:38.028 [INF] RunHandleWinLoss: 0
19:42:38.028 [INF] CalculateResult: 20220808
19:42:38.028 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
19:46:36.436 [INF] RunScan: 0
19:46:36.436 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:47:38.030 [INF] RunHandleWinLoss: 0
19:47:38.030 [INF] CalculateResult: 20220808
19:47:38.030 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
19:48:42.658 [DBG] Client connected from 154.89.5.110:42314
no ex
19:48:42.658 [DBG] 243 bytes read
no ex
19:48:54.624 [DBG] 0 bytes read. Closing.
no ex
19:48:54.663 [DBG] Client connected from 154.89.5.110:46652
no ex
19:48:54.663 [DBG] 243 bytes read
no ex
19:49:06.627 [DBG] 0 bytes read. Closing.
no ex
19:49:06.664 [DBG] Client connected from 154.89.5.110:50148
no ex
19:49:06.664 [DBG] 0 bytes read. Closing.
no ex
19:49:06.696 [DBG] Client connected from 154.89.5.110:50160
no ex
19:49:06.696 [DBG] 57 bytes read
no ex
19:49:18.662 [DBG] 0 bytes read. Closing.
no ex
19:49:18.696 [DBG] Client connected from 154.89.5.110:54098
no ex
19:49:30.662 [DBG] 0 bytes read. Closing.
no ex
19:49:30.702 [DBG] Client connected from 154.89.5.110:57980
no ex
19:49:30.702 [DBG] 16 bytes read
no ex
19:49:42.666 [DBG] 0 bytes read. Closing.
no ex
19:51:36.436 [INF] RunScan: 0
19:51:36.436 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:52:38.032 [INF] RunHandleWinLoss: 0
19:52:38.032 [INF] CalculateResult: 20220808
19:52:38.032 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
19:56:36.436 [INF] RunScan: 0
19:56:36.436 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:57:38.036 [INF] RunHandleWinLoss: 0
19:57:38.037 [INF] CalculateResult: 20220808
19:57:38.037 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
20:01:36.436 [INF] RunScan: 0
20:01:36.436 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:1)","Data":null,"DataObj":null}
20:02:38.038 [INF] RunHandleWinLoss: 0
20:02:38.038 [INF] CalculateResult: 20220808
20:02:38.038 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
20:06:36.436 [INF] RunScan: 0
20:06:36.436 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:6)","Data":null,"DataObj":null}
20:07:38.039 [INF] RunHandleWinLoss: 0
20:07:38.039 [INF] CalculateResult: 20220808
20:07:38.039 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
20:11:36.437 [INF] RunScan: 0
20:11:36.437 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:11)","Data":null,"DataObj":null}
20:12:38.043 [INF] RunHandleWinLoss: 0
20:12:38.043 [INF] CalculateResult: 20220808
20:12:38.043 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
20:16:36.437 [INF] RunScan: 0
20:16:36.437 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:16)","Data":null,"DataObj":null}
20:17:38.044 [INF] RunHandleWinLoss: 0
20:17:38.044 [INF] CalculateResult: 20220808
20:17:38.044 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
20:21:36.437 [INF] RunScan: 0
20:21:36.437 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:21)","Data":null,"DataObj":null}
20:22:38.046 [INF] RunHandleWinLoss: 0
20:22:38.046 [INF] CalculateResult: 20220808
20:22:38.046 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
20:26:36.437 [INF] RunScan: 0
20:26:36.437 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:26)","Data":null,"DataObj":null}
20:27:38.047 [INF] RunHandleWinLoss: 0
20:27:38.047 [INF] CalculateResult: 20220808
20:27:38.047 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
20:31:36.438 [INF] RunScan: 0
20:31:36.438 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:31)","Data":null,"DataObj":null}
20:32:38.049 [INF] RunHandleWinLoss: 0
20:32:38.049 [INF] CalculateResult: 20220808
20:32:38.049 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
20:36:36.438 [INF] RunScan: 0
20:36:36.438 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:36)","Data":null,"DataObj":null}
20:37:38.051 [INF] RunHandleWinLoss: 0
20:37:38.051 [INF] CalculateResult: 20220808
20:37:38.051 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
20:41:36.438 [INF] RunScan: 0
20:41:36.438 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:41)","Data":null,"DataObj":null}
20:42:38.053 [INF] RunHandleWinLoss: 0
20:42:38.053 [INF] CalculateResult: 20220808
20:42:38.053 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
20:46:36.438 [INF] RunScan: 0
20:46:36.438 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:46)","Data":null,"DataObj":null}
20:47:38.054 [INF] RunHandleWinLoss: 0
20:47:38.054 [INF] CalculateResult: 20220808
20:47:38.054 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
20:51:36.438 [INF] RunScan: 0
20:51:36.439 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:51)","Data":null,"DataObj":null}
20:52:38.056 [INF] RunHandleWinLoss: 0
20:52:38.056 [INF] CalculateResult: 20220808
20:52:38.056 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
20:56:36.439 [INF] RunScan: 0
20:56:36.439 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:56)","Data":null,"DataObj":null}
20:57:38.058 [INF] RunHandleWinLoss: 0
20:57:38.058 [INF] CalculateResult: 20220808
20:57:38.058 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
21:01:36.439 [INF] RunScan: 0
21:01:36.439 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:1)","Data":null,"DataObj":null}
21:02:38.060 [INF] RunHandleWinLoss: 0
21:02:38.060 [INF] CalculateResult: 20220808
21:02:38.060 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
21:06:36.439 [INF] RunScan: 0
21:06:36.439 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:6)","Data":null,"DataObj":null}
21:07:38.061 [INF] RunHandleWinLoss: 0
21:07:38.061 [INF] CalculateResult: 20220808
21:07:38.061 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
21:11:36.439 [INF] RunScan: 0
21:11:36.439 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:11)","Data":null,"DataObj":null}
21:12:38.062 [INF] RunHandleWinLoss: 0
21:12:38.062 [INF] CalculateResult: 20220808
21:12:38.062 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
21:16:36.439 [INF] RunScan: 0
21:16:36.440 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:16)","Data":null,"DataObj":null}
21:17:38.064 [INF] RunHandleWinLoss: 0
21:17:38.064 [INF] CalculateResult: 20220808
21:17:38.064 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
21:21:36.440 [INF] RunScan: 0
21:21:36.440 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:21)","Data":null,"DataObj":null}
21:22:38.067 [INF] RunHandleWinLoss: 0
21:22:38.067 [INF] CalculateResult: 20220808
21:22:38.067 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
21:26:36.440 [INF] RunScan: 0
21:26:36.440 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:26)","Data":null,"DataObj":null}
21:27:38.068 [INF] RunHandleWinLoss: 0
21:27:38.069 [INF] CalculateResult: 20220808
21:27:38.069 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
21:31:36.440 [INF] RunScan: 0
21:31:36.440 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:31)","Data":null,"DataObj":null}
21:32:38.070 [INF] RunHandleWinLoss: 0
21:32:38.070 [INF] CalculateResult: 20220808
21:32:38.070 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
21:36:36.440 [INF] RunScan: 0
21:36:36.440 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:36)","Data":null,"DataObj":null}
21:37:38.072 [INF] RunHandleWinLoss: 0
21:37:38.072 [INF] CalculateResult: 20220808
21:37:38.072 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
21:41:36.441 [INF] RunScan: 0
21:41:36.441 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:41)","Data":null,"DataObj":null}
21:42:38.073 [INF] RunHandleWinLoss: 0
21:42:38.074 [INF] CalculateResult: 20220808
21:42:38.074 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
21:46:36.441 [INF] RunScan: 0
21:46:36.441 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:46)","Data":null,"DataObj":null}
21:47:38.075 [INF] RunHandleWinLoss: 0
21:47:38.075 [INF] CalculateResult: 20220808
21:47:38.075 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
21:51:36.441 [INF] RunScan: 0
21:51:36.441 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:51)","Data":null,"DataObj":null}
21:52:38.077 [INF] RunHandleWinLoss: 0
21:52:38.077 [INF] CalculateResult: 20220808
21:52:38.077 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
21:56:36.447 [INF] RunScan: 0
21:56:36.447 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:56)","Data":null,"DataObj":null}
21:57:38.078 [INF] RunHandleWinLoss: 0
21:57:38.078 [INF] CalculateResult: 20220808
21:57:38.078 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
22:01:36.447 [INF] RunScan: 0
22:01:36.448 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:1)","Data":null,"DataObj":null}
22:02:38.080 [INF] RunHandleWinLoss: 0
22:02:38.080 [INF] CalculateResult: 20220808
22:02:38.080 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
22:06:36.448 [INF] RunScan: 0
22:06:36.448 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:6)","Data":null,"DataObj":null}
22:07:38.081 [INF] RunHandleWinLoss: 0
22:07:38.081 [INF] CalculateResult: 20220808
22:07:38.081 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
22:11:36.448 [INF] RunScan: 0
22:11:36.448 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:11)","Data":null,"DataObj":null}
22:12:38.083 [INF] RunHandleWinLoss: 0
22:12:38.083 [INF] CalculateResult: 20220808
22:12:38.083 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
22:16:36.448 [INF] RunScan: 0
22:16:36.449 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:16)","Data":null,"DataObj":null}
22:17:38.085 [INF] RunHandleWinLoss: 0
22:17:38.085 [INF] CalculateResult: 20220808
22:17:38.085 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
22:20:15.599 [DBG] Client connected from 162.142.125.9:45738
no ex
22:20:16.628 [DBG] Error while reading
System.IO.IOException: Unable to read data from the transport connection: Connection reset by peer.
 ---> System.Net.Sockets.SocketException (104): Connection reset by peer
   at System.Net.Sockets.Socket.EndReceive(IAsyncResult asyncResult)
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   --- End of inner exception stack trace ---
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
22:20:16.817 [DBG] Client connected from 162.142.125.9:44176
no ex
22:20:16.817 [DBG] 243 bytes read
no ex
22:20:19.822 [DBG] Error while reading
System.IO.IOException: Unable to read data from the transport connection: Connection reset by peer.
 ---> System.Net.Sockets.SocketException (104): Connection reset by peer
   at System.Net.Sockets.Socket.EndReceive(IAsyncResult asyncResult)
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   --- End of inner exception stack trace ---
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
22:20:20.011 [DBG] Client connected from 162.142.125.9:48016
no ex
22:20:21.018 [DBG] 44 bytes read
no ex
22:20:21.018 [DBG] Error while reading
Fleck.WebSocketException: Exception of type 'Fleck.WebSocketException' was thrown.
   at Fleck.HandlerFactory.BuildHandler(WebSocketHttpRequest request, Action`1 onMessage, Action onClose, Action`1 onBinary, Action`1 onPing, Action`1 onPong) in /var/app/banca/Fleck/HandlerFactory.cs:line 25
   at Fleck.WebSocketServer.<>c__DisplayClass42_0.<OnClientConnect>b__1(WebSocketHttpRequest r) in /var/app/banca/Fleck/WebSocketServer.cs:line 164
   at Fleck.WebSocketConnection.CreateHandler(IEnumerable`1 data) in /var/app/banca/Fleck/WebSocketConnection.cs:line 159
   at Fleck.WebSocketConnection.<>c__DisplayClass61_0.<Read>b__0(Int32 r) in /var/app/banca/Fleck/WebSocketConnection.cs:line 193
   at Fleck.SocketWrapper.<>c__DisplayClass24_0.<Receive>b__1(Task`1 t) in /var/app/banca/Fleck/SocketWrapper.cs:line 126
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
22:20:21.417 [DBG] Client connected from 162.142.125.9:36498
no ex
22:20:21.420 [DBG] Error while reading
System.IO.IOException: Unable to read data from the transport connection: Connection reset by peer.
 ---> System.Net.Sockets.SocketException (104): Connection reset by peer
   at System.Net.Sockets.Socket.EndReceive(IAsyncResult asyncResult)
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   --- End of inner exception stack trace ---
   at System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
22:21:36.449 [INF] RunScan: 0
22:21:36.449 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:21)","Data":null,"DataObj":null}
22:22:38.087 [INF] RunHandleWinLoss: 0
22:22:38.087 [INF] CalculateResult: 20220808
22:22:38.087 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
22:26:36.449 [INF] RunScan: 0
22:26:36.449 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:26)","Data":null,"DataObj":null}
22:27:38.088 [INF] RunHandleWinLoss: 0
22:27:38.088 [INF] CalculateResult: 20220808
22:27:38.088 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
22:31:36.449 [INF] RunScan: 0
22:31:36.449 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:31)","Data":null,"DataObj":null}
22:32:38.090 [INF] RunHandleWinLoss: 0
22:32:38.090 [INF] CalculateResult: 20220808
22:32:38.090 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
22:36:36.449 [INF] RunScan: 0
22:36:36.450 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:36)","Data":null,"DataObj":null}
22:37:38.093 [INF] RunHandleWinLoss: 0
22:37:38.093 [INF] CalculateResult: 20220808
22:37:38.093 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
22:41:36.450 [INF] RunScan: 0
22:41:36.450 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:41)","Data":null,"DataObj":null}
22:42:38.095 [INF] RunHandleWinLoss: 0
22:42:38.095 [INF] CalculateResult: 20220808
22:42:38.095 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
22:46:36.450 [INF] RunScan: 0
22:46:36.450 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:46)","Data":null,"DataObj":null}
22:47:38.096 [INF] RunHandleWinLoss: 0
22:47:38.096 [INF] CalculateResult: 20220808
22:47:38.096 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
22:51:36.450 [INF] RunScan: 0
22:51:36.450 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:51)","Data":null,"DataObj":null}
22:52:38.100 [INF] RunHandleWinLoss: 0
22:52:38.100 [INF] CalculateResult: 20220808
22:52:38.100 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
22:56:36.451 [INF] RunScan: 0
22:56:36.451 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(22:56)","Data":null,"DataObj":null}
22:57:38.102 [INF] RunHandleWinLoss: 0
22:57:38.102 [INF] CalculateResult: 20220808
22:57:38.102 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
23:01:36.451 [INF] RunScan: 0
23:01:36.452 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:1)","Data":null,"DataObj":null}
23:02:38.103 [INF] RunHandleWinLoss: 0
23:02:38.103 [INF] CalculateResult: 20220808
23:02:38.103 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
23:06:36.452 [INF] RunScan: 0
23:06:36.452 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:6)","Data":null,"DataObj":null}
23:07:38.105 [INF] RunHandleWinLoss: 0
23:07:38.105 [INF] CalculateResult: 20220808
23:07:38.105 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
23:11:36.454 [INF] RunScan: 0
23:11:36.454 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:11)","Data":null,"DataObj":null}
23:12:38.106 [INF] RunHandleWinLoss: 0
23:12:38.106 [INF] CalculateResult: 20220808
23:12:38.106 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
23:16:36.454 [INF] RunScan: 0
23:16:36.454 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:16)","Data":null,"DataObj":null}
23:17:38.109 [INF] RunHandleWinLoss: 0
23:17:38.109 [INF] CalculateResult: 20220808
23:17:38.109 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
23:21:36.455 [INF] RunScan: 0
23:21:36.455 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:21)","Data":null,"DataObj":null}
23:22:38.110 [INF] RunHandleWinLoss: 0
23:22:38.110 [INF] CalculateResult: 20220808
23:22:38.110 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
23:26:36.455 [INF] RunScan: 0
23:26:36.455 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:26)","Data":null,"DataObj":null}
23:27:38.113 [INF] RunHandleWinLoss: 0
23:27:38.113 [INF] CalculateResult: 20220808
23:27:38.113 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
23:31:36.455 [INF] RunScan: 0
23:31:36.455 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:31)","Data":null,"DataObj":null}
23:32:38.114 [INF] RunHandleWinLoss: 0
23:32:38.115 [INF] CalculateResult: 20220808
23:32:38.115 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
23:36:36.455 [INF] RunScan: 0
23:36:36.456 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:36)","Data":null,"DataObj":null}
23:37:38.117 [INF] RunHandleWinLoss: 0
23:37:38.117 [INF] CalculateResult: 20220808
23:37:38.117 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
23:41:36.456 [INF] RunScan: 0
23:41:36.456 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:41)","Data":null,"DataObj":null}
23:42:38.118 [INF] RunHandleWinLoss: 0
23:42:38.118 [INF] CalculateResult: 20220808
23:42:38.118 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
23:46:36.456 [INF] RunScan: 0
23:46:36.456 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:46)","Data":null,"DataObj":null}
23:47:38.119 [INF] RunHandleWinLoss: 0
23:47:38.119 [INF] CalculateResult: 20220808
23:47:38.119 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
23:49:20.104 [DBG] Client connected from 139.59.86.253:53188
no ex
23:49:20.104 [DBG] 204 bytes read
no ex
23:49:20.105 [DBG] Error while reading
Fleck.WebSocketException: Exception of type 'Fleck.WebSocketException' was thrown.
   at Fleck.HandlerFactory.BuildHandler(WebSocketHttpRequest request, Action`1 onMessage, Action onClose, Action`1 onBinary, Action`1 onPing, Action`1 onPong) in /var/app/banca/Fleck/HandlerFactory.cs:line 25
   at Fleck.WebSocketServer.<>c__DisplayClass42_0.<OnClientConnect>b__1(WebSocketHttpRequest r) in /var/app/banca/Fleck/WebSocketServer.cs:line 164
   at Fleck.WebSocketConnection.CreateHandler(IEnumerable`1 data) in /var/app/banca/Fleck/WebSocketConnection.cs:line 159
   at Fleck.WebSocketConnection.<>c__DisplayClass61_0.<Read>b__0(Int32 r) in /var/app/banca/Fleck/WebSocketConnection.cs:line 193
   at Fleck.SocketWrapper.<>c__DisplayClass24_0.<Receive>b__1(Task`1 t) in /var/app/banca/Fleck/SocketWrapper.cs:line 126
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
23:49:48.271 [DBG] Client connected from 139.59.86.253:53738
no ex
23:49:48.271 [DBG] 6 bytes read
no ex
23:50:03.272 [DBG] 0 bytes read. Closing.
no ex
23:51:36.456 [INF] RunScan: 0
23:51:36.456 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:51)","Data":null,"DataObj":null}
23:52:38.122 [INF] RunHandleWinLoss: 0
23:52:38.122 [INF] CalculateResult: 20220808
23:52:38.122 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
23:56:36.456 [INF] RunScan: 0
23:56:36.457 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(23:56)","Data":null,"DataObj":null}
23:57:38.125 [INF] RunHandleWinLoss: 0
23:57:38.125 [INF] CalculateResult: 20220808
23:57:38.125 [INF] CalculateResult 2: select * from loto_request where Session=20220808 AND NOT Status='1'
