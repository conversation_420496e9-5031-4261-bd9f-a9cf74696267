16:38:25.155 [INF] Current version 1.0.0
16:38:25.577 [INF] Starting server
16:38:25.597 [INF] Allow solo on server 8888
16:38:25.598 [INF] Start bot on server 8888
16:38:25.598 [INF] Server started
16:38:25.717 [INF] Litenet server started at 8888
16:38:25.724 [INF] Server started at ws://0.0.0.0:2083 (actual port 2083)
no ex
16:38:25.726 [INF] Websocket server started at 2083
16:38:25.734 [INF] 8888 bot: 0, peer: 0, ccu: 0
16:39:25.037 [INF] RunScan: 0
16:39:25.202 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:39)","Data":null,"DataObj":null}
16:40:25.038 [INF] RunHandleWinLoss: 0
16:40:25.042 [INF] CalculateResult: 20250723
16:40:25.049 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
16:44:25.204 [INF] RunScan: 0
16:44:25.205 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:44)","Data":null,"DataObj":null}
16:45:25.081 [INF] RunHandleWinLoss: 0
16:45:25.084 [INF] CalculateResult: 20250723
16:45:25.084 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
16:49:25.206 [INF] RunScan: 0
16:49:25.208 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:49)","Data":null,"DataObj":null}
16:50:25.090 [INF] RunHandleWinLoss: 0
16:50:25.095 [INF] CalculateResult: 20250723
16:50:25.096 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
16:54:25.208 [INF] RunScan: 0
16:54:25.211 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:54)","Data":null,"DataObj":null}
16:55:25.103 [INF] RunHandleWinLoss: 0
16:55:25.105 [INF] CalculateResult: 20250723
16:55:25.105 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
16:59:25.211 [INF] RunScan: 0
16:59:25.212 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(16:59)","Data":null,"DataObj":null}
17:00:25.110 [INF] RunHandleWinLoss: 0
17:00:25.110 [INF] CalculateResult: 20250723
17:00:25.111 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
17:04:25.213 [INF] RunScan: 0
17:04:25.214 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:4)","Data":null,"DataObj":null}
17:05:25.115 [INF] RunHandleWinLoss: 0
17:05:25.116 [INF] CalculateResult: 20250723
17:05:25.116 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
17:09:25.215 [INF] RunScan: 0
17:09:25.216 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:9)","Data":null,"DataObj":null}
17:10:25.119 [INF] RunHandleWinLoss: 0
17:10:25.120 [INF] CalculateResult: 20250723
17:10:25.120 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
17:13:56.485 [DBG] Client connected from 127.0.0.1:38688
no ex
17:13:56.490 [DBG] 352 bytes read
no ex
17:13:56.569 [DBG] Error while reading
Fleck.WebSocketException: Exception of type 'Fleck.WebSocketException' was thrown.
   at Fleck.HandlerFactory.BuildHandler(WebSocketHttpRequest request, Action`1 onMessage, Action onClose, Action`1 onBinary, Action`1 onPing, Action`1 onPong) in /var/app/banca/Fleck/HandlerFactory.cs:line 25
   at Fleck.WebSocketServer.<>c__DisplayClass42_0.<OnClientConnect>b__1(WebSocketHttpRequest r) in /var/app/banca/Fleck/WebSocketServer.cs:line 164
   at Fleck.WebSocketConnection.CreateHandler(IEnumerable`1 data) in /var/app/banca/Fleck/WebSocketConnection.cs:line 159
   at Fleck.WebSocketConnection.<>c__DisplayClass61_0.<Read>b__0(Int32 r) in /var/app/banca/Fleck/WebSocketConnection.cs:line 193
   at Fleck.SocketWrapper.<>c__DisplayClass24_0.<Receive>b__1(Task`1 t) in /var/app/banca/Fleck/SocketWrapper.cs:line 126
   at System.Threading.Tasks.ContinuationTaskFromResultTask`1.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__274_0(Object obj)
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
17:14:05.188 [DBG] Client connected from 127.0.0.1:38708
no ex
17:14:05.189 [DBG] 350 bytes read
no ex
17:14:05.192 [DBG] Error while reading
Fleck.WebSocketException: Exception of type 'Fleck.WebSocketException' was thrown.
   at Fleck.HandlerFactory.BuildHandler(WebSocketHttpRequest request, Action`1 onMessage, Action onClose, Action`1 onBinary, Action`1 onPing, Action`1 onPong) in /var/app/banca/Fleck/HandlerFactory.cs:line 25
   at Fleck.WebSocketServer.<>c__DisplayClass42_0.<OnClientConnect>b__1(WebSocketHttpRequest r) in /var/app/banca/Fleck/WebSocketServer.cs:line 164
   at Fleck.WebSocketConnection.CreateHandler(IEnumerable`1 data) in /var/app/banca/Fleck/WebSocketConnection.cs:line 159
   at Fleck.WebSocketConnection.<>c__DisplayClass61_0.<Read>b__0(Int32 r) in /var/app/banca/Fleck/WebSocketConnection.cs:line 193
   at Fleck.SocketWrapper.<>c__DisplayClass24_0.<Receive>b__1(Task`1 t) in /var/app/banca/Fleck/SocketWrapper.cs:line 126
   at System.Threading.Tasks.ContinuationTaskFromResultTask`1.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__274_0(Object obj)
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location where exception was thrown ---
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
17:14:25.217 [INF] RunScan: 0
17:14:25.217 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:14)","Data":null,"DataObj":null}
17:15:25.124 [INF] RunHandleWinLoss: 0
17:15:25.125 [INF] CalculateResult: 20250723
17:15:25.125 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
17:19:25.218 [INF] RunScan: 0
17:19:25.219 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:19)","Data":null,"DataObj":null}
17:20:25.127 [INF] RunHandleWinLoss: 0
17:20:25.127 [INF] CalculateResult: 20250723
17:20:25.127 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
17:24:25.220 [INF] RunScan: 0
17:24:25.221 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:24)","Data":null,"DataObj":null}
17:25:25.130 [INF] RunHandleWinLoss: 0
17:25:25.131 [INF] CalculateResult: 20250723
17:25:25.131 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
17:29:25.222 [INF] RunScan: 0
17:29:25.223 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:29)","Data":null,"DataObj":null}
17:30:25.134 [INF] RunHandleWinLoss: 0
17:30:25.135 [INF] CalculateResult: 20250723
17:30:25.135 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
17:34:25.223 [INF] RunScan: 0
17:34:25.224 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:34)","Data":null,"DataObj":null}
17:35:25.137 [INF] RunHandleWinLoss: 0
17:35:25.138 [INF] CalculateResult: 20250723
17:35:25.138 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
17:39:25.224 [INF] RunScan: 0
17:39:25.225 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:39)","Data":null,"DataObj":null}
17:40:25.141 [INF] RunHandleWinLoss: 0
17:40:25.141 [INF] CalculateResult: 20250723
17:40:25.141 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
17:44:25.226 [INF] RunScan: 0
17:44:25.226 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:44)","Data":null,"DataObj":null}
17:45:25.144 [INF] RunHandleWinLoss: 0
17:45:25.144 [INF] CalculateResult: 20250723
17:45:25.144 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
17:49:25.227 [INF] RunScan: 0
17:49:25.227 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:49)","Data":null,"DataObj":null}
17:50:25.147 [INF] RunHandleWinLoss: 0
17:50:25.147 [INF] CalculateResult: 20250723
17:50:25.147 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
17:54:25.228 [INF] RunScan: 0
17:54:25.229 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:54)","Data":null,"DataObj":null}
17:55:25.150 [INF] RunHandleWinLoss: 0
17:55:25.150 [INF] CalculateResult: 20250723
17:55:25.150 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
17:59:25.229 [INF] RunScan: 0
17:59:25.230 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(17:59)","Data":null,"DataObj":null}
18:00:25.153 [INF] RunHandleWinLoss: 0
18:00:25.154 [INF] CalculateResult: 20250723
18:00:25.154 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
18:04:25.230 [INF] RunScan: 0
18:04:25.231 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:4)","Data":null,"DataObj":null}
18:05:25.156 [INF] RunHandleWinLoss: 0
18:05:25.157 [INF] CalculateResult: 20250723
18:05:25.157 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
18:09:25.231 [INF] RunScan: 0
18:09:25.232 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:9)","Data":null,"DataObj":null}
18:10:25.159 [INF] RunHandleWinLoss: 0
18:10:25.160 [INF] CalculateResult: 20250723
18:10:25.160 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
18:14:25.232 [INF] RunScan: 0
18:14:25.233 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:14)","Data":null,"DataObj":null}
18:15:25.163 [INF] RunHandleWinLoss: 0
18:15:25.163 [INF] CalculateResult: 20250723
18:15:25.163 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
18:19:25.233 [INF] RunScan: 0
18:19:25.234 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:19)","Data":null,"DataObj":null}
18:20:25.167 [INF] RunHandleWinLoss: 0
18:20:25.167 [INF] CalculateResult: 20250723
18:20:25.167 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
18:24:25.234 [INF] RunScan: 0
18:24:25.235 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:24)","Data":null,"DataObj":null}
18:25:25.170 [INF] RunHandleWinLoss: 0
18:25:25.171 [INF] CalculateResult: 20250723
18:25:25.171 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
18:29:25.235 [INF] RunScan: 0
18:29:25.237 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:29)","Data":null,"DataObj":null}
18:30:25.174 [INF] RunHandleWinLoss: 0
18:30:25.174 [INF] CalculateResult: 20250723
18:30:25.174 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
18:34:25.237 [INF] RunScan: 0
18:34:25.238 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:34)","Data":null,"DataObj":null}
18:35:25.177 [INF] RunHandleWinLoss: 0
18:35:25.177 [INF] CalculateResult: 20250723
18:35:25.177 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
18:39:25.239 [INF] RunScan: 0
18:39:25.240 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(18:39)","Data":null,"DataObj":null}
18:40:25.180 [INF] RunHandleWinLoss: 0
18:40:25.181 [INF] CalculateResult: 20250723
18:40:25.181 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
18:44:25.240 [INF] RunScan: 0
18:44:25.685 [INF] ScanXskt result: 49821-32917-33514-19274-56020-92889-58549-60897-04128-80411-1208-4426-9178-6729-2330-7794-0080-9865-7462-5166-151-696-341-48-70-36-03
18:44:25.686 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng 5.","Data":null,"DataObj":null}
18:45:25.184 [INF] RunHandleWinLoss: 0
18:45:25.184 [INF] CalculateResult: 20250723
18:45:25.184 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
18:49:25.686 [INF] RunScan: 0
18:49:25.694 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
18:50:25.188 [INF] RunHandleWinLoss: 0
18:50:25.188 [INF] CalculateResult: 20250723
18:50:25.188 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
18:54:25.694 [INF] RunScan: 0
18:54:25.696 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
18:55:25.193 [INF] RunHandleWinLoss: 0
18:55:25.194 [INF] CalculateResult: 20250723
18:55:25.194 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
18:59:25.696 [INF] RunScan: 0
18:59:25.697 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:00:25.196 [INF] RunHandleWinLoss: 0
19:00:25.197 [INF] CalculateResult: 20250723
19:00:25.197 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
19:04:25.698 [INF] RunScan: 0
19:04:25.699 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:05:25.199 [INF] RunHandleWinLoss: 0
19:05:25.199 [INF] CalculateResult: 20250723
19:05:25.199 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
19:09:25.699 [INF] RunScan: 0
19:09:25.700 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:10:25.203 [INF] RunHandleWinLoss: 0
19:10:25.203 [INF] CalculateResult: 20250723
19:10:25.204 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
19:14:25.700 [INF] RunScan: 0
19:14:25.701 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:15:25.206 [INF] RunHandleWinLoss: 0
19:15:25.207 [INF] CalculateResult: 20250723
19:15:25.207 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
19:19:25.701 [INF] RunScan: 0
19:19:25.702 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:20:25.209 [INF] RunHandleWinLoss: 0
19:20:25.210 [INF] CalculateResult: 20250723
19:20:25.210 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
19:24:25.702 [INF] RunScan: 0
19:24:25.703 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:25:25.212 [INF] RunHandleWinLoss: 0
19:25:25.212 [INF] CalculateResult: 20250723
19:25:25.212 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
19:29:25.703 [INF] RunScan: 0
19:29:25.704 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:30:25.215 [INF] RunHandleWinLoss: 0
19:30:25.215 [INF] CalculateResult: 20250723
19:30:25.215 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
19:34:25.704 [INF] RunScan: 0
19:34:25.705 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:35:25.218 [INF] RunHandleWinLoss: 0
19:35:25.218 [INF] CalculateResult: 20250723
19:35:25.218 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
19:39:25.705 [INF] RunScan: 0
19:39:25.706 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:40:25.221 [INF] RunHandleWinLoss: 0
19:40:25.221 [INF] CalculateResult: 20250723
19:40:25.221 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
19:44:25.706 [INF] RunScan: 0
19:44:25.707 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:45:25.223 [INF] RunHandleWinLoss: 0
19:45:25.223 [INF] CalculateResult: 20250723
19:45:25.223 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
19:49:25.707 [INF] RunScan: 0
19:49:25.708 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:50:25.226 [INF] RunHandleWinLoss: 0
19:50:25.226 [INF] CalculateResult: 20250723
19:50:25.226 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
19:54:25.709 [INF] RunScan: 0
19:54:25.710 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
19:55:25.229 [INF] RunHandleWinLoss: 0
19:55:25.229 [INF] CalculateResult: 20250723
19:55:25.229 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
19:59:25.710 [INF] RunScan: 0
19:59:25.712 [INF] Result: {"Status":"Success","Message":"Kết quả ngày hôm nay đã được đăng","Data":null,"DataObj":null}
20:00:25.232 [INF] RunHandleWinLoss: 0
20:00:25.232 [INF] CalculateResult: 20250723
20:00:25.232 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
20:04:25.712 [INF] RunScan: 0
20:04:25.713 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:4)","Data":null,"DataObj":null}
20:05:25.236 [INF] RunHandleWinLoss: 0
20:05:25.236 [INF] CalculateResult: 20250723
20:05:25.236 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
20:09:25.713 [INF] RunScan: 0
20:09:25.714 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:9)","Data":null,"DataObj":null}
20:10:25.238 [INF] RunHandleWinLoss: 0
20:10:25.238 [INF] CalculateResult: 20250723
20:10:25.238 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
20:14:25.714 [INF] RunScan: 0
20:14:25.714 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:14)","Data":null,"DataObj":null}
20:15:25.240 [INF] RunHandleWinLoss: 0
20:15:25.241 [INF] CalculateResult: 20250723
20:15:25.241 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
20:19:25.715 [INF] RunScan: 0
20:19:25.715 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:19)","Data":null,"DataObj":null}
20:20:25.244 [INF] RunHandleWinLoss: 0
20:20:25.244 [INF] CalculateResult: 20250723
20:20:25.244 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
20:24:25.715 [INF] RunScan: 0
20:24:25.716 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:24)","Data":null,"DataObj":null}
20:25:25.247 [INF] RunHandleWinLoss: 0
20:25:25.247 [INF] CalculateResult: 20250723
20:25:25.247 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
20:29:25.716 [INF] RunScan: 0
20:29:25.717 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:29)","Data":null,"DataObj":null}
20:30:25.251 [INF] RunHandleWinLoss: 0
20:30:25.251 [INF] CalculateResult: 20250723
20:30:25.251 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
20:34:25.717 [INF] RunScan: 0
20:34:25.717 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:34)","Data":null,"DataObj":null}
20:35:25.253 [INF] RunHandleWinLoss: 0
20:35:25.254 [INF] CalculateResult: 20250723
20:35:25.254 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
20:39:25.718 [INF] RunScan: 0
20:39:25.718 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:39)","Data":null,"DataObj":null}
20:40:25.256 [INF] RunHandleWinLoss: 0
20:40:25.257 [INF] CalculateResult: 20250723
20:40:25.257 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
20:44:25.718 [INF] RunScan: 0
20:44:25.719 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:44)","Data":null,"DataObj":null}
20:45:25.259 [INF] RunHandleWinLoss: 0
20:45:25.259 [INF] CalculateResult: 20250723
20:45:25.259 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
20:49:25.720 [INF] RunScan: 0
20:49:25.720 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:49)","Data":null,"DataObj":null}
20:50:25.262 [INF] RunHandleWinLoss: 0
20:50:25.262 [INF] CalculateResult: 20250723
20:50:25.262 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
20:54:25.720 [INF] RunScan: 0
20:54:25.721 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:54)","Data":null,"DataObj":null}
20:55:25.265 [INF] RunHandleWinLoss: 0
20:55:25.266 [INF] CalculateResult: 20250723
20:55:25.266 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
20:59:25.721 [INF] RunScan: 0
20:59:25.722 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(20:59)","Data":null,"DataObj":null}
21:00:25.269 [INF] RunHandleWinLoss: 0
21:00:25.269 [INF] CalculateResult: 20250723
21:00:25.269 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
21:04:25.722 [INF] RunScan: 0
21:04:25.722 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:4)","Data":null,"DataObj":null}
21:05:25.273 [INF] RunHandleWinLoss: 0
21:05:25.273 [INF] CalculateResult: 20250723
21:05:25.273 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
21:07:09.409 [DBG] Client connected from 127.0.0.1:47904
no ex
21:07:09.409 [DBG] 784 bytes read
no ex
21:07:09.417 [DBG] Building Hybi-14 Response
no ex
21:07:09.422 [DBG] Sent 129 bytes
no ex
21:07:09.519 [DBG] 31 bytes read
no ex
21:07:09.678 [DBG] Sent 30 bytes
no ex
21:07:09.678 [DBG] 115 bytes read
no ex
21:07:09.679 [INF] Server full speed, frame 942503, total 149, task 0, engine 0, bot 0, network 149, push 0, avg fps 33.112583
21:07:09.698 [INF] GET: http://127.0.0.1:8081/api?c=3&un=volamminhchu&pw=7371d69e38344c8b89420a6c03fec3d1&pf=web&at=
21:07:09.768 [INF] GET response: {"success":true,"message":null,"errorCode":"0","data":null,"sessionKey":"eyJuaWNrbmFtZSI6Imh1bmdiYTEyMyIsImF2YXRhciI6IjAiLCJ2aW5Ub3RhbCI6MCwieHVUb3RhbCI6NTAwMDAwLCJ2aXBwb2ludCI6MCwidmlwcG9pbnRTYXZlIjowLCJjcmVhdGVUaW1lIjoiMjMtMDctMjAyNSIsImlwQWRkcmVzcyI6IjEyNy4wLjAuMSIsImNlcnRpZmljYXRlIjpmYWxzZSwibHVja3lSb3RhdGUiOjAsImRhaUx5IjowLCJtb2JpbGVTZWN1cmUiOjAsImJpcnRoZGF5IjoiIiwiYXBwU2VjdXJlIjowLCJ1c2VybmFtZSI6InZvbGFtbWluaGNodSIsImVtYWlsIjowLCJhZGRyZXNzIjpudWxsLCJ2ZXJpZnlNb2JpbGUiOnRydWV9","accessToken":"e7d1a70392b5d160b751183e21d23119"}

21:07:10.014 [DBG] Sent 5727 bytes
no ex
21:07:12.521 [DBG] 31 bytes read
no ex
21:07:12.522 [DBG] Sent 30 bytes
no ex
21:07:13.762 [DBG] Sent 184 bytes
no ex
21:07:15.542 [DBG] 31 bytes read
no ex
21:07:15.543 [DBG] Sent 30 bytes
no ex
21:07:18.549 [DBG] 31 bytes read
no ex
21:07:18.550 [DBG] Sent 30 bytes
no ex
21:07:18.759 [DBG] Sent 184 bytes
no ex
21:07:21.578 [DBG] 31 bytes read
no ex
21:07:21.579 [DBG] Sent 30 bytes
no ex
21:07:23.753 [DBG] Sent 184 bytes
no ex
21:07:24.575 [DBG] 31 bytes read
no ex
21:07:24.576 [DBG] Sent 30 bytes
no ex
21:07:27.579 [DBG] 31 bytes read
no ex
21:07:27.580 [DBG] Sent 30 bytes
no ex
21:07:28.753 [DBG] Sent 184 bytes
no ex
21:07:30.578 [DBG] 31 bytes read
no ex
21:07:30.579 [DBG] Sent 30 bytes
no ex
21:07:33.585 [DBG] 31 bytes read
no ex
21:07:33.586 [DBG] Sent 30 bytes
no ex
21:07:33.763 [DBG] Sent 184 bytes
no ex
21:07:36.594 [DBG] 31 bytes read
no ex
21:07:36.595 [DBG] Sent 30 bytes
no ex
21:07:38.763 [DBG] Sent 184 bytes
no ex
21:07:39.604 [DBG] 31 bytes read
no ex
21:07:39.605 [DBG] Sent 30 bytes
no ex
21:07:42.607 [DBG] 31 bytes read
no ex
21:07:42.608 [DBG] Sent 30 bytes
no ex
21:07:43.761 [DBG] Sent 184 bytes
no ex
21:07:45.609 [DBG] 31 bytes read
no ex
21:07:45.609 [DBG] Sent 30 bytes
no ex
21:07:48.621 [DBG] 31 bytes read
no ex
21:07:48.622 [DBG] Sent 30 bytes
no ex
21:07:48.755 [DBG] Sent 184 bytes
no ex
21:07:51.635 [DBG] 31 bytes read
no ex
21:07:51.635 [DBG] Sent 30 bytes
no ex
21:07:53.750 [DBG] Sent 184 bytes
no ex
21:07:54.643 [DBG] 31 bytes read
no ex
21:07:54.644 [DBG] Sent 30 bytes
no ex
21:07:57.655 [DBG] 31 bytes read
no ex
21:07:57.656 [DBG] Sent 30 bytes
no ex
21:07:58.765 [DBG] Sent 184 bytes
no ex
21:08:00.654 [DBG] 31 bytes read
no ex
21:08:00.655 [DBG] Sent 30 bytes
no ex
21:08:03.681 [DBG] 31 bytes read
no ex
21:08:03.681 [DBG] Sent 30 bytes
no ex
21:08:03.763 [DBG] Sent 184 bytes
no ex
21:08:06.678 [DBG] 31 bytes read
no ex
21:08:06.679 [DBG] Sent 30 bytes
no ex
21:08:08.759 [DBG] Sent 184 bytes
no ex
21:08:09.687 [DBG] 31 bytes read
no ex
21:08:09.688 [DBG] Sent 30 bytes
no ex
21:08:12.695 [DBG] 31 bytes read
no ex
21:08:12.696 [DBG] Sent 30 bytes
no ex
21:08:13.755 [DBG] Sent 184 bytes
no ex
21:08:15.704 [DBG] 31 bytes read
no ex
21:08:15.707 [DBG] Sent 30 bytes
no ex
21:08:18.713 [DBG] 31 bytes read
no ex
21:08:18.713 [DBG] Sent 30 bytes
no ex
21:08:18.763 [DBG] Sent 184 bytes
no ex
21:08:21.713 [DBG] 31 bytes read
no ex
21:08:21.714 [DBG] Sent 30 bytes
no ex
21:08:23.753 [DBG] Sent 184 bytes
no ex
21:08:24.806 [DBG] 31 bytes read
no ex
21:08:24.807 [DBG] Sent 30 bytes
no ex
21:08:27.761 [DBG] 31 bytes read
no ex
21:08:27.761 [DBG] Sent 30 bytes
no ex
21:08:28.760 [DBG] Sent 184 bytes
no ex
21:08:30.764 [DBG] 31 bytes read
no ex
21:08:30.764 [DBG] Sent 30 bytes
no ex
21:08:33.763 [DBG] Sent 72 bytes
no ex
21:08:33.763 [DBG] Sent 184 bytes
no ex
21:08:33.764 [DBG] 31 bytes read
no ex
21:08:33.765 [DBG] Sent 30 bytes
no ex
21:08:36.776 [DBG] 31 bytes read
no ex
21:08:36.777 [DBG] Sent 30 bytes
no ex
21:08:38.767 [DBG] Sent 184 bytes
no ex
21:08:39.770 [DBG] 31 bytes read
no ex
21:08:39.770 [DBG] Sent 30 bytes
no ex
21:08:42.774 [DBG] 31 bytes read
no ex
21:08:42.775 [DBG] Sent 30 bytes
no ex
21:08:43.760 [DBG] Sent 184 bytes
no ex
21:08:45.784 [DBG] 31 bytes read
no ex
21:08:45.785 [DBG] Sent 30 bytes
no ex
21:08:48.772 [DBG] Sent 184 bytes
no ex
21:08:48.799 [DBG] 31 bytes read
no ex
21:08:48.800 [DBG] Sent 30 bytes
no ex
21:08:51.806 [DBG] 31 bytes read
no ex
21:08:51.806 [DBG] Sent 30 bytes
no ex
21:08:53.770 [DBG] Sent 184 bytes
no ex
21:08:54.821 [DBG] 31 bytes read
no ex
21:08:54.821 [DBG] Sent 30 bytes
no ex
21:08:57.832 [DBG] 31 bytes read
no ex
21:08:57.832 [DBG] Sent 30 bytes
no ex
21:08:58.758 [DBG] Sent 184 bytes
no ex
21:09:00.844 [DBG] 31 bytes read
no ex
21:09:00.844 [DBG] Sent 30 bytes
no ex
21:09:03.773 [DBG] Sent 184 bytes
no ex
21:09:03.851 [DBG] 31 bytes read
no ex
21:09:03.851 [DBG] Sent 30 bytes
no ex
21:09:06.859 [DBG] 31 bytes read
no ex
21:09:06.860 [DBG] Sent 30 bytes
no ex
21:09:08.771 [DBG] Sent 184 bytes
no ex
21:09:09.871 [DBG] 31 bytes read
no ex
21:09:09.872 [DBG] Sent 30 bytes
no ex
21:09:12.880 [DBG] 31 bytes read
no ex
21:09:12.881 [DBG] Sent 30 bytes
no ex
21:09:13.770 [DBG] Sent 184 bytes
no ex
21:09:15.882 [DBG] 31 bytes read
no ex
21:09:15.882 [DBG] Sent 30 bytes
no ex
21:09:18.770 [DBG] Sent 184 bytes
no ex
21:09:18.883 [DBG] 31 bytes read
no ex
21:09:18.883 [DBG] Sent 30 bytes
no ex
21:09:21.884 [DBG] 31 bytes read
no ex
21:09:21.884 [DBG] Sent 30 bytes
no ex
21:09:23.763 [DBG] Sent 184 bytes
no ex
21:09:24.887 [DBG] 31 bytes read
no ex
21:09:24.888 [DBG] Sent 30 bytes
no ex
21:09:25.722 [INF] RunScan: 0
21:09:25.723 [INF] Result: {"Status":"Error","Message":"Kết quả được lấy từ 18h40 -> 20h(21:9)","Data":null,"DataObj":null}
21:09:27.888 [DBG] 31 bytes read
no ex
21:09:27.888 [DBG] Sent 30 bytes
no ex
21:09:28.768 [DBG] Sent 184 bytes
no ex
21:09:30.893 [DBG] 31 bytes read
no ex
21:09:30.894 [DBG] Sent 30 bytes
no ex
21:09:33.773 [DBG] Sent 184 bytes
no ex
21:09:33.898 [DBG] 31 bytes read
no ex
21:09:33.898 [DBG] Sent 30 bytes
no ex
21:09:36.901 [DBG] 31 bytes read
no ex
21:09:36.901 [DBG] Sent 30 bytes
no ex
21:09:38.778 [DBG] Sent 184 bytes
no ex
21:09:39.902 [DBG] 31 bytes read
no ex
21:09:39.902 [DBG] Sent 30 bytes
no ex
21:09:42.908 [DBG] 31 bytes read
no ex
21:09:42.908 [DBG] Sent 30 bytes
no ex
21:09:43.772 [DBG] Sent 184 bytes
no ex
21:09:45.914 [DBG] 31 bytes read
no ex
21:09:45.915 [DBG] Sent 30 bytes
no ex
21:09:48.774 [DBG] Sent 184 bytes
no ex
21:09:48.924 [DBG] 31 bytes read
no ex
21:09:48.924 [DBG] Sent 30 bytes
no ex
21:09:51.930 [DBG] 31 bytes read
no ex
21:09:51.931 [DBG] Sent 30 bytes
no ex
21:09:53.775 [DBG] Sent 184 bytes
no ex
21:09:54.940 [DBG] 31 bytes read
no ex
21:09:54.941 [DBG] Sent 30 bytes
no ex
21:09:57.946 [DBG] 31 bytes read
no ex
21:09:57.946 [DBG] Sent 30 bytes
no ex
21:09:58.771 [DBG] Sent 184 bytes
no ex
21:10:00.960 [DBG] 31 bytes read
no ex
21:10:00.960 [DBG] Sent 30 bytes
no ex
21:10:03.775 [DBG] Sent 184 bytes
no ex
21:10:03.968 [DBG] 31 bytes read
no ex
21:10:03.968 [DBG] Sent 30 bytes
no ex
21:10:06.966 [DBG] 31 bytes read
no ex
21:10:06.966 [DBG] Sent 30 bytes
no ex
21:10:08.774 [DBG] Sent 184 bytes
no ex
21:10:09.978 [DBG] 31 bytes read
no ex
21:10:09.978 [DBG] Sent 30 bytes
no ex
21:10:12.990 [DBG] 31 bytes read
no ex
21:10:12.991 [DBG] Sent 30 bytes
no ex
21:10:13.772 [DBG] Sent 184 bytes
no ex
21:10:16.000 [DBG] 31 bytes read
no ex
21:10:16.000 [DBG] Sent 30 bytes
no ex
21:10:18.782 [DBG] Sent 184 bytes
no ex
21:10:19.019 [DBG] 31 bytes read
no ex
21:10:19.020 [DBG] Sent 30 bytes
no ex
21:10:22.021 [DBG] 31 bytes read
no ex
21:10:22.022 [DBG] Sent 30 bytes
no ex
21:10:23.769 [DBG] Sent 184 bytes
no ex
21:10:25.021 [DBG] 31 bytes read
no ex
21:10:25.021 [DBG] Sent 30 bytes
no ex
21:10:25.275 [INF] RunHandleWinLoss: 0
21:10:25.275 [INF] CalculateResult: 20250723
21:10:25.275 [INF] CalculateResult 2: select * from loto_request where Session=20250723 AND NOT Status='1'
21:10:28.031 [DBG] 31 bytes read
no ex
21:10:28.031 [DBG] Sent 30 bytes
no ex
21:10:28.775 [DBG] Sent 184 bytes
no ex
21:10:31.043 [DBG] 31 bytes read
no ex
21:10:31.043 [DBG] Sent 30 bytes
no ex
21:10:33.781 [DBG] Sent 184 bytes
no ex
21:10:34.054 [DBG] 31 bytes read
no ex
21:10:34.054 [DBG] Sent 30 bytes
no ex
21:10:37.067 [DBG] 31 bytes read
no ex
21:10:37.067 [DBG] Sent 30 bytes
no ex
21:10:38.773 [DBG] Sent 184 bytes
no ex
21:10:40.064 [DBG] 31 bytes read
no ex
21:10:40.064 [DBG] Sent 30 bytes
no ex
21:10:43.083 [DBG] 31 bytes read
no ex
21:10:43.083 [DBG] Sent 30 bytes
no ex
21:10:43.784 [DBG] Sent 184 bytes
no ex
21:10:46.084 [DBG] 31 bytes read
no ex
21:10:46.085 [DBG] Sent 30 bytes
no ex
21:10:48.783 [DBG] Sent 184 bytes
no ex
21:10:49.086 [DBG] 31 bytes read
no ex
21:10:49.087 [DBG] Sent 30 bytes
no ex
21:10:52.095 [DBG] 31 bytes read
no ex
21:10:52.095 [DBG] Sent 30 bytes
no ex
21:10:53.785 [DBG] Sent 184 bytes
no ex
21:10:55.105 [DBG] 31 bytes read
no ex
21:10:55.105 [DBG] Sent 30 bytes
no ex
21:10:58.116 [DBG] 31 bytes read
no ex
21:10:58.116 [DBG] Sent 30 bytes
no ex
21:10:58.787 [DBG] Sent 184 bytes
no ex
