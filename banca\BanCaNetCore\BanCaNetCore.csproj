﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE;SERVER; NetCoreVersion; USE_lzLib</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>TRACE;SERVER; NetCoreVersion; USE_lzLib</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="..\BanCaLiteNet\BanCaApplication.cs" Link="BanCaApplication.cs" />
  </ItemGroup>

  <ItemGroup>
    
    <PackageReference Include="Nancy" Version="2.0.0" />
    <PackageReference Include="Nancy.Hosting.Self" Version="2.0.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.0.601" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core\Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="default.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="high.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="low.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="mid.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Properties" />
  </ItemGroup>

</Project>
