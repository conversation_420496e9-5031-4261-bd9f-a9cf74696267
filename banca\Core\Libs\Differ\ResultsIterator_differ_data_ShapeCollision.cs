// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace differ {
	public class ResultsIterator_differ_data_ShapeCollision : global::haxe.lang.HxObject {
		
		public ResultsIterator_differ_data_ShapeCollision(global::haxe.lang.EmptyObject empty) {
		}
		
		
		public ResultsIterator_differ_data_ShapeCollision(global::differ.Results_differ_data_ShapeCollision _results) {
			global::differ.ResultsIterator_differ_data_ShapeCollision.__hx_ctor_differ_ResultsIterator_differ_data_ShapeCollision(this, _results);
		}
		
		
		public static void __hx_ctor_differ_ResultsIterator_differ_data_ShapeCollision(global::differ.ResultsIterator_differ_data_ShapeCollision __hx_this, global::differ.Results_differ_data_ShapeCollision _results) {
			__hx_this.index = 0;
			__hx_this.index = 0;
			__hx_this.results = _results;
		}
		
		
		public int index;
		
		public global::differ.Results_differ_data_ShapeCollision results;
		
		public bool hasNext() {
			return ( this.index < this.results.count );
		}
		
		
		public global::differ.data.ShapeCollision next() {
			unchecked {
				global::differ.Results_differ_data_ShapeCollision _this = this.results;
				int index = this.index++;
				if (( ( index < 0 ) && ( index > ( _this.count - 1 ) ) )) {
					return null;
				}
				else {
					return ((global::differ.data.ShapeCollision) (_this.items[index]) );
				}
				
			}
		}
		
		
		public override double __hx_setField_f(string field, int hash, double @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1041537810:
					{
						this.index = ((int) (@value) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField_f(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_setField(string field, int hash, object @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1800886518:
					{
						this.results = ((global::differ.Results_differ_data_ShapeCollision) (@value) );
						return @value;
					}
					
					
					case 1041537810:
					{
						this.index = ((int) (global::haxe.lang.Runtime.toInt(@value)) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_getField(string field, int hash, bool throwErrors, bool isCheck, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1224901875:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "next", 1224901875)) );
					}
					
					
					case 407283053:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "hasNext", 407283053)) );
					}
					
					
					case 1800886518:
					{
						return this.results;
					}
					
					
					case 1041537810:
					{
						return this.index;
					}
					
					
					default:
					{
						return base.__hx_getField(field, hash, throwErrors, isCheck, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override double __hx_getField_f(string field, int hash, bool throwErrors, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1041537810:
					{
						return ((double) (this.index) );
					}
					
					
					default:
					{
						return base.__hx_getField_f(field, hash, throwErrors, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_invokeField(string field, int hash, global::ArrayHaxe dynargs) {
			unchecked {
				switch (hash) {
					case 1224901875:
					{
						return this.next();
					}
					
					
					case 407283053:
					{
						return this.hasNext();
					}
					
					
					default:
					{
						return base.__hx_invokeField(field, hash, dynargs);
					}
					
				}
				
			}
		}
		
		
		public override void __hx_getFields(global::ArrayHaxe<object> baseArr) {
			baseArr.push("results");
			baseArr.push("index");
			base.__hx_getFields(baseArr);
		}
		
		
	}
}


