// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace differ.data {
	public class RayCollision : global::haxe.lang.HxObject {
		
		public RayCollision(global::haxe.lang.EmptyObject empty) {
		}
		
		
		[global::System.ComponentModel.EditorBrowsable(global::System.ComponentModel.EditorBrowsableState.Never)]
		public RayCollision() {
			global::differ.data.RayCollision.__hx_ctor_differ_data_RayCollision(this);
		}
		
		
		public static void __hx_ctor_differ_data_RayCollision(global::differ.data.RayCollision __hx_this) {
			__hx_this.end = 0.0;
			__hx_this.start = 0.0;
		}
		
		
		public global::differ.shapes.Shape shape;
		
		public global::differ.shapes.Ray ray;
		
		public double start;
		
		public double end;
		
		public global::differ.data.RayCollision reset() {
			this.ray = null;
			this.shape = null;
			this.start = 0.0;
			this.end = 0.0;
			return this;
		}
		
		
		public void copy_from(global::differ.data.RayCollision other) {
			this.ray = other.ray;
			this.shape = other.shape;
			this.start = other.start;
			this.end = other.end;
		}
		
		
		public global::differ.data.RayCollision clone() {
			global::differ.data.RayCollision _clone = new global::differ.data.RayCollision();
			{
				_clone.ray = this.ray;
				_clone.shape = this.shape;
				_clone.start = this.start;
				_clone.end = this.end;
			}
			
			return _clone;
		}
		
		
		public override double __hx_setField_f(string field, int hash, double @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 5047259:
					{
						this.end = ((double) (@value) );
						return @value;
					}
					
					
					case 67859554:
					{
						this.start = ((double) (@value) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField_f(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_setField(string field, int hash, object @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 5047259:
					{
						this.end = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 67859554:
					{
						this.start = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 5690858:
					{
						this.ray = ((global::differ.shapes.Ray) (@value) );
						return @value;
					}
					
					
					case 2082267937:
					{
						this.shape = ((global::differ.shapes.Shape) (@value) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_getField(string field, int hash, bool throwErrors, bool isCheck, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1214452573:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "clone", 1214452573)) );
					}
					
					
					case 1772189044:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "copy_from", 1772189044)) );
					}
					
					
					case 1724402127:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "reset", 1724402127)) );
					}
					
					
					case 5047259:
					{
						return this.end;
					}
					
					
					case 67859554:
					{
						return this.start;
					}
					
					
					case 5690858:
					{
						return this.ray;
					}
					
					
					case 2082267937:
					{
						return this.shape;
					}
					
					
					default:
					{
						return base.__hx_getField(field, hash, throwErrors, isCheck, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override double __hx_getField_f(string field, int hash, bool throwErrors, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 5047259:
					{
						return this.end;
					}
					
					
					case 67859554:
					{
						return this.start;
					}
					
					
					default:
					{
						return base.__hx_getField_f(field, hash, throwErrors, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_invokeField(string field, int hash, global::ArrayHaxe dynargs) {
			unchecked {
				switch (hash) {
					case 1214452573:
					{
						return this.clone();
					}
					
					
					case 1772189044:
					{
						this.copy_from(((global::differ.data.RayCollision) (dynargs[0]) ));
						break;
					}
					
					
					case 1724402127:
					{
						return this.reset();
					}
					
					
					default:
					{
						return base.__hx_invokeField(field, hash, dynargs);
					}
					
				}
				
				return null;
			}
		}
		
		
		public override void __hx_getFields(global::ArrayHaxe<object> baseArr) {
			baseArr.push("end");
			baseArr.push("start");
			baseArr.push("ray");
			baseArr.push("shape");
			base.__hx_getFields(baseArr);
		}
		
		
	}
}



#pragma warning disable 109, 114, 219, 429, 168, 162
namespace differ.data {
	public class RayCollisionHelper : global::haxe.lang.HxObject {
		
		public RayCollisionHelper(global::haxe.lang.EmptyObject empty) {
		}
		
		
		public RayCollisionHelper() {
			global::differ.data.RayCollisionHelper.__hx_ctor_differ_data_RayCollisionHelper(this);
		}
		
		
		public static void __hx_ctor_differ_data_RayCollisionHelper(global::differ.data.RayCollisionHelper __hx_this) {
		}
		
		
		public static double hitStartX(global::differ.data.RayCollision data) {
			return ( data.ray.start.x + ( data.ray.get_dir().x * data.start ) );
		}
		
		
		public static double hitStartY(global::differ.data.RayCollision data) {
			return ( data.ray.start.y + ( data.ray.get_dir().y * data.start ) );
		}
		
		
		public static double hitEndX(global::differ.data.RayCollision data) {
			return ( data.ray.start.x + ( data.ray.get_dir().x * data.end ) );
		}
		
		
		public static double hitEndY(global::differ.data.RayCollision data) {
			return ( data.ray.start.y + ( data.ray.get_dir().y * data.end ) );
		}
		
		
	}
}


