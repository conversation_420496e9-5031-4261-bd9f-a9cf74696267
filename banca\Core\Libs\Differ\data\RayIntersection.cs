// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace differ.data {
	public class RayIntersection : global::haxe.lang.HxObject {
		
		public RayIntersection(global::haxe.lang.EmptyObject empty) {
		}
		
		
		[global::System.ComponentModel.EditorBrowsable(global::System.ComponentModel.EditorBrowsableState.Never)]
		public RayIntersection() {
			global::differ.data.RayIntersection.__hx_ctor_differ_data_RayIntersection(this);
		}
		
		
		public static void __hx_ctor_differ_data_RayIntersection(global::differ.data.RayIntersection __hx_this) {
			__hx_this.u2 = 0.0;
			__hx_this.u1 = 0.0;
		}
		
		
		public global::differ.shapes.Ray ray1;
		
		public global::differ.shapes.Ray ray2;
		
		public double u1;
		
		public double u2;
		
		public global::differ.data.RayIntersection reset() {
			this.ray1 = null;
			this.ray2 = null;
			this.u1 = 0.0;
			this.u2 = 0.0;
			return this;
		}
		
		
		public void copy_from(global::differ.data.RayIntersection other) {
			this.ray1 = other.ray1;
			this.ray2 = other.ray2;
			this.u1 = other.u1;
			this.u2 = other.u2;
		}
		
		
		public global::differ.data.RayIntersection clone() {
			global::differ.data.RayIntersection _clone = new global::differ.data.RayIntersection();
			{
				_clone.ray1 = this.ray1;
				_clone.ray2 = this.ray2;
				_clone.u1 = this.u1;
				_clone.u2 = this.u2;
			}
			
			return _clone;
		}
		
		
		public override double __hx_setField_f(string field, int hash, double @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 26141:
					{
						this.u2 = ((double) (@value) );
						return @value;
					}
					
					
					case 26140:
					{
						this.u1 = ((double) (@value) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField_f(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_setField(string field, int hash, object @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 26141:
					{
						this.u2 = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 26140:
					{
						this.u1 = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 1269061384:
					{
						this.ray2 = ((global::differ.shapes.Ray) (@value) );
						return @value;
					}
					
					
					case 1269061383:
					{
						this.ray1 = ((global::differ.shapes.Ray) (@value) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_getField(string field, int hash, bool throwErrors, bool isCheck, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1214452573:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "clone", 1214452573)) );
					}
					
					
					case 1772189044:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "copy_from", 1772189044)) );
					}
					
					
					case 1724402127:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "reset", 1724402127)) );
					}
					
					
					case 26141:
					{
						return this.u2;
					}
					
					
					case 26140:
					{
						return this.u1;
					}
					
					
					case 1269061384:
					{
						return this.ray2;
					}
					
					
					case 1269061383:
					{
						return this.ray1;
					}
					
					
					default:
					{
						return base.__hx_getField(field, hash, throwErrors, isCheck, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override double __hx_getField_f(string field, int hash, bool throwErrors, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 26141:
					{
						return this.u2;
					}
					
					
					case 26140:
					{
						return this.u1;
					}
					
					
					default:
					{
						return base.__hx_getField_f(field, hash, throwErrors, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_invokeField(string field, int hash, global::ArrayHaxe dynargs) {
			unchecked {
				switch (hash) {
					case 1214452573:
					{
						return this.clone();
					}
					
					
					case 1772189044:
					{
						this.copy_from(((global::differ.data.RayIntersection) (dynargs[0]) ));
						break;
					}
					
					
					case 1724402127:
					{
						return this.reset();
					}
					
					
					default:
					{
						return base.__hx_invokeField(field, hash, dynargs);
					}
					
				}
				
				return null;
			}
		}
		
		
		public override void __hx_getFields(global::ArrayHaxe<object> baseArr) {
			baseArr.push("u2");
			baseArr.push("u1");
			baseArr.push("ray2");
			baseArr.push("ray1");
			base.__hx_getFields(baseArr);
		}
		
		
	}
}


