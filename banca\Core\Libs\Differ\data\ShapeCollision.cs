// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace differ.data {
	public class ShapeCollision : global::haxe.lang.HxObject {
		
		public ShapeCollision(global::haxe.lang.EmptyObject empty) {
		}
		
		
		[global::System.ComponentModel.EditorBrowsable(global::System.ComponentModel.EditorBrowsableState.Never)]
		public ShapeCollision() {
			global::differ.data.ShapeCollision.__hx_ctor_differ_data_ShapeCollision(this);
		}
		
		
		public static void __hx_ctor_differ_data_ShapeCollision(global::differ.data.ShapeCollision __hx_this) {
			__hx_this.otherUnitVectorY = 0.0;
			__hx_this.otherUnitVectorX = 0.0;
			__hx_this.otherSeparationY = 0.0;
			__hx_this.otherSeparationX = 0.0;
			__hx_this.otherOverlap = 0.0;
			__hx_this.unitVectorY = 0.0;
			__hx_this.unitVectorX = 0.0;
			__hx_this.separationY = 0.0;
			__hx_this.separationX = 0.0;
			__hx_this.overlap = 0.0;
		}
		
		
		public double overlap;
		
		public double separationX;
		
		public double separationY;
		
		public double unitVectorX;
		
		public double unitVectorY;
		
		public double otherOverlap;
		
		public double otherSeparationX;
		
		public double otherSeparationY;
		
		public double otherUnitVectorX;
		
		public double otherUnitVectorY;
		
		public global::differ.shapes.Shape shape1;
		
		public global::differ.shapes.Shape shape2;
		
		public global::differ.data.ShapeCollision reset() {
			this.shape1 = this.shape2 = null;
			this.overlap = this.separationX = this.separationY = this.unitVectorX = this.unitVectorY = 0.0;
			this.otherOverlap = this.otherSeparationX = this.otherSeparationY = this.otherUnitVectorX = this.otherUnitVectorY = 0.0;
			return this;
		}
		
		
		public global::differ.data.ShapeCollision clone() {
			global::differ.data.ShapeCollision _clone = new global::differ.data.ShapeCollision();
			{
				_clone.overlap = this.overlap;
				_clone.separationX = this.separationX;
				_clone.separationY = this.separationY;
				_clone.unitVectorX = this.unitVectorX;
				_clone.unitVectorY = this.unitVectorY;
				_clone.otherOverlap = this.otherOverlap;
				_clone.otherSeparationX = this.otherSeparationX;
				_clone.otherSeparationY = this.otherSeparationY;
				_clone.otherUnitVectorX = this.otherUnitVectorX;
				_clone.otherUnitVectorY = this.otherUnitVectorY;
				_clone.shape1 = this.shape1;
				_clone.shape2 = this.shape2;
			}
			
			return _clone;
		}
		
		
		public void copy_from(global::differ.data.ShapeCollision _other) {
			this.overlap = _other.overlap;
			this.separationX = _other.separationX;
			this.separationY = _other.separationY;
			this.unitVectorX = _other.unitVectorX;
			this.unitVectorY = _other.unitVectorY;
			this.otherOverlap = _other.otherOverlap;
			this.otherSeparationX = _other.otherSeparationX;
			this.otherSeparationY = _other.otherSeparationY;
			this.otherUnitVectorX = _other.otherUnitVectorX;
			this.otherUnitVectorY = _other.otherUnitVectorY;
			this.shape1 = _other.shape1;
			this.shape2 = _other.shape2;
		}
		
		
		public override double __hx_setField_f(string field, int hash, double @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1143436066:
					{
						this.otherUnitVectorY = ((double) (@value) );
						return @value;
					}
					
					
					case 1143436065:
					{
						this.otherUnitVectorX = ((double) (@value) );
						return @value;
					}
					
					
					case 991811683:
					{
						this.otherSeparationY = ((double) (@value) );
						return @value;
					}
					
					
					case 991811682:
					{
						this.otherSeparationX = ((double) (@value) );
						return @value;
					}
					
					
					case 757104279:
					{
						this.otherOverlap = ((double) (@value) );
						return @value;
					}
					
					
					case 494170130:
					{
						this.unitVectorY = ((double) (@value) );
						return @value;
					}
					
					
					case 494170129:
					{
						this.unitVectorX = ((double) (@value) );
						return @value;
					}
					
					
					case 342545747:
					{
						this.separationY = ((double) (@value) );
						return @value;
					}
					
					
					case 342545746:
					{
						this.separationX = ((double) (@value) );
						return @value;
					}
					
					
					case 688931719:
					{
						this.overlap = ((double) (@value) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField_f(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_setField(string field, int hash, object @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 489282033:
					{
						this.shape2 = ((global::differ.shapes.Shape) (@value) );
						return @value;
					}
					
					
					case 489282032:
					{
						this.shape1 = ((global::differ.shapes.Shape) (@value) );
						return @value;
					}
					
					
					case 1143436066:
					{
						this.otherUnitVectorY = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 1143436065:
					{
						this.otherUnitVectorX = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 991811683:
					{
						this.otherSeparationY = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 991811682:
					{
						this.otherSeparationX = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 757104279:
					{
						this.otherOverlap = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 494170130:
					{
						this.unitVectorY = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 494170129:
					{
						this.unitVectorX = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 342545747:
					{
						this.separationY = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 342545746:
					{
						this.separationX = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 688931719:
					{
						this.overlap = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_getField(string field, int hash, bool throwErrors, bool isCheck, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1772189044:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "copy_from", 1772189044)) );
					}
					
					
					case 1214452573:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "clone", 1214452573)) );
					}
					
					
					case 1724402127:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "reset", 1724402127)) );
					}
					
					
					case 489282033:
					{
						return this.shape2;
					}
					
					
					case 489282032:
					{
						return this.shape1;
					}
					
					
					case 1143436066:
					{
						return this.otherUnitVectorY;
					}
					
					
					case 1143436065:
					{
						return this.otherUnitVectorX;
					}
					
					
					case 991811683:
					{
						return this.otherSeparationY;
					}
					
					
					case 991811682:
					{
						return this.otherSeparationX;
					}
					
					
					case 757104279:
					{
						return this.otherOverlap;
					}
					
					
					case 494170130:
					{
						return this.unitVectorY;
					}
					
					
					case 494170129:
					{
						return this.unitVectorX;
					}
					
					
					case 342545747:
					{
						return this.separationY;
					}
					
					
					case 342545746:
					{
						return this.separationX;
					}
					
					
					case 688931719:
					{
						return this.overlap;
					}
					
					
					default:
					{
						return base.__hx_getField(field, hash, throwErrors, isCheck, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override double __hx_getField_f(string field, int hash, bool throwErrors, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1143436066:
					{
						return this.otherUnitVectorY;
					}
					
					
					case 1143436065:
					{
						return this.otherUnitVectorX;
					}
					
					
					case 991811683:
					{
						return this.otherSeparationY;
					}
					
					
					case 991811682:
					{
						return this.otherSeparationX;
					}
					
					
					case 757104279:
					{
						return this.otherOverlap;
					}
					
					
					case 494170130:
					{
						return this.unitVectorY;
					}
					
					
					case 494170129:
					{
						return this.unitVectorX;
					}
					
					
					case 342545747:
					{
						return this.separationY;
					}
					
					
					case 342545746:
					{
						return this.separationX;
					}
					
					
					case 688931719:
					{
						return this.overlap;
					}
					
					
					default:
					{
						return base.__hx_getField_f(field, hash, throwErrors, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_invokeField(string field, int hash, global::ArrayHaxe dynargs) {
			unchecked {
				switch (hash) {
					case 1772189044:
					{
						this.copy_from(((global::differ.data.ShapeCollision) (dynargs[0]) ));
						break;
					}
					
					
					case 1214452573:
					{
						return this.clone();
					}
					
					
					case 1724402127:
					{
						return this.reset();
					}
					
					
					default:
					{
						return base.__hx_invokeField(field, hash, dynargs);
					}
					
				}
				
				return null;
			}
		}
		
		
		public override void __hx_getFields(global::ArrayHaxe<object> baseArr) {
			baseArr.push("shape2");
			baseArr.push("shape1");
			baseArr.push("otherUnitVectorY");
			baseArr.push("otherUnitVectorX");
			baseArr.push("otherSeparationY");
			baseArr.push("otherSeparationX");
			baseArr.push("otherOverlap");
			baseArr.push("unitVectorY");
			baseArr.push("unitVectorX");
			baseArr.push("separationY");
			baseArr.push("separationX");
			baseArr.push("overlap");
			base.__hx_getFields(baseArr);
		}
		
		
	}
}


