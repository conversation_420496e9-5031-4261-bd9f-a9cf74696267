// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace differ.math {
	public class Matrix : global::haxe.lang.HxObject {
		
		public Matrix(global::haxe.lang.EmptyObject empty) {
		}
		
		
		public Matrix(global::haxe.lang.Null<double> a, global::haxe.lang.Null<double> b, global::haxe.lang.Null<double> c, global::haxe.lang.Null<double> d, global::haxe.lang.Null<double> tx, global::haxe.lang.Null<double> ty) {
			global::differ.math.Matrix.__hx_ctor_differ_math_Matrix(this, a, b, c, d, tx, ty);
		}
		
		
		public static void __hx_ctor_differ_math_Matrix(global::differ.math.Matrix __hx_this, global::haxe.lang.Null<double> a, global::haxe.lang.Null<double> b, global::haxe.lang.Null<double> c, global::haxe.lang.Null<double> d, global::haxe.lang.Null<double> tx, global::haxe.lang.Null<double> ty) {
			unchecked {
				__hx_this._last_rotation = ((double) (((int) (0) )) );
				double __temp_ty13 = ( ( ! (ty.hasValue) ) ? (((double) (0) )) : ((ty).@value) );
				double __temp_tx12 = ( ( ! (tx.hasValue) ) ? (((double) (0) )) : ((tx).@value) );
				double __temp_d11 = ( ( ! (d.hasValue) ) ? (((double) (1) )) : ((d).@value) );
				double __temp_c10 = ( ( ! (c.hasValue) ) ? (((double) (0) )) : ((c).@value) );
				double __temp_b9 = ( ( ! (b.hasValue) ) ? (((double) (0) )) : ((b).@value) );
				double __temp_a8 = ( ( ! (a.hasValue) ) ? (((double) (1) )) : ((a).@value) );
				__hx_this.a = __temp_a8;
				__hx_this.b = __temp_b9;
				__hx_this.c = __temp_c10;
				__hx_this.d = __temp_d11;
				__hx_this.tx = __temp_tx12;
				__hx_this.ty = __temp_ty13;
			}
		}
		
		
		public double a;
		
		public double b;
		
		public double c;
		
		public double d;
		
		public double tx;
		
		public double ty;
		
		public double _last_rotation;
		
		public virtual void identity() {
			unchecked {
				this.a = ((double) (1) );
				this.b = ((double) (0) );
				this.c = ((double) (0) );
				this.d = ((double) (1) );
				this.tx = ((double) (0) );
				this.ty = ((double) (0) );
			}
		}
		
		
		public virtual void translate(double x, double y) {
			this.tx += x;
			this.ty += y;
		}
		
		
		public virtual void compose(global::differ.math.Vector _position, double _rotation, global::differ.math.Vector _scale) {
			this.identity();
			this.scale(_scale.x, _scale.y);
			this.rotate(_rotation);
			this.makeTranslation(_position.x, _position.y);
		}
		
		
		public virtual global::differ.math.Matrix makeTranslation(double _x, double _y) {
			this.tx = _x;
			this.ty = _y;
			return this;
		}
		
		
		public virtual void rotate(double angle) {
			double cos = global::System.Math.Cos(((double) (angle) ));
			double sin = global::System.Math.Sin(((double) (angle) ));
			double a1 = ( ( this.a * cos ) - ( this.b * sin ) );
			this.b = ( ( this.a * sin ) + ( this.b * cos ) );
			this.a = a1;
			double c1 = ( ( this.c * cos ) - ( this.d * sin ) );
			this.d = ( ( this.c * sin ) + ( this.d * cos ) );
			this.c = c1;
			double tx1 = ( ( this.tx * cos ) - ( this.ty * sin ) );
			this.ty = ( ( this.tx * sin ) + ( this.ty * cos ) );
			this.tx = tx1;
		}
		
		
		public virtual void scale(double x, double y) {
			this.a *= x;
			this.b *= y;
			this.c *= x;
			this.d *= y;
			this.tx *= x;
			this.ty *= y;
		}
		
		
		public virtual string toString() {
			return global::haxe.lang.Runtime.concat(global::haxe.lang.Runtime.concat(global::haxe.lang.Runtime.concat(global::haxe.lang.Runtime.concat(global::haxe.lang.Runtime.concat(global::haxe.lang.Runtime.concat(global::haxe.lang.Runtime.concat(global::haxe.lang.Runtime.concat(global::haxe.lang.Runtime.concat(global::haxe.lang.Runtime.concat(global::haxe.lang.Runtime.concat(global::haxe.lang.Runtime.concat("(a=", global::haxe.lang.Runtime.toString(this.a)), ", b="), global::haxe.lang.Runtime.toString(this.b)), ", c="), global::haxe.lang.Runtime.toString(this.c)), ", d="), global::haxe.lang.Runtime.toString(this.d)), ", tx="), global::haxe.lang.Runtime.toString(this.tx)), ", ty="), global::haxe.lang.Runtime.toString(this.ty)), ")");
		}
		
		
		public override double __hx_setField_f(string field, int hash, double @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1792286664:
					{
						this._last_rotation = ((double) (@value) );
						return @value;
					}
					
					
					case 25989:
					{
						this.ty = ((double) (@value) );
						return @value;
					}
					
					
					case 25988:
					{
						this.tx = ((double) (@value) );
						return @value;
					}
					
					
					case 100:
					{
						this.d = ((double) (@value) );
						return @value;
					}
					
					
					case 99:
					{
						this.c = ((double) (@value) );
						return @value;
					}
					
					
					case 98:
					{
						this.b = ((double) (@value) );
						return @value;
					}
					
					
					case 97:
					{
						this.a = ((double) (@value) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField_f(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_setField(string field, int hash, object @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1792286664:
					{
						this._last_rotation = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 25989:
					{
						this.ty = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 25988:
					{
						this.tx = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 100:
					{
						this.d = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 99:
					{
						this.c = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 98:
					{
						this.b = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 97:
					{
						this.a = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_getField(string field, int hash, bool throwErrors, bool isCheck, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 946786476:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "toString", 946786476)) );
					}
					
					
					case 2026819210:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "scale", 2026819210)) );
					}
					
					
					case 1260406363:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "rotate", 1260406363)) );
					}
					
					
					case 966728099:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "makeTranslation", 966728099)) );
					}
					
					
					case 57915666:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "compose", 57915666)) );
					}
					
					
					case 1233114958:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "translate", 1233114958)) );
					}
					
					
					case 959399230:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "identity", 959399230)) );
					}
					
					
					case 1792286664:
					{
						return this._last_rotation;
					}
					
					
					case 25989:
					{
						return this.ty;
					}
					
					
					case 25988:
					{
						return this.tx;
					}
					
					
					case 100:
					{
						return this.d;
					}
					
					
					case 99:
					{
						return this.c;
					}
					
					
					case 98:
					{
						return this.b;
					}
					
					
					case 97:
					{
						return this.a;
					}
					
					
					default:
					{
						return base.__hx_getField(field, hash, throwErrors, isCheck, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override double __hx_getField_f(string field, int hash, bool throwErrors, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1792286664:
					{
						return this._last_rotation;
					}
					
					
					case 25989:
					{
						return this.ty;
					}
					
					
					case 25988:
					{
						return this.tx;
					}
					
					
					case 100:
					{
						return this.d;
					}
					
					
					case 99:
					{
						return this.c;
					}
					
					
					case 98:
					{
						return this.b;
					}
					
					
					case 97:
					{
						return this.a;
					}
					
					
					default:
					{
						return base.__hx_getField_f(field, hash, throwErrors, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_invokeField(string field, int hash, global::ArrayHaxe dynargs) {
			unchecked {
				switch (hash) {
					case 946786476:
					{
						return this.toString();
					}
					
					
					case 2026819210:
					{
						this.scale(((double) (global::haxe.lang.Runtime.toDouble(dynargs[0])) ), ((double) (global::haxe.lang.Runtime.toDouble(dynargs[1])) ));
						break;
					}
					
					
					case 1260406363:
					{
						this.rotate(((double) (global::haxe.lang.Runtime.toDouble(dynargs[0])) ));
						break;
					}
					
					
					case 966728099:
					{
						return this.makeTranslation(((double) (global::haxe.lang.Runtime.toDouble(dynargs[0])) ), ((double) (global::haxe.lang.Runtime.toDouble(dynargs[1])) ));
					}
					
					
					case 57915666:
					{
						this.compose(((global::differ.math.Vector) (dynargs[0]) ), ((double) (global::haxe.lang.Runtime.toDouble(dynargs[1])) ), ((global::differ.math.Vector) (dynargs[2]) ));
						break;
					}
					
					
					case 1233114958:
					{
						this.translate(((double) (global::haxe.lang.Runtime.toDouble(dynargs[0])) ), ((double) (global::haxe.lang.Runtime.toDouble(dynargs[1])) ));
						break;
					}
					
					
					case 959399230:
					{
						this.identity();
						break;
					}
					
					
					default:
					{
						return base.__hx_invokeField(field, hash, dynargs);
					}
					
				}
				
				return null;
			}
		}
		
		
		public override void __hx_getFields(global::ArrayHaxe<object> baseArr) {
			baseArr.push("_last_rotation");
			baseArr.push("ty");
			baseArr.push("tx");
			baseArr.push("d");
			baseArr.push("c");
			baseArr.push("b");
			baseArr.push("a");
			base.__hx_getFields(baseArr);
		}
		
		
		public override string ToString(){
			return this.toString();
		}
		
		
	}
}


