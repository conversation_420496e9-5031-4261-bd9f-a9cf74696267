// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace differ.sat {
	public class SAT2D : global::haxe.lang.HxObject {
		
		static SAT2D() {
			global::differ.sat.SAT2D.tmp1 = new global::differ.data.ShapeCollision();
			global::differ.sat.SAT2D.tmp2 = new global::differ.data.ShapeCollision();
		}
		
		
		public SAT2D(global::haxe.lang.EmptyObject empty) {
		}
		
		
		public SAT2D() {
			global::differ.sat.SAT2D.__hx_ctor_differ_sat_SAT2D(this);
		}
		
		
		public static void __hx_ctor_differ_sat_SAT2D(global::differ.sat.SAT2D __hx_this) {
		}
		
		
		public static global::differ.data.ShapeCollision testCircleVsPolygon(global::differ.shapes.Circle circle, global::differ.shapes.Polygon polygon, global::differ.data.ShapeCollision @into, global::haxe.lang.Null<bool> flip) {
			unchecked {
				bool __temp_flip16 = ( ( ! (flip.hasValue) ) ? (false) : ((flip).@value) );
				if (( @into == null )) {
					@into = new global::differ.data.ShapeCollision();
				}
				else {
					@into.shape1 = @into.shape2 = null;
					@into.overlap = @into.separationX = @into.separationY = @into.unitVectorX = @into.unitVectorY = 0.0;
					@into.otherOverlap = @into.otherSeparationX = @into.otherSeparationY = @into.otherUnitVectorX = @into.otherUnitVectorY = 0.0;
				}
				
				global::ArrayHaxe<object> verts = polygon.get_transformedVertices();
				double circleX = circle.get_x();
				double circleY = circle.get_y();
				double testDistance = ((double) (1073741823) );
				double distance = 0.0;
				double closestX = 0.0;
				double closestY = 0.0;
				{
					int _g1 = 0;
					int _g = verts.length;
					while (( _g1 < _g )) {
						int i = _g1++;
						double x = ( circleX - ((global::differ.math.Vector) (verts[i]) ).x );
						double y = ( circleY - ((global::differ.math.Vector) (verts[i]) ).y );
						distance = ( ( x * x ) + ( y * y ) );
						if (( distance < testDistance )) {
							testDistance = distance;
							closestX = ((global::differ.math.Vector) (verts[i]) ).x;
							closestY = ((global::differ.math.Vector) (verts[i]) ).y;
						}
						
					}
					
				}
				
				double normalAxisX = ( closestX - circleX );
				double normalAxisY = ( closestY - circleY );
				double normAxisLen = global::System.Math.Sqrt(((double) (( ( normalAxisX * normalAxisX ) + ( normalAxisY * normalAxisY ) )) ));
				double component = normalAxisX;
				if (( normAxisLen == 0 )) {
					normalAxisX = ((double) (0) );
				}
				else {
					component /= normAxisLen;
					normalAxisX = component;
				}
				
				double component1 = normalAxisY;
				if (( normAxisLen == 0 )) {
					normalAxisY = ((double) (0) );
				}
				else {
					component1 /= normAxisLen;
					normalAxisY = component1;
				}
				
				double test = 0.0;
				double min1 = ( ( normalAxisX * ((global::differ.math.Vector) (verts[0]) ).x ) + ( normalAxisY * ((global::differ.math.Vector) (verts[0]) ).y ) );
				double max1 = min1;
				{
					int _g11 = 1;
					int _g2 = verts.length;
					while (( _g11 < _g2 )) {
						int j = _g11++;
						test = ( ( normalAxisX * ((global::differ.math.Vector) (verts[j]) ).x ) + ( normalAxisY * ((global::differ.math.Vector) (verts[j]) ).y ) );
						if (( test < min1 )) {
							min1 = test;
						}
						
						if (( test > max1 )) {
							max1 = test;
						}
						
					}
					
				}
				
				double max2 = circle.get_transformedRadius();
				double min2 =  - (circle.get_transformedRadius()) ;
				double offset = ( ( normalAxisX *  - (circleX)  ) + ( normalAxisY *  - (circleY)  ) );
				min1 += offset;
				max1 += offset;
				double test1 = ( min1 - max2 );
				double test2 = ( min2 - max1 );
				if (( ( test1 > 0 ) || ( test2 > 0 ) )) {
					return null;
				}
				
				double distMin =  - ((( max2 - min1 ))) ;
				if (__temp_flip16) {
					distMin *= ((double) (-1) );
				}
				
				@into.overlap = distMin;
				@into.unitVectorX = normalAxisX;
				@into.unitVectorY = normalAxisY;
				double closest = global::System.Math.Abs(((double) (distMin) ));
				{
					int _g12 = 0;
					int _g3 = verts.length;
					while (( _g12 < _g3 )) {
						int i1 = _g12++;
						global::differ.math.Vector v2 = ( (( i1 >= ( verts.length - 1 ) )) ? (((global::differ.math.Vector) (verts[0]) )) : (((global::differ.math.Vector) (verts[( i1 + 1 )]) )) );
						normalAxisX =  - ((( v2.y - ((global::differ.math.Vector) (verts[i1]) ).y ))) ;
						global::differ.math.Vector v21 = ( (( i1 >= ( verts.length - 1 ) )) ? (((global::differ.math.Vector) (verts[0]) )) : (((global::differ.math.Vector) (verts[( i1 + 1 )]) )) );
						normalAxisY = ( v21.x - ((global::differ.math.Vector) (verts[i1]) ).x );
						double aLen = global::System.Math.Sqrt(((double) (( ( normalAxisX * normalAxisX ) + ( normalAxisY * normalAxisY ) )) ));
						double component2 = normalAxisX;
						if (( aLen == 0 )) {
							normalAxisX = ((double) (0) );
						}
						else {
							component2 /= aLen;
							normalAxisX = component2;
						}
						
						double component3 = normalAxisY;
						if (( aLen == 0 )) {
							normalAxisY = ((double) (0) );
						}
						else {
							component3 /= aLen;
							normalAxisY = component3;
						}
						
						min1 = ( ( normalAxisX * ((global::differ.math.Vector) (verts[0]) ).x ) + ( normalAxisY * ((global::differ.math.Vector) (verts[0]) ).y ) );
						max1 = min1;
						{
							int _g31 = 1;
							int _g21 = verts.length;
							while (( _g31 < _g21 )) {
								int j1 = _g31++;
								test = ( ( normalAxisX * ((global::differ.math.Vector) (verts[j1]) ).x ) + ( normalAxisY * ((global::differ.math.Vector) (verts[j1]) ).y ) );
								if (( test < min1 )) {
									min1 = test;
								}
								
								if (( test > max1 )) {
									max1 = test;
								}
								
							}
							
						}
						
						max2 = circle.get_transformedRadius();
						min2 =  - (circle.get_transformedRadius()) ;
						offset = ( ( normalAxisX *  - (circleX)  ) + ( normalAxisY *  - (circleY)  ) );
						min1 += offset;
						max1 += offset;
						test1 = ( min1 - max2 );
						test2 = ( min2 - max1 );
						if (( ( test1 > 0 ) || ( test2 > 0 ) )) {
							return null;
						}
						
						distMin =  - ((( max2 - min1 ))) ;
						if (__temp_flip16) {
							distMin *= ((double) (-1) );
						}
						
						if (( global::System.Math.Abs(((double) (distMin) )) < closest )) {
							@into.unitVectorX = normalAxisX;
							@into.unitVectorY = normalAxisY;
							@into.overlap = distMin;
							closest = global::System.Math.Abs(((double) (distMin) ));
						}
						
					}
					
				}
				
				@into.shape1 = ( (__temp_flip16) ? ((global::differ.shapes.Shape) (polygon) ) : ((global::differ.shapes.Shape) (circle) ) );
				@into.shape2 = ( (__temp_flip16) ? ((global::differ.shapes.Shape) (circle) ) : ((global::differ.shapes.Shape) (polygon) ) );
				@into.separationX = ( @into.unitVectorX * @into.overlap );
				@into.separationY = ( @into.unitVectorY * @into.overlap );
				if ( ! (__temp_flip16) ) {
					@into.unitVectorX =  - (@into.unitVectorX) ;
					@into.unitVectorY =  - (@into.unitVectorY) ;
				}
				
				return @into;
			}
		}
		
		
		public static global::differ.data.ShapeCollision testCircleVsCircle(global::differ.shapes.Circle circleA, global::differ.shapes.Circle circleB, global::differ.data.ShapeCollision @into, global::haxe.lang.Null<bool> flip) {
			bool __temp_flip17 = ( ( ! (flip.hasValue) ) ? (false) : ((flip).@value) );
			global::differ.shapes.Circle circle1 = ( (__temp_flip17) ? (circleB) : (circleA) );
			global::differ.shapes.Circle circle2 = ( (__temp_flip17) ? (circleA) : (circleB) );
			double totalRadius = ( circle1.get_transformedRadius() + circle2.get_transformedRadius() );
			double x = ( circle1.get_x() - circle2.get_x() );
			double y = ( circle1.get_y() - circle2.get_y() );
			double distancesq = ( ( x * x ) + ( y * y ) );
			if (( distancesq < ( totalRadius * totalRadius ) )) {
				if (( @into == null )) {
					@into = new global::differ.data.ShapeCollision();
				}
				else {
					@into.shape1 = @into.shape2 = null;
					@into.overlap = @into.separationX = @into.separationY = @into.unitVectorX = @into.unitVectorY = 0.0;
					@into.otherOverlap = @into.otherSeparationX = @into.otherSeparationY = @into.otherUnitVectorX = @into.otherUnitVectorY = 0.0;
				}
				
				double difference = ( totalRadius - global::System.Math.Sqrt(((double) (distancesq) )) );
				@into.shape1 = circle1;
				@into.shape2 = circle2;
				double unitVecX = ( circle1.get_x() - circle2.get_x() );
				double unitVecY = ( circle1.get_y() - circle2.get_y() );
				double unitVecLen = global::System.Math.Sqrt(((double) (( ( unitVecX * unitVecX ) + ( unitVecY * unitVecY ) )) ));
				double component = unitVecX;
				if (( unitVecLen == 0 )) {
					unitVecX = ((double) (0) );
				}
				else {
					component /= unitVecLen;
					unitVecX = component;
				}
				
				double component1 = unitVecY;
				if (( unitVecLen == 0 )) {
					unitVecY = ((double) (0) );
				}
				else {
					component1 /= unitVecLen;
					unitVecY = component1;
				}
				
				@into.unitVectorX = unitVecX;
				@into.unitVectorY = unitVecY;
				@into.separationX = ( @into.unitVectorX * difference );
				@into.separationY = ( @into.unitVectorY * difference );
				@into.overlap = difference;
				return @into;
			}
			
			return null;
		}
		
		
		public static global::differ.data.ShapeCollision tmp1;
		
		public static global::differ.data.ShapeCollision tmp2;
		
		public static global::differ.data.ShapeCollision testPolygonVsPolygon(global::differ.shapes.Polygon polygon1, global::differ.shapes.Polygon polygon2, global::differ.data.ShapeCollision @into, global::haxe.lang.Null<bool> flip) {
			bool __temp_flip18 = ( ( ! (flip.hasValue) ) ? (false) : ((flip).@value) );
			if (( @into == null )) {
				@into = new global::differ.data.ShapeCollision();
			}
			else {
				@into.shape1 = @into.shape2 = null;
				@into.overlap = @into.separationX = @into.separationY = @into.unitVectorX = @into.unitVectorY = 0.0;
				@into.otherOverlap = @into.otherSeparationX = @into.otherSeparationY = @into.otherUnitVectorX = @into.otherUnitVectorY = 0.0;
			}
			
			if (( global::differ.sat.SAT2D.checkPolygons(polygon1, polygon2, global::differ.sat.SAT2D.tmp1, new global::haxe.lang.Null<bool>(__temp_flip18, true)) == null )) {
				return null;
			}
			
			if (( global::differ.sat.SAT2D.checkPolygons(polygon2, polygon1, global::differ.sat.SAT2D.tmp2, new global::haxe.lang.Null<bool>( ! (__temp_flip18) , true)) == null )) {
				return null;
			}
			
			global::differ.data.ShapeCollision result = null;
			global::differ.data.ShapeCollision other = null;
			if (( global::System.Math.Abs(((double) (global::differ.sat.SAT2D.tmp1.overlap) )) < global::System.Math.Abs(((double) (global::differ.sat.SAT2D.tmp2.overlap) )) )) {
				result = global::differ.sat.SAT2D.tmp1;
				other = global::differ.sat.SAT2D.tmp2;
			}
			else {
				result = global::differ.sat.SAT2D.tmp2;
				other = global::differ.sat.SAT2D.tmp1;
			}
			
			result.otherOverlap = other.overlap;
			result.otherSeparationX = other.separationX;
			result.otherSeparationY = other.separationY;
			result.otherUnitVectorX = other.unitVectorX;
			result.otherUnitVectorY = other.unitVectorY;
			{
				@into.overlap = result.overlap;
				@into.separationX = result.separationX;
				@into.separationY = result.separationY;
				@into.unitVectorX = result.unitVectorX;
				@into.unitVectorY = result.unitVectorY;
				@into.otherOverlap = result.otherOverlap;
				@into.otherSeparationX = result.otherSeparationX;
				@into.otherSeparationY = result.otherSeparationY;
				@into.otherUnitVectorX = result.otherUnitVectorX;
				@into.otherUnitVectorY = result.otherUnitVectorY;
				@into.shape1 = result.shape1;
				@into.shape2 = result.shape2;
			}
			
			other = null;
			result = other;
			return @into;
		}
		
		
		public static global::differ.data.RayCollision testRayVsCircle(global::differ.shapes.Ray ray, global::differ.shapes.Circle circle, global::differ.data.RayCollision @into) {
			unchecked {
				double deltaX = ( ray.end.x - ray.start.x );
				double deltaY = ( ray.end.y - ray.start.y );
				double ray2circleX = ( ray.start.x - circle.get_position().x );
				double ray2circleY = ( ray.start.y - circle.get_position().y );
				double a = ( ( deltaX * deltaX ) + ( deltaY * deltaY ) );
				double b = ( 2 * (( ( deltaX * ray2circleX ) + ( deltaY * ray2circleY ) )) );
				double c = ( ( ( ray2circleX * ray2circleX ) + ( ray2circleY * ray2circleY ) ) - ( circle.get_radius() * circle.get_radius() ) );
				double d = ( ( b * b ) - ( ( 4 * a ) * c ) );
				if (( d >= 0 )) {
					d = global::System.Math.Sqrt(((double) (d) ));
					double t1 = ( ((  - (b)  - d )) / (( 2 * a )) );
					double t2 = ( ((  - (b)  + d )) / (( 2 * a )) );
					bool valid = default(bool);
					global::differ.shapes.InfiniteState _g = ray.infinite;
					switch (_g.index) {
						case 0:
						{
							if (( t1 >= 0.0 )) {
								valid = ( t1 <= 1.0 );
							}
							else {
								valid = false;
							}
							
							break;
						}
						
						
						case 1:
						{
							valid = ( t1 >= 0.0 );
							break;
						}
						
						
						case 2:
						{
							valid = true;
							break;
						}
						
						
					}
					
					if (valid) {
						if (( @into == null )) {
							@into = new global::differ.data.RayCollision();
						}
						else {
							@into.ray = null;
							@into.shape = null;
							@into.start = 0.0;
							@into.end = 0.0;
						}
						
						@into.shape = circle;
						@into.ray = ray;
						@into.start = t1;
						@into.end = t2;
						return @into;
					}
					
				}
				
				return null;
			}
		}
		
		
		public static global::differ.data.RayCollision testRayVsPolygon(global::differ.shapes.Ray ray, global::differ.shapes.Polygon polygon, global::differ.data.RayCollision @into) {
			unchecked {
				double min_u = global::MathHaxe.POSITIVE_INFINITY;
				double max_u = global::MathHaxe.NEGATIVE_INFINITY;
				double startX = ray.start.x;
				double startY = ray.start.y;
				double deltaX = ( ray.end.x - startX );
				double deltaY = ( ray.end.y - startY );
				global::ArrayHaxe<object> verts = polygon.get_transformedVertices();
				global::differ.math.Vector v1 = ((global::differ.math.Vector) (verts[( verts.length - 1 )]) );
				global::differ.math.Vector v2 = ((global::differ.math.Vector) (verts[0]) );
				double ud = ( ( (( v2.y - v1.y )) * deltaX ) - ( (( v2.x - v1.x )) * deltaY ) );
				double ua = ( (( ( (( v2.x - v1.x )) * (( startY - v1.y )) ) - ( (( v2.y - v1.y )) * (( startX - v1.x )) ) )) / ud );
				double ub = ( (( ( deltaX * (( startY - v1.y )) ) - ( deltaY * (( startX - v1.x )) ) )) / ud );
				if (( ( ( ud != 0.0 ) && ( ub >= 0.0 ) ) && ( ub <= 1.0 ) )) {
					if (( ua < min_u )) {
						min_u = ua;
					}
					
					if (( ua > max_u )) {
						max_u = ua;
					}
					
				}
				
				{
					int _g1 = 1;
					int _g = verts.length;
					while (( _g1 < _g )) {
						int i = _g1++;
						v1 = ((global::differ.math.Vector) (verts[( i - 1 )]) );
						v2 = ((global::differ.math.Vector) (verts[i]) );
						ud = ( ( (( v2.y - v1.y )) * deltaX ) - ( (( v2.x - v1.x )) * deltaY ) );
						ua = ( (( ( (( v2.x - v1.x )) * (( startY - v1.y )) ) - ( (( v2.y - v1.y )) * (( startX - v1.x )) ) )) / ud );
						ub = ( (( ( deltaX * (( startY - v1.y )) ) - ( deltaY * (( startX - v1.x )) ) )) / ud );
						if (( ( ( ud != 0.0 ) && ( ub >= 0.0 ) ) && ( ub <= 1.0 ) )) {
							if (( ua < min_u )) {
								min_u = ua;
							}
							
							if (( ua > max_u )) {
								max_u = ua;
							}
							
						}
						
					}
					
				}
				
				bool valid = default(bool);
				global::differ.shapes.InfiniteState _g2 = ray.infinite;
				switch (_g2.index) {
					case 0:
					{
						if (( min_u >= 0.0 )) {
							valid = ( min_u <= 1.0 );
						}
						else {
							valid = false;
						}
						
						break;
					}
					
					
					case 1:
					{
						if (( min_u != global::MathHaxe.POSITIVE_INFINITY )) {
							valid = ( min_u >= 0.0 );
						}
						else {
							valid = false;
						}
						
						break;
					}
					
					
					case 2:
					{
						valid = ( min_u != global::MathHaxe.POSITIVE_INFINITY );
						break;
					}
					
					
				}
				
				if (valid) {
					if (( @into == null )) {
						@into = new global::differ.data.RayCollision();
					}
					else {
						@into.ray = null;
						@into.shape = null;
						@into.start = 0.0;
						@into.end = 0.0;
					}
					
					@into.shape = polygon;
					@into.ray = ray;
					@into.start = min_u;
					@into.end = max_u;
					return @into;
				}
				
				return null;
			}
		}
		
		
		public static global::differ.data.RayIntersection testRayVsRay(global::differ.shapes.Ray ray1, global::differ.shapes.Ray ray2, global::differ.data.RayIntersection @into) {
			unchecked {
				double delta1X = ( ray1.end.x - ray1.start.x );
				double delta1Y = ( ray1.end.y - ray1.start.y );
				double delta2X = ( ray2.end.x - ray2.start.x );
				double delta2Y = ( ray2.end.y - ray2.start.y );
				double diffX = ( ray1.start.x - ray2.start.x );
				double diffY = ( ray1.start.y - ray2.start.y );
				double ud = ( ( delta2Y * delta1X ) - ( delta2X * delta1Y ) );
				if (( ud == 0.0 )) {
					return null;
				}
				
				double u1 = ( (( ( delta2X * diffY ) - ( delta2Y * diffX ) )) / ud );
				double u2 = ( (( ( delta1X * diffY ) - ( delta1Y * diffX ) )) / ud );
				bool valid1 = default(bool);
				global::differ.shapes.InfiniteState _g = ray1.infinite;
				switch (_g.index) {
					case 0:
					{
						if (( u1 > 0.0 )) {
							valid1 = ( u1 <= 1.0 );
						}
						else {
							valid1 = false;
						}
						
						break;
					}
					
					
					case 1:
					{
						valid1 = ( u1 > 0.0 );
						break;
					}
					
					
					case 2:
					{
						valid1 = true;
						break;
					}
					
					
				}
				
				bool valid2 = default(bool);
				global::differ.shapes.InfiniteState _g1 = ray2.infinite;
				switch (_g1.index) {
					case 0:
					{
						if (( u2 > 0.0 )) {
							valid2 = ( u2 <= 1.0 );
						}
						else {
							valid2 = false;
						}
						
						break;
					}
					
					
					case 1:
					{
						valid2 = ( u2 > 0.0 );
						break;
					}
					
					
					case 2:
					{
						valid2 = true;
						break;
					}
					
					
				}
				
				if (( valid1 && valid2 )) {
					if (( @into == null )) {
						@into = new global::differ.data.RayIntersection();
					}
					else {
						@into.ray1 = null;
						@into.ray2 = null;
						@into.u1 = 0.0;
						@into.u2 = 0.0;
					}
					
					@into.ray1 = ray1;
					@into.ray2 = ray2;
					@into.u1 = u1;
					@into.u2 = u2;
					return @into;
				}
				
				return null;
			}
		}
		
		
		public static global::differ.data.ShapeCollision checkPolygons(global::differ.shapes.Polygon polygon1, global::differ.shapes.Polygon polygon2, global::differ.data.ShapeCollision @into, global::haxe.lang.Null<bool> flip) {
			unchecked {
				bool __temp_flip19 = ( ( ! (flip.hasValue) ) ? (false) : ((flip).@value) );
				{
					@into.shape1 = @into.shape2 = null;
					@into.overlap = @into.separationX = @into.separationY = @into.unitVectorX = @into.unitVectorY = 0.0;
					@into.otherOverlap = @into.otherSeparationX = @into.otherSeparationY = @into.otherUnitVectorX = @into.otherUnitVectorY = 0.0;
				}
				
				double offset = 0.0;
				double test1 = 0.0;
				double test2 = 0.0;
				double testNum = 0.0;
				double min1 = 0.0;
				double max1 = 0.0;
				double min2 = 0.0;
				double max2 = 0.0;
				double closest = ((double) (1073741823) );
				double axisX = 0.0;
				double axisY = 0.0;
				global::ArrayHaxe<object> verts1 = polygon1.get_transformedVertices();
				global::ArrayHaxe<object> verts2 = polygon2.get_transformedVertices();
				{
					int _g1 = 0;
					int _g = verts1.length;
					while (( _g1 < _g )) {
						int i = _g1++;
						global::differ.math.Vector v2 = ( (( i >= ( verts1.length - 1 ) )) ? (((global::differ.math.Vector) (verts1[0]) )) : (((global::differ.math.Vector) (verts1[( i + 1 )]) )) );
						axisX =  - ((( v2.y - ((global::differ.math.Vector) (verts1[i]) ).y ))) ;
						global::differ.math.Vector v21 = ( (( i >= ( verts1.length - 1 ) )) ? (((global::differ.math.Vector) (verts1[0]) )) : (((global::differ.math.Vector) (verts1[( i + 1 )]) )) );
						axisY = ( v21.x - ((global::differ.math.Vector) (verts1[i]) ).x );
						double aLen = global::System.Math.Sqrt(((double) (( ( axisX * axisX ) + ( axisY * axisY ) )) ));
						double component = axisX;
						if (( aLen == 0 )) {
							axisX = ((double) (0) );
						}
						else {
							component /= aLen;
							axisX = component;
						}
						
						double component1 = axisY;
						if (( aLen == 0 )) {
							axisY = ((double) (0) );
						}
						else {
							component1 /= aLen;
							axisY = component1;
						}
						
						min1 = ( ( axisX * ((global::differ.math.Vector) (verts1[0]) ).x ) + ( axisY * ((global::differ.math.Vector) (verts1[0]) ).y ) );
						max1 = min1;
						{
							int _g3 = 1;
							int _g2 = verts1.length;
							while (( _g3 < _g2 )) {
								int j = _g3++;
								testNum = ( ( axisX * ((global::differ.math.Vector) (verts1[j]) ).x ) + ( axisY * ((global::differ.math.Vector) (verts1[j]) ).y ) );
								if (( testNum < min1 )) {
									min1 = testNum;
								}
								
								if (( testNum > max1 )) {
									max1 = testNum;
								}
								
							}
							
						}
						
						min2 = ( ( axisX * ((global::differ.math.Vector) (verts2[0]) ).x ) + ( axisY * ((global::differ.math.Vector) (verts2[0]) ).y ) );
						max2 = min2;
						{
							int _g31 = 1;
							int _g21 = verts2.length;
							while (( _g31 < _g21 )) {
								int j1 = _g31++;
								testNum = ( ( axisX * ((global::differ.math.Vector) (verts2[j1]) ).x ) + ( axisY * ((global::differ.math.Vector) (verts2[j1]) ).y ) );
								if (( testNum < min2 )) {
									min2 = testNum;
								}
								
								if (( testNum > max2 )) {
									max2 = testNum;
								}
								
							}
							
						}
						
						test1 = ( min1 - max2 );
						test2 = ( min2 - max1 );
						if (( ( test1 > 0 ) || ( test2 > 0 ) )) {
							return null;
						}
						
						double distMin =  - ((( max2 - min1 ))) ;
						if (__temp_flip19) {
							distMin *= ((double) (-1) );
						}
						
						if (( global::System.Math.Abs(((double) (distMin) )) < closest )) {
							@into.unitVectorX = axisX;
							@into.unitVectorY = axisY;
							@into.overlap = distMin;
							closest = global::System.Math.Abs(((double) (distMin) ));
						}
						
					}
					
				}
				
				@into.shape1 = ( (__temp_flip19) ? ((global::differ.shapes.Shape) (polygon2) ) : ((global::differ.shapes.Shape) (polygon1) ) );
				@into.shape2 = ( (__temp_flip19) ? ((global::differ.shapes.Shape) (polygon1) ) : ((global::differ.shapes.Shape) (polygon2) ) );
				@into.separationX = (  - (@into.unitVectorX)  * @into.overlap );
				@into.separationY = (  - (@into.unitVectorY)  * @into.overlap );
				if (__temp_flip19) {
					@into.unitVectorX =  - (@into.unitVectorX) ;
					@into.unitVectorY =  - (@into.unitVectorY) ;
				}
				
				return @into;
			}
		}
		
		
		public static double rayU(double udelta, double aX, double aY, double bX, double bY, double dX, double dY) {
			return ( (( ( dX * (( aY - bY )) ) - ( dY * (( aX - bX )) ) )) / udelta );
		}
		
		
		public static double findNormalAxisX(global::ArrayHaxe<object> verts, int index) {
			unchecked {
				global::differ.math.Vector v2 = ( (( index >= ( verts.length - 1 ) )) ? (((global::differ.math.Vector) (verts[0]) )) : (((global::differ.math.Vector) (verts[( index + 1 )]) )) );
				return  - ((( v2.y - ((global::differ.math.Vector) (verts[index]) ).y ))) ;
			}
		}
		
		
		public static double findNormalAxisY(global::ArrayHaxe<object> verts, int index) {
			unchecked {
				global::differ.math.Vector v2 = ( (( index >= ( verts.length - 1 ) )) ? (((global::differ.math.Vector) (verts[0]) )) : (((global::differ.math.Vector) (verts[( index + 1 )]) )) );
				return ( v2.x - ((global::differ.math.Vector) (verts[index]) ).x );
			}
		}
		
		
	}
}


