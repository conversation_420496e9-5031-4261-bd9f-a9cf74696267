// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace differ.shapes {
	public class Polygon : global::differ.shapes.Shape {
		
		public Polygon(global::haxe.lang.EmptyObject empty) : base(global::haxe.lang.EmptyObject.EMPTY) {
		}
		
		
		public Polygon(double x, double y, global::ArrayHaxe<object> vertices) : base(global::haxe.lang.EmptyObject.EMPTY) {
			global::differ.shapes.Polygon.__hx_ctor_differ_shapes_Polygon(this, x, y, vertices);
		}
		
		
		public static void __hx_ctor_differ_shapes_Polygon(global::differ.shapes.Polygon __hx_this, double x, double y, global::ArrayHaxe<object> vertices) {
			global::differ.shapes.Shape.__hx_ctor_differ_shapes_Shape(__hx_this, x, y);
			__hx_this.name = global::haxe.lang.Runtime.concat(global::haxe.lang.Runtime.concat("polygon(sides:", global::haxe.lang.Runtime.toString(vertices.length)), ")");
			__hx_this._transformedVertices = new global::ArrayHaxe<object>();
			__hx_this._vertices = vertices;
		}
		
		
		public static global::differ.shapes.Polygon create(double x, double y, int sides, global::haxe.lang.Null<double> radius) {
			unchecked {
				double __temp_radius26 = ( ( ! (radius.hasValue) ) ? (((double) (100) )) : ((radius).@value) );
				if (( sides < 3 )) {
					throw global::haxe.lang.HaxeException.wrap("Polygon - Needs at least 3 sides");
				}
				
				double rotation = ( ( global::MathHaxe.PI * 2 ) / sides );
				double angle = default(double);
				global::differ.math.Vector vector = null;
				global::ArrayHaxe<object> vertices = new global::ArrayHaxe<object>();
				{
					int _g1 = 0;
					int _g = sides;
					while (( _g1 < _g )) {
						int i = _g1++;
						angle = ( ( i * rotation ) + ( (( global::MathHaxe.PI - rotation )) * 0.5 ) );
						vector = new global::differ.math.Vector(default(global::haxe.lang.Null<double>), default(global::haxe.lang.Null<double>));
						vector.x = ( global::System.Math.Cos(((double) (angle) )) * __temp_radius26 );
						vector.y = ( global::System.Math.Sin(((double) (angle) )) * __temp_radius26 );
						vertices.push(vector);
					}
					
				}
				
				return new global::differ.shapes.Polygon(x, y, vertices);
			}
		}
		
		
		public static global::differ.shapes.Polygon rectangle(double x, double y, double width, double height, global::haxe.lang.Null<bool> centered) {
			unchecked {
				bool __temp_centered27 = ( ( ! (centered.hasValue) ) ? (true) : ((centered).@value) );
				global::ArrayHaxe<object> vertices = new global::ArrayHaxe<object>();
				if (__temp_centered27) {
					vertices.push(new global::differ.math.Vector(new global::haxe.lang.Null<double>((  - (width)  / 2 ), true), new global::haxe.lang.Null<double>((  - (height)  / 2 ), true)));
					vertices.push(new global::differ.math.Vector(new global::haxe.lang.Null<double>(( width / 2 ), true), new global::haxe.lang.Null<double>((  - (height)  / 2 ), true)));
					vertices.push(new global::differ.math.Vector(new global::haxe.lang.Null<double>(( width / 2 ), true), new global::haxe.lang.Null<double>(( height / 2 ), true)));
					vertices.push(new global::differ.math.Vector(new global::haxe.lang.Null<double>((  - (width)  / 2 ), true), new global::haxe.lang.Null<double>(( height / 2 ), true)));
				}
				else {
					vertices.push(new global::differ.math.Vector(new global::haxe.lang.Null<double>(((double) (0) ), true), new global::haxe.lang.Null<double>(((double) (0) ), true)));
					vertices.push(new global::differ.math.Vector(new global::haxe.lang.Null<double>(width, true), new global::haxe.lang.Null<double>(((double) (0) ), true)));
					vertices.push(new global::differ.math.Vector(new global::haxe.lang.Null<double>(width, true), new global::haxe.lang.Null<double>(height, true)));
					vertices.push(new global::differ.math.Vector(new global::haxe.lang.Null<double>(((double) (0) ), true), new global::haxe.lang.Null<double>(height, true)));
				}
				
				return new global::differ.shapes.Polygon(x, y, vertices);
			}
		}

        public static void updateRectangle(global::differ.shapes.Polygon rec, double x, double y, double width, double height, global::haxe.lang.Null<bool> centered)
        {
            unchecked
            {
                bool __temp_centered27 = ((!(centered.hasValue)) ? (true) : ((centered).@value));
                global::ArrayHaxe<object> vertices = rec._vertices;
                if (__temp_centered27)
                {
                    var v = (global::differ.math.Vector)vertices[0];
                    v.x = -width / 2;
                    v.y = -height / 2;

                    v = (global::differ.math.Vector)vertices[1];
                    v.x = width / 2;
                    v.y = -height / 2;

                    v = (global::differ.math.Vector)vertices[2];
                    v.x = width / 2;
                    v.y = height / 2;

                    v = (global::differ.math.Vector)vertices[3];
                    v.x = -width / 2;
                    v.y = height / 2;
                }
                else
                {
                    var v = (global::differ.math.Vector)vertices[0];
                    v.x = 0;
                    v.y = 0;

                    v = (global::differ.math.Vector)vertices[1];
                    v.x = width;
                    v.y = 0;

                    v = (global::differ.math.Vector)vertices[2];
                    v.x = width;
                    v.y = height;

                    v = (global::differ.math.Vector)vertices[3];
                    v.x = 0;
                    v.y = height;
                }
            }
        }


        public static global::differ.shapes.Polygon square(double x, double y, double width, global::haxe.lang.Null<bool> centered) {
			bool __temp_centered28 = ( ( ! (centered.hasValue) ) ? (true) : ((centered).@value) );
			return global::differ.shapes.Polygon.rectangle(x, y, width, width, new global::haxe.lang.Null<bool>(__temp_centered28, true));
		}
		
		
		public static global::differ.shapes.Polygon triangle(double x, double y, double radius) {
			unchecked {
				return global::differ.shapes.Polygon.create(x, y, 3, new global::haxe.lang.Null<double>(radius, true));
			}
		}
		
		
		
		
		
		
		public global::ArrayHaxe<object> _transformedVertices;
		
		public global::ArrayHaxe<object> _vertices;
		
		public override global::differ.data.ShapeCollision test(global::differ.shapes.Shape shape, global::differ.data.ShapeCollision @into) {
			return shape.testPolygon(this, @into, new global::haxe.lang.Null<bool>(true, true));
		}
		
		
		public override global::differ.data.ShapeCollision testCircle(global::differ.shapes.Circle circle, global::differ.data.ShapeCollision @into, global::haxe.lang.Null<bool> flip) {
			bool __temp_flip24 = ( ( ! (flip.hasValue) ) ? (false) : ((flip).@value) );
			return global::differ.sat.SAT2D.testCircleVsPolygon(circle, this, @into, new global::haxe.lang.Null<bool>( ! (__temp_flip24) , true));
		}
		
		
		public override global::differ.data.ShapeCollision testPolygon(global::differ.shapes.Polygon polygon, global::differ.data.ShapeCollision @into, global::haxe.lang.Null<bool> flip) {
			bool __temp_flip25 = ( ( ! (flip.hasValue) ) ? (false) : ((flip).@value) );
			return global::differ.sat.SAT2D.testPolygonVsPolygon(this, polygon, @into, new global::haxe.lang.Null<bool>(__temp_flip25, true));
		}
		
		
		public override global::differ.data.RayCollision testRay(global::differ.shapes.Ray ray, global::differ.data.RayCollision @into) {
			return global::differ.sat.SAT2D.testRayVsPolygon(ray, this, null);
		}
		
		
		public override void destroy() {
			int _count = this._vertices.length;
			{
				int _g1 = 0;
				int _g = _count;
				while (( _g1 < _g )) {
					int i = _g1++;
					this._vertices[i] = null;
				}
				
			}
			
			this._transformedVertices = null;
			this._vertices = null;
			base.destroy();
		}
		
		
		public virtual global::ArrayHaxe<object> get_transformedVertices() {
			if ( ! (this._transformed) ) {
				this._transformedVertices = new global::ArrayHaxe<object>();
				this._transformed = true;
				int _count = this._vertices.length;
				{
					int _g1 = 0;
					int _g = _count;
					while (( _g1 < _g )) {
						int i = _g1++;
						global::differ.math.Vector _this = ((global::differ.math.Vector) (this._vertices[i]) );
						this._transformedVertices.push(new global::differ.math.Vector(new global::haxe.lang.Null<double>(_this.x, true), new global::haxe.lang.Null<double>(_this.y, true)).transform(this._transformMatrix));
					}
					
				}
				
			}
			
			return this._transformedVertices;
		}
		
		
		public virtual global::ArrayHaxe<object> get_vertices() {
			return this._vertices;
		}
		
		
		public override object __hx_setField(string field, int hash, object @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 833337176:
					{
						this._vertices = ((global::ArrayHaxe<object>) (global::ArrayHaxe<object>.__hx_cast<object>(((global::ArrayHaxe) (@value) ))) );
						return @value;
					}
					
					
					case 416386789:
					{
						this._transformedVertices = ((global::ArrayHaxe<object>) (global::ArrayHaxe<object>.__hx_cast<object>(((global::ArrayHaxe) (@value) ))) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_getField(string field, int hash, bool throwErrors, bool isCheck, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 523203586:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "get_vertices", 523203586)) );
					}
					
					
					case 567156347:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "get_transformedVertices", 567156347)) );
					}
					
					
					case 612773114:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "destroy", 612773114)) );
					}
					
					
					case 1036338360:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "testRay", 1036338360)) );
					}
					
					
					case 1331294280:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "testPolygon", 1331294280)) );
					}
					
					
					case 1862383618:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "testCircle", 1862383618)) );
					}
					
					
					case 1291438162:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "test", 1291438162)) );
					}
					
					
					case 833337176:
					{
						return this._vertices;
					}
					
					
					case 416386789:
					{
						return this._transformedVertices;
					}
					
					
					case 1779810297:
					{
						return this.get_vertices();
					}
					
					
					case 427325412:
					{
						return this.get_transformedVertices();
					}
					
					
					default:
					{
						return base.__hx_getField(field, hash, throwErrors, isCheck, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_invokeField(string field, int hash, global::ArrayHaxe dynargs) {
			unchecked {
				switch (hash) {
					case 1291438162:
					case 1862383618:
					case 1331294280:
					case 1036338360:
					case 612773114:
					{
						return global::haxe.lang.Runtime.slowCallField(this, field, dynargs);
					}
					
					
					case 523203586:
					{
						return this.get_vertices();
					}
					
					
					case 567156347:
					{
						return this.get_transformedVertices();
					}
					
					
					default:
					{
						return base.__hx_invokeField(field, hash, dynargs);
					}
					
				}
				
			}
		}
		
		
		public override void __hx_getFields(global::ArrayHaxe<object> baseArr) {
			baseArr.push("_vertices");
			baseArr.push("_transformedVertices");
			baseArr.push("vertices");
			baseArr.push("transformedVertices");
			base.__hx_getFields(baseArr);
		}
		
		
	}
}


