// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace differ.shapes {
	public class Ray : global::haxe.lang.HxObject {
		
		public Ray(global::haxe.lang.EmptyObject empty) {
		}
		
		
		public Ray(global::differ.math.Vector _start, global::differ.math.Vector _end, global::differ.shapes.InfiniteState _infinite) {
			global::differ.shapes.Ray.__hx_ctor_differ_shapes_Ray(this, _start, _end, _infinite);
		}
		
		
		public static void __hx_ctor_differ_shapes_Ray(global::differ.shapes.Ray __hx_this, global::differ.math.Vector _start, global::differ.math.Vector _end, global::differ.shapes.InfiniteState _infinite) {
			__hx_this.start = _start;
			__hx_this.end = _end;
			__hx_this.infinite = ( (( _infinite == null )) ? (global::differ.shapes.InfiniteState.not_infinite) : (_infinite) );
			__hx_this.dir_cache = new global::differ.math.Vector(new global::haxe.lang.Null<double>(( __hx_this.end.x - __hx_this.start.x ), true), new global::haxe.lang.Null<double>(( __hx_this.end.y - __hx_this.start.y ), true));
		}
		
		
		public global::differ.math.Vector start;
		
		public global::differ.math.Vector end;
		
		
		
		public global::differ.shapes.InfiniteState infinite;
		
		public global::differ.math.Vector dir_cache;
		
		public virtual global::differ.math.Vector get_dir() {
			this.dir_cache.x = ( this.end.x - this.start.x );
			this.dir_cache.y = ( this.end.y - this.start.y );
			return this.dir_cache;
		}
		
		
		public override object __hx_setField(string field, int hash, object @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 495490704:
					{
						this.dir_cache = ((global::differ.math.Vector) (@value) );
						return @value;
					}
					
					
					case 1516836564:
					{
						this.infinite = ((global::differ.shapes.InfiniteState) (@value) );
						return @value;
					}
					
					
					case 5047259:
					{
						this.end = ((global::differ.math.Vector) (@value) );
						return @value;
					}
					
					
					case 67859554:
					{
						this.start = ((global::differ.math.Vector) (@value) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_getField(string field, int hash, bool throwErrors, bool isCheck, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 650184164:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "get_dir", 650184164)) );
					}
					
					
					case 495490704:
					{
						return this.dir_cache;
					}
					
					
					case 1516836564:
					{
						return this.infinite;
					}
					
					
					case 4996429:
					{
						return this.get_dir();
					}
					
					
					case 5047259:
					{
						return this.end;
					}
					
					
					case 67859554:
					{
						return this.start;
					}
					
					
					default:
					{
						return base.__hx_getField(field, hash, throwErrors, isCheck, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_invokeField(string field, int hash, global::ArrayHaxe dynargs) {
			unchecked {
				switch (hash) {
					case 650184164:
					{
						return this.get_dir();
					}
					
					
					default:
					{
						return base.__hx_invokeField(field, hash, dynargs);
					}
					
				}
				
			}
		}
		
		
		public override void __hx_getFields(global::ArrayHaxe<object> baseArr) {
			baseArr.push("dir_cache");
			baseArr.push("infinite");
			baseArr.push("dir");
			baseArr.push("end");
			baseArr.push("start");
			base.__hx_getFields(baseArr);
		}
		
		
	}
}



#pragma warning disable 109, 114, 219, 429, 168, 162
namespace differ.shapes {
	public class InfiniteState : global::haxe.lang.Enum {
		
		public InfiniteState(int index) : base(index) {
		}
		
		
		public static readonly string[] __hx_constructs = new string[]{"not_infinite", "infinite_from_start", "infinite"};
		
		public static readonly global::differ.shapes.InfiniteState not_infinite = new global::differ.shapes.InfiniteState(0);
		
		public static readonly global::differ.shapes.InfiniteState infinite_from_start = new global::differ.shapes.InfiniteState(1);
		
		public static readonly global::differ.shapes.InfiniteState infinite = new global::differ.shapes.InfiniteState(2);
		
		public override string getTag() {
			return global::differ.shapes.InfiniteState.__hx_constructs[this.index];
		}
		
		
	}
}


