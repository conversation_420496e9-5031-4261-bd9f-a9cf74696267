// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace differ.shapes {
	public class Shape : global::haxe.lang.HxObject {
		
		public Shape(global::haxe.lang.EmptyObject empty) {
		}
		
		
		public Shape(double _x, double _y) {
			global::differ.shapes.Shape.__hx_ctor_differ_shapes_Shape(this, _x, _y);
		}
		
		
		public static void __hx_ctor_differ_shapes_Shape(global::differ.shapes.Shape __hx_this, double _x, double _y) {
			unchecked {
				__hx_this._transformed = false;
				__hx_this._scaleY = ((double) (((int) (1) )) );
				__hx_this._scaleX = ((double) (((int) (1) )) );
				__hx_this._rotation_radians = ((double) (((int) (0) )) );
				__hx_this._rotation = ((double) (((int) (0) )) );
				__hx_this.name = "shape";
				__hx_this.active = true;
				__hx_this.tags = new global::haxe.ds.StringMap<object>();
				__hx_this._position = new global::differ.math.Vector(new global::haxe.lang.Null<double>(_x, true), new global::haxe.lang.Null<double>(_y, true));
				__hx_this._scale = new global::differ.math.Vector(new global::haxe.lang.Null<double>(((double) (1) ), true), new global::haxe.lang.Null<double>(((double) (1) ), true));
				__hx_this._rotation = ((double) (0) );
				__hx_this._scaleX = ((double) (1) );
				__hx_this._scaleY = ((double) (1) );
				__hx_this._transformMatrix = new global::differ.math.Matrix(default(global::haxe.lang.Null<double>), default(global::haxe.lang.Null<double>), default(global::haxe.lang.Null<double>), default(global::haxe.lang.Null<double>), default(global::haxe.lang.Null<double>), default(global::haxe.lang.Null<double>));
				__hx_this._transformMatrix.makeTranslation(__hx_this._position.x, __hx_this._position.y);
			}
		}
		
		
		public bool active;
		
		public string name;
		
		public object data;
		
		public global::haxe.ds.StringMap<object> tags;
		
		
		
		
		
		
		
		
		
		
		
		
		
		public global::differ.math.Vector _position;
		
		public double _rotation;
		
		public double _rotation_radians;
		
		public global::differ.math.Vector _scale;
		
		public double _scaleX;
		
		public double _scaleY;
		
		public bool _transformed;
		
		public global::differ.math.Matrix _transformMatrix;
		
		public virtual global::differ.data.ShapeCollision test(global::differ.shapes.Shape shape, global::differ.data.ShapeCollision @into) {
			return null;
		}
		
		
		public virtual global::differ.data.ShapeCollision testCircle(global::differ.shapes.Circle circle, global::differ.data.ShapeCollision @into, global::haxe.lang.Null<bool> flip) {
			bool __temp_flip20 = ( ( ! (flip.hasValue) ) ? (false) : ((flip).@value) );
			return null;
		}
		
		
		public virtual global::differ.data.ShapeCollision testPolygon(global::differ.shapes.Polygon polygon, global::differ.data.ShapeCollision @into, global::haxe.lang.Null<bool> flip) {
			bool __temp_flip21 = ( ( ! (flip.hasValue) ) ? (false) : ((flip).@value) );
			return null;
		}
		
		
		public virtual global::differ.data.RayCollision testRay(global::differ.shapes.Ray ray, global::differ.data.RayCollision @into) {
			return null;
		}
		
		
		public virtual void destroy() {
			this._position = null;
			this._scale = null;
			this._transformMatrix = null;
		}
		
		
		public virtual void refresh_transform() {
			this._transformMatrix.compose(this._position, this._rotation_radians, this._scale);
			this._transformed = false;
		}
		
		
		public virtual global::differ.math.Vector get_position() {
			return this._position;
		}
		
		
		public virtual global::differ.math.Vector set_position(global::differ.math.Vector v) {
			this._position = v;
			this.refresh_transform();
			return this._position;
		}
		
		
		public virtual double get_x() {
			return this._position.x;
		}
		
		
		public virtual double set_x(double x) {
			this._position.x = x;
			this.refresh_transform();
			return this._position.x;
		}
		
		
		public virtual double get_y() {
			return this._position.y;
		}
		
		
		public virtual double set_y(double y) {
			this._position.y = y;
			this.refresh_transform();
			return this._position.y;
		}
		
		
		public virtual double get_rotation() {
			return this._rotation;
		}
		
		
		public virtual double set_rotation(double v) {
			unchecked {
				this._rotation_radians = ( v * (( global::MathHaxe.PI / 180 )) );
				this.refresh_transform();
				return this._rotation = v;
			}
		}

        public virtual double set_rotation_rad(double v)
        {
            unchecked
            {
                this._rotation_radians = v;
                this.refresh_transform();
                this._rotation = (v * ((180 / global::MathHaxe.PI)));
                return this._rotation;
            }
        }

        public virtual double get_scaleX() {
			return this._scaleX;
		}
		
		
		public virtual double set_scaleX(double scale) {
			this._scaleX = scale;
			this._scale.x = this._scaleX;
			this.refresh_transform();
			return this._scaleX;
		}
		
		
		public virtual double get_scaleY() {
			return this._scaleY;
		}
		
		
		public virtual double set_scaleY(double scale) {
			this._scaleY = scale;
			this._scale.y = this._scaleY;
			this.refresh_transform();
			return this._scaleY;
		}
		
		
		public override double __hx_setField_f(string field, int hash, double @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 714931246:
					{
						this._scaleY = ((double) (@value) );
						return @value;
					}
					
					
					case 714931245:
					{
						this._scaleX = ((double) (@value) );
						return @value;
					}
					
					
					case 338049424:
					{
						this._rotation_radians = ((double) (@value) );
						return @value;
					}
					
					
					case 1344025757:
					{
						this._rotation = ((double) (@value) );
						return @value;
					}
					
					
					case 1009117839:
					{
						this.set_scaleY(@value);
						return @value;
					}
					
					
					case 1009117838:
					{
						this.set_scaleX(@value);
						return @value;
					}
					
					
					case 143015230:
					{
						this.set_rotation(@value);
						return @value;
					}
					
					
					case 121:
					{
						this.set_y(@value);
						return @value;
					}
					
					
					case 120:
					{
						this.set_x(@value);
						return @value;
					}
					
					
					case 1113806378:
					{
						this.data = ((object) (@value) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField_f(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_setField(string field, int hash, object @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1643967310:
					{
						this._transformMatrix = ((global::differ.math.Matrix) (@value) );
						return @value;
					}
					
					
					case 416573132:
					{
						this._transformed = global::haxe.lang.Runtime.toBool(@value);
						return @value;
					}
					
					
					case 714931246:
					{
						this._scaleY = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 714931245:
					{
						this._scaleX = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 1958090187:
					{
						this._scale = ((global::differ.math.Vector) (@value) );
						return @value;
					}
					
					
					case 338049424:
					{
						this._rotation_radians = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 1344025757:
					{
						this._rotation = ((double) (global::haxe.lang.Runtime.toDouble(@value)) );
						return @value;
					}
					
					
					case 311465992:
					{
						this._position = ((global::differ.math.Vector) (@value) );
						return @value;
					}
					
					
					case 1009117839:
					{
						this.set_scaleY(((double) (global::haxe.lang.Runtime.toDouble(@value)) ));
						return @value;
					}
					
					
					case 1009117838:
					{
						this.set_scaleX(((double) (global::haxe.lang.Runtime.toDouble(@value)) ));
						return @value;
					}
					
					
					case 143015230:
					{
						this.set_rotation(((double) (global::haxe.lang.Runtime.toDouble(@value)) ));
						return @value;
					}
					
					
					case 121:
					{
						this.set_y(((double) (global::haxe.lang.Runtime.toDouble(@value)) ));
						return @value;
					}
					
					
					case 120:
					{
						this.set_x(((double) (global::haxe.lang.Runtime.toDouble(@value)) ));
						return @value;
					}
					
					
					case 1257939113:
					{
						this.set_position(((global::differ.math.Vector) (@value) ));
						return @value;
					}
					
					
					case 1291236569:
					{
						this.tags = ((global::haxe.ds.StringMap<object>) (global::haxe.ds.StringMap<object>.__hx_cast<object>(((global::haxe.ds.StringMap) (@value) ))) );
						return @value;
					}
					
					
					case 1113806378:
					{
						this.data = ((object) (@value) );
						return @value;
					}
					
					
					case 1224700491:
					{
						this.name = global::haxe.lang.Runtime.toString(@value);
						return @value;
					}
					
					
					case 373703110:
					{
						this.active = global::haxe.lang.Runtime.toBool(@value);
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_getField(string field, int hash, bool throwErrors, bool isCheck, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 808122572:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "set_scaleY", 808122572)) );
					}
					
					
					case 749558360:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "get_scaleY", 749558360)) );
					}
					
					
					case 808122571:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "set_scaleX", 808122571)) );
					}
					
					
					case 749558359:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "get_scaleX", 749558359)) );
					}
					
					
					case 1385764027:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "set_rotation", 1385764027)) );
					}
					
					
					case 1033892167:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "get_rotation", 1033892167)) );
					}
					
					
					case 2049940316:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "set_y", 2049940316)) );
					}
					
					
					case 291546448:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "get_y", 291546448)) );
					}
					
					
					case 2049940315:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "set_x", 2049940315)) );
					}
					
					
					case 291546447:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "get_x", 291546447)) );
					}
					
					
					case 353204262:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "set_position", 353204262)) );
					}
					
					
					case 1332402:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "get_position", 1332402)) );
					}
					
					
					case 2105901768:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "refresh_transform", 2105901768)) );
					}
					
					
					case 612773114:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "destroy", 612773114)) );
					}
					
					
					case 1036338360:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "testRay", 1036338360)) );
					}
					
					
					case 1331294280:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "testPolygon", 1331294280)) );
					}
					
					
					case 1862383618:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "testCircle", 1862383618)) );
					}
					
					
					case 1291438162:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "test", 1291438162)) );
					}
					
					
					case 1643967310:
					{
						return this._transformMatrix;
					}
					
					
					case 416573132:
					{
						return this._transformed;
					}
					
					
					case 714931246:
					{
						return this._scaleY;
					}
					
					
					case 714931245:
					{
						return this._scaleX;
					}
					
					
					case 1958090187:
					{
						return this._scale;
					}
					
					
					case 338049424:
					{
						return this._rotation_radians;
					}
					
					
					case 1344025757:
					{
						return this._rotation;
					}
					
					
					case 311465992:
					{
						return this._position;
					}
					
					
					case 1009117839:
					{
						return this.get_scaleY();
					}
					
					
					case 1009117838:
					{
						return this.get_scaleX();
					}
					
					
					case 143015230:
					{
						return this.get_rotation();
					}
					
					
					case 121:
					{
						return this.get_y();
					}
					
					
					case 120:
					{
						return this.get_x();
					}
					
					
					case 1257939113:
					{
						return this.get_position();
					}
					
					
					case 1291236569:
					{
						return this.tags;
					}
					
					
					case 1113806378:
					{
						return this.data;
					}
					
					
					case 1224700491:
					{
						return this.name;
					}
					
					
					case 373703110:
					{
						return this.active;
					}
					
					
					default:
					{
						return base.__hx_getField(field, hash, throwErrors, isCheck, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override double __hx_getField_f(string field, int hash, bool throwErrors, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 714931246:
					{
						return this._scaleY;
					}
					
					
					case 714931245:
					{
						return this._scaleX;
					}
					
					
					case 338049424:
					{
						return this._rotation_radians;
					}
					
					
					case 1344025757:
					{
						return this._rotation;
					}
					
					
					case 1009117839:
					{
						return this.get_scaleY();
					}
					
					
					case 1009117838:
					{
						return this.get_scaleX();
					}
					
					
					case 143015230:
					{
						return this.get_rotation();
					}
					
					
					case 121:
					{
						return this.get_y();
					}
					
					
					case 120:
					{
						return this.get_x();
					}
					
					
					case 1113806378:
					{
						return ((double) (global::haxe.lang.Runtime.toDouble(this.data)) );
					}
					
					
					default:
					{
						return base.__hx_getField_f(field, hash, throwErrors, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_invokeField(string field, int hash, global::ArrayHaxe dynargs) {
			unchecked {
				switch (hash) {
					case 808122572:
					{
						return this.set_scaleY(((double) (global::haxe.lang.Runtime.toDouble(dynargs[0])) ));
					}
					
					
					case 749558360:
					{
						return this.get_scaleY();
					}
					
					
					case 808122571:
					{
						return this.set_scaleX(((double) (global::haxe.lang.Runtime.toDouble(dynargs[0])) ));
					}
					
					
					case 749558359:
					{
						return this.get_scaleX();
					}
					
					
					case 1385764027:
					{
						return this.set_rotation(((double) (global::haxe.lang.Runtime.toDouble(dynargs[0])) ));
					}
					
					
					case 1033892167:
					{
						return this.get_rotation();
					}
					
					
					case 2049940316:
					{
						return this.set_y(((double) (global::haxe.lang.Runtime.toDouble(dynargs[0])) ));
					}
					
					
					case 291546448:
					{
						return this.get_y();
					}
					
					
					case 2049940315:
					{
						return this.set_x(((double) (global::haxe.lang.Runtime.toDouble(dynargs[0])) ));
					}
					
					
					case 291546447:
					{
						return this.get_x();
					}
					
					
					case 353204262:
					{
						return this.set_position(((global::differ.math.Vector) (dynargs[0]) ));
					}
					
					
					case 1332402:
					{
						return this.get_position();
					}
					
					
					case 2105901768:
					{
						this.refresh_transform();
						break;
					}
					
					
					case 612773114:
					{
						this.destroy();
						break;
					}
					
					
					case 1036338360:
					{
						return this.testRay(((global::differ.shapes.Ray) (dynargs[0]) ), ((global::differ.data.RayCollision) (dynargs[1]) ));
					}
					
					
					case 1331294280:
					{
						return this.testPolygon(((global::differ.shapes.Polygon) (dynargs[0]) ), ((global::differ.data.ShapeCollision) (dynargs[1]) ), global::haxe.lang.Null<object>.ofDynamic<bool>(dynargs[2]));
					}
					
					
					case 1862383618:
					{
						return this.testCircle(((global::differ.shapes.Circle) (dynargs[0]) ), ((global::differ.data.ShapeCollision) (dynargs[1]) ), global::haxe.lang.Null<object>.ofDynamic<bool>(dynargs[2]));
					}
					
					
					case 1291438162:
					{
						return this.test(((global::differ.shapes.Shape) (dynargs[0]) ), ((global::differ.data.ShapeCollision) (dynargs[1]) ));
					}
					
					
					default:
					{
						return base.__hx_invokeField(field, hash, dynargs);
					}
					
				}
				
				return null;
			}
		}
		
		
		public override void __hx_getFields(global::ArrayHaxe<object> baseArr) {
			baseArr.push("_transformMatrix");
			baseArr.push("_transformed");
			baseArr.push("_scaleY");
			baseArr.push("_scaleX");
			baseArr.push("_scale");
			baseArr.push("_rotation_radians");
			baseArr.push("_rotation");
			baseArr.push("_position");
			baseArr.push("scaleY");
			baseArr.push("scaleX");
			baseArr.push("rotation");
			baseArr.push("y");
			baseArr.push("x");
			baseArr.push("position");
			baseArr.push("tags");
			baseArr.push("data");
			baseArr.push("name");
			baseArr.push("active");
			base.__hx_getFields(baseArr);
		}
		
		
	}
}


