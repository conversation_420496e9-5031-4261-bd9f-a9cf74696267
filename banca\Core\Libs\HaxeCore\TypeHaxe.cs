// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
public class ValueType : global::haxe.lang.ParamEnum {
	
	public ValueType(int index, object[] @params) : base(index, @params) {
	}
	
	
	public static readonly string[] __hx_constructs = new string[]{"TNull", "TInt", "TFloat", "TBool", "TObject", "TFunction", "TClass", "TEnum", "TUnknown"};
	
	public static readonly global::ValueType TNull = new global::ValueType(0, null);
	
	public static readonly global::ValueType TInt = new global::ValueType(1, null);
	
	public static readonly global::ValueType TFloat = new global::ValueType(2, null);
	
	public static readonly global::ValueType TBool = new global::ValueType(3, null);
	
	public static readonly global::ValueType TObject = new global::ValueType(4, null);
	
	public static readonly global::ValueType TFunction = new global::ValueType(5, null);
	
	public static global::ValueType TClass(global::System.Type c) {
		unchecked {
			return new global::ValueType(6, new object[]{c});
		}
	}
	
	
	public static global::ValueType TEnum(global::System.Type e) {
		unchecked {
			return new global::ValueType(7, new object[]{e});
		}
	}
	
	
	public static readonly global::ValueType TUnknown = new global::ValueType(8, null);
	
	public override string getTag() {
		return global::ValueType.__hx_constructs[this.index];
	}
	
	
}



#pragma warning disable 109, 114, 219, 429, 168, 162
public class TypeHaxe : global::haxe.lang.HxObject {
	
	public TypeHaxe(global::haxe.lang.EmptyObject empty) {
	}
	
	
	public TypeHaxe() {
		global::TypeHaxe.__hx_ctor__Type(this);
	}
	
	
	public static void __hx_ctor__Type(global::TypeHaxe __hx_this) {
	}
	
	
	public static global::System.Type getClass<T>(T o) {
		if (( ( global::System.Object.ReferenceEquals(((object) (o) ), default(object)) || ( o is global::haxe.lang.DynamicObject ) ) || ( o is global::System.Type ) )) {
			return null;
		}
		
		return o.GetType();
	}
	
	
	public static global::System.Type getEnum(object o) {
		if (( ( o is global::System.Enum ) || ( o is global::haxe.lang.Enum ) )) {
			return o.GetType();
		}
		
		return null;
	}
	
	
	public static global::System.Type getSuperClass(global::System.Type c) {
		global::System.Type t = ((global::System.Type) (c) );
		global::System.Type @base = t.BaseType;
		if (( ( global::haxe.lang.Runtime.typeEq(@base, null) || string.Equals(( @base as global::System.Reflection.MemberInfo ).ToString(), "haxe.lang.HxObject") ) || string.Equals(( @base as global::System.Reflection.MemberInfo ).ToString(), "System.Object") )) {
			return null;
		}
		
		return ((global::System.Type) (@base) );
	}
	
	
	public static string getClassName(global::System.Type c) {
		string ret = global::haxe.lang.Runtime.toString(((global::System.Type) (c) ));
		switch (ret) {
			case "System.Double":
			{
				return "Float";
			}
			
			
			case "System.Int32":
			{
				return "Int";
			}
			
			
			case "System.Object":
			{
				return "Dynamic";
			}
			
			
			case "System.String":
			{
				return "String";
			}
			
			
			case "System.Type":
			{
				return "Class";
			}
			
			
			default:
			{
				return global::haxe.lang.Runtime.toString(global::haxe.lang.StringExt.split(ret, "`")[0]);
			}
			
		}
		
	}
	
	
	public static string getEnumName(global::System.Type e) {
		unchecked {
			string ret = global::haxe.lang.Runtime.toString(((global::System.Type) (e) ));
			if (( ( ret.Length == 14 ) && string.Equals(ret, "System.Boolean") )) {
				return "Bool";
			}
			
			return ret;
		}
	}
	
	
	public static global::System.Type resolveClass(string name) {
		global::System.Type t = global::System.Type.GetType(((string) (name) ));
		if (global::haxe.lang.Runtime.typeEq(t, null)) {
			global::System.Collections.IEnumerator all = ( global::System.AppDomain.CurrentDomain.GetAssemblies() as global::System.Array ).GetEnumerator();
			while (all.MoveNext()) {
				global::System.Reflection.Assembly t2 = ((global::System.Reflection.Assembly) (all.Current) );
				t = t2.GetType(((string) (name) ));
				if ( ! (global::haxe.lang.Runtime.typeEq(t, null)) ) {
					break;
				}
				
			}
			
		}
		
		if (global::haxe.lang.Runtime.typeEq(t, null)) {
			switch (name) {
				case "Class":
				{
					return ((global::System.Type) (typeof(global::System.Type)) );
				}
				
				
				case "Dynamic":
				{
					return ((global::System.Type) (typeof(object)) );
				}
				
				
				case "Float":
				{
					return ((global::System.Type) (typeof(double)) );
				}
				
				
				case "Int":
				{
					return ((global::System.Type) (typeof(int)) );
				}
				
				
				case "String":
				{
					return ((global::System.Type) (typeof(string)) );
				}
				
				
				default:
				{
					return null;
				}
				
			}
			
		}
		else if (( t.IsInterface && (((global::System.Type) (typeof(global::haxe.lang.IGenericObject)) )).IsAssignableFrom(((global::System.Type) (t) )) )) {
			{
				uint _g_idx = default(uint);
				object[] _g_arr = ( t as global::System.Reflection.MemberInfo ).GetCustomAttributes(((bool) (true) ));
				_g_idx = ((uint) (0) );
				while (((bool) (( _g_idx < ( _g_arr as global::System.Array ).Length )) )) {
					object attr = ((object) (_g_arr[((int) (_g_idx++) )]) );
					global::haxe.lang.GenericInterface g = ((global::haxe.lang.GenericInterface) (( attr as global::haxe.lang.GenericInterface )) );
					if (( g != null )) {
						return ((global::System.Type) (g.generic) );
					}
					
				}
				
			}
			
			return ((global::System.Type) (t) );
		}
		else {
			return ((global::System.Type) (t) );
		}
		
	}
	
	
	public static global::System.Type resolveEnum(string name) {
		if (string.Equals(name, "Bool")) {
			return typeof(bool);
		}
		
		global::System.Type t = global::TypeHaxe.resolveClass(name);
		if (( (  ! (global::haxe.lang.Runtime.typeEq(t, null))  && t.BaseType.Equals(((global::System.Type) (typeof(global::System.Enum)) )) ) || ((global::System.Type) (typeof(global::haxe.lang.Enum)) ).IsAssignableFrom(((global::System.Type) (t) )) )) {
			return t;
		}
		
		return null;
	}
	
	
	public static T createInstance<T>(global::System.Type cl, global::ArrayHaxe args) {
		if (global::haxe.lang.Runtime.refEq(cl, typeof(string))) {
			return global::haxe.lang.Runtime.genericCast<T>(args[0]);
		}
		
		global::System.Type t = ((global::System.Type) (cl) );
		if (t.IsInterface) {
			t = global::TypeHaxe.resolveClass(global::TypeHaxe.getClassName(cl));
		}
		
		global::System.Reflection.ConstructorInfo[] ctors = t.GetConstructors();
		return global::haxe.lang.Runtime.genericCast<T>(global::haxe.lang.Runtime.callMethod(null, ((global::System.Reflection.MethodBase[]) (ctors) ), ( ctors as global::System.Array ).Length, args));
	}
	
	
	protected static readonly object[] __createEmptyInstance_EMPTY_ARGS = new object[]{((object) (global::haxe.lang.EmptyObject.EMPTY) )};
	
	public static T createEmptyInstance<T>(global::System.Type cl) {
		global::System.Type t = ((global::System.Type) (cl) );
		if (global::System.Object.ReferenceEquals(((object) (t) ), ((object) (typeof(string)) ))) {
			return (T)(object)"";
		}
		
		object res = null;
		try {
			res = global::System.Activator.CreateInstance(((global::System.Type) (t) ), ((object[]) (global::TypeHaxe.__createEmptyInstance_EMPTY_ARGS) ));
		}
		catch (global::System.MissingMemberException _){
			global::haxe.lang.Exceptions.exception = _;
			res = global::System.Activator.CreateInstance(((global::System.Type) (t) ));
		}
		
		
		return (T)res;
	}
	
	
	public static T createEnum<T>(global::System.Type e, string constr, global::ArrayHaxe @params) {
		if (( ( @params == null ) || ( ((int) (global::haxe.lang.Runtime.getField_f(@params, "length", 520590566, true)) ) == 0 ) )) {
			T ret = global::haxe.lang.Runtime.genericCast<T>(global::haxe.lang.Runtime.slowGetField(e, constr, true));
			if (( ret is global::haxe.lang.Function )) {
				throw global::haxe.lang.HaxeException.wrap(global::haxe.lang.Runtime.concat(global::haxe.lang.Runtime.concat("Constructor ", constr), " needs parameters"));
			}
			
			return ret;
		}
		else {
			return global::haxe.lang.Runtime.genericCast<T>(global::haxe.lang.Runtime.slowCallField(e, constr, @params));
		}
		
	}
	
	
	public static T createEnumIndex<T>(global::System.Type e, int index, global::ArrayHaxe @params) {
		global::ArrayHaxe<object> constr = global::TypeHaxe.getEnumConstructs(e);
		return global::TypeHaxe.createEnum<T>(((global::System.Type) (e) ), global::haxe.lang.Runtime.toString(constr[index]), ((global::ArrayHaxe) (@params) ));
	}
	
	
	public static global::ArrayHaxe<object> getInstanceFields(global::System.Type c) {
		unchecked {
			if (global::haxe.lang.Runtime.refEq(c, typeof(string))) {
				return global::haxe.lang.StringRefl.fields;
			}
			
			global::System.Type c1 = ((global::System.Type) (c) );
			global::ArrayHaxe<object> ret = new global::ArrayHaxe<object>(new object[]{});
			global::System.Reflection.BindingFlags this1 = global::System.Reflection.BindingFlags.Public;
			global::System.Reflection.BindingFlags this2 = ((global::System.Reflection.BindingFlags) (( ((global::System.Reflection.BindingFlags) (this1) ) | ((global::System.Reflection.BindingFlags) (global::System.Reflection.BindingFlags.Instance) ) )) );
			global::System.Reflection.BindingFlags this3 = ((global::System.Reflection.BindingFlags) (( ((global::System.Reflection.BindingFlags) (this2) ) | ((global::System.Reflection.BindingFlags) (global::System.Reflection.BindingFlags.FlattenHierarchy) ) )) );
			global::System.Reflection.MemberInfo[] mis = c1.GetMembers(((global::System.Reflection.BindingFlags) (this3) ));
			{
				int _g1 = 0;
				int _g = ( mis as global::System.Array ).Length;
				while (( _g1 < _g )) {
					int i = _g1++;
					global::System.Reflection.MemberInfo i1 = mis[i];
					if (( i1 is global::System.Reflection.PropertyInfo )) {
						continue;
					}
					
					string n = i1.Name;
					if ((  ! (n.StartsWith("__hx_"))  && ( (( (((bool) (( ((uint) (0) ) < n.Length )) )) ? (((int) (n[0]) )) : (-1) )) != 46 ) )) {
						switch (n) {
							case "Equals":
							case "GetHashCode":
							case "GetType":
							case "ToString":
							{
								break;
							}
							
							
							default:
							{
								ret.push(n);
								break;
							}
							
						}
						
					}
					
				}
				
			}
			
			return ret;
		}
	}
	
	
	public static global::ArrayHaxe<object> getClassFields(global::System.Type c) {
		if (global::System.Object.ReferenceEquals(((object) (c) ), ((object) (typeof(string)) ))) {
			return new global::ArrayHaxe<object>(new object[]{"fromCharCode"});
		}
		
		global::ArrayHaxe<object> ret = new global::ArrayHaxe<object>(new object[]{});
		global::System.Reflection.BindingFlags this1 = global::System.Reflection.BindingFlags.Public;
		global::System.Reflection.BindingFlags this2 = ((global::System.Reflection.BindingFlags) (( ((global::System.Reflection.BindingFlags) (this1) ) | ((global::System.Reflection.BindingFlags) (global::System.Reflection.BindingFlags.Static) ) )) );
		global::System.Reflection.MemberInfo[] infos = ((global::System.Type) (c) ).GetMembers(((global::System.Reflection.BindingFlags) (this2) ));
		{
			int _g1 = 0;
			int _g = ( infos as global::System.Array ).Length;
			while (( _g1 < _g )) {
				int i = _g1++;
				string name = infos[i].Name;
				if ( ! (name.StartsWith("__hx_")) ) {
					ret.push(name);
				}
				
			}
			
		}
		
		return ret;
	}
	
	
	public static global::ArrayHaxe<object> getEnumConstructs(global::System.Type e) {
		if (global::ReflectHaxe.hasField(e, "__hx_constructs")) {
			global::ArrayHaxe<object> ret = new global::ArrayHaxe<object>(((object[]) (((string[]) (global::haxe.lang.Runtime.getField(e, "__hx_constructs", 1781145963, true)) )) ));
			return ret.copy();
		}
		
		return new global::ArrayHaxe<object>(((object[]) (global::System.Enum.GetNames(((global::System.Type) (e) ))) ));
	}
	
	
	public static global::ValueType @typeof(object v) {
		if (( v == null )) {
			return global::ValueType.TNull;
		}
		
		global::System.Type t = ((global::System.Type) (( v as global::System.Type )) );
		if ( ! (global::haxe.lang.Runtime.typeEq(t, null)) ) {
			return global::ValueType.TObject;
		}
		
		t = ((global::System.Type) (v.GetType()) );
		if (( t.IsEnum || ( v is global::haxe.lang.Enum ) )) {
			return global::ValueType.TEnum(((global::System.Type) (t) ));
		}
		
		if (t.IsValueType) {
			global::System.IConvertible vc = ((global::System.IConvertible) (v) );
			if (( vc != null )) {
				global::System.TypeCode _g = vc.GetTypeCode();
				switch (_g) {
					case global::System.TypeCode.Boolean:
					{
						return global::ValueType.TBool;
					}
					
					
					case global::System.TypeCode.Int32:
					{
						return global::ValueType.TInt;
					}
					
					
					case global::System.TypeCode.Double:
					{
						double d = vc.ToDouble(default(global::System.IFormatProvider));
						if (( ( ( d >= global::System.Int32.MinValue ) && ( d <= global::System.Int32.MaxValue ) ) && ( d == vc.ToInt32(default(global::System.IFormatProvider)) ) )) {
							return global::ValueType.TInt;
						}
						else {
							return global::ValueType.TFloat;
						}
						
					}
					
					
					default:
					{
						return global::ValueType.TClass(((global::System.Type) (t) ));
					}
					
				}
				
			}
			else {
				return global::ValueType.TClass(((global::System.Type) (t) ));
			}
			
		}
		
		if (( v is global::haxe.lang.IHxObject )) {
			if (( v is global::haxe.lang.DynamicObject )) {
				return global::ValueType.TObject;
			}
			else if (( v is global::haxe.lang.Enum )) {
				return global::ValueType.TEnum(((global::System.Type) (t) ));
			}
			
			return global::ValueType.TClass(((global::System.Type) (t) ));
		}
		else if (( v is global::haxe.lang.Function )) {
			return global::ValueType.TFunction;
		}
		else {
			return global::ValueType.TClass(((global::System.Type) (t) ));
		}
		
	}
	
	
	public static bool enumEq<T>(T a, T b) {
		if (global::haxe.lang.Runtime.eq(a, default(T))) {
			return global::haxe.lang.Runtime.eq(b, default(T));
		}
		else if (global::haxe.lang.Runtime.eq(b, default(T))) {
			return false;
		}
		else {
			return a.Equals(b);
		}
		
	}
	
	
	public static string enumConstructor(object e) {
		if (( e is global::System.Enum )) {
			return global::haxe.lang.Runtime.concat(global::Std.@string(e), "");
		}
		else {
			return (((global::haxe.lang.Enum) (e) )).getTag();
		}
		
	}
	
	
	public static global::ArrayHaxe enumParameters(object e) {
		if (( e is global::System.Enum )) {
			return new global::ArrayHaxe<object>(new object[]{});
		}
		else {
			return (((global::haxe.lang.Enum) (e) )).getParams();
		}
		
	}
	
	
	public static int enumIndex(object e) {
		if (( e is global::System.Enum )) {
			global::System.Array values = global::System.Enum.GetValues(((global::System.Type) (e.GetType()) ));
			return global::System.Array.IndexOf(((global::System.Array) (values) ), ((object) (e) ));
		}
		else {
			return (((global::haxe.lang.Enum) (e) )).index;
		}
		
	}
	
	
	public static global::ArrayHaxe<T> allEnums<T>(global::System.Type e) {
		global::ArrayHaxe<object> ctors = global::TypeHaxe.getEnumConstructs(e);
		global::ArrayHaxe<T> ret = new global::ArrayHaxe<T>(new T[]{});
		{
			int _g = 0;
			while (( _g < ctors.length )) {
				string ctor = global::haxe.lang.Runtime.toString(ctors[_g]);
				 ++ _g;
				T v = global::haxe.lang.Runtime.genericCast<T>(global::ReflectHaxe.field(e, ctor));
				if (global::Std.@is(v, e)) {
					ret.push(v);
				}
				
			}
			
		}
		
		return ret;
	}
	
	
}


