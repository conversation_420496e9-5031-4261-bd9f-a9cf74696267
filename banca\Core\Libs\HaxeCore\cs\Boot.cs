// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace cs {
	public class Boot : global::haxe.lang.HxObject {
		
		public Boot(global::haxe.lang.EmptyObject empty) {
		}
		
		
		public Boot() {
			global::cs.Boot.__hx_ctor_cs_Boot(this);
		}
		
		
		public static void __hx_ctor_cs_Boot(global::cs.<PERSON>ot __hx_this) {
		}
		
		
		public static void init() {
			global::cs.Lib.applyCultureChanges();
		}
		
		
	}
}


