// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace cs {
	public class Lib : global::haxe.lang.HxObject {
		
		public Lib(global::haxe.lang.EmptyObject empty) {
		}
		
		
		public Lib() {
			global::cs.Lib.__hx_ctor_cs_Lib(this);
		}
		
		
		public static void __hx_ctor_cs_Lib(global::cs.Lib __hx_this) {
		}
		
		
		public static string decimalSeparator;
		
		public static void applyCultureChanges() {
			global::System.Globalization.CultureInfo ci = new global::System.Globalization.CultureInfo(((string) (global::System.Threading.Thread.CurrentThread.CurrentCulture.Name) ), ((bool) (true) ));
			global::cs.Lib.decimalSeparator = ci.NumberFormat.NumberDecimalSeparator;
			ci.NumberFormat.NumberDecimalSeparator = ((string) (".") );
			global::System.Threading.Thread.CurrentThread.CurrentCulture = ((global::System.Globalization.CultureInfo) (ci) );
		}
		
		
		public static void revertDefaultCulture() {
			global::System.Globalization.CultureInfo ci = new global::System.Globalization.CultureInfo(((string) (global::System.Threading.Thread.CurrentThread.CurrentCulture.Name) ), ((bool) (true) ));
			global::System.Threading.Thread.CurrentThread.CurrentCulture = ((global::System.Globalization.CultureInfo) (ci) );
		}
		
		
		public static void p_nativeArray<T>(global::ArrayHaxe<T> arr, global::System.Array ret) {
			T[] native = arr.__a;
			int len = arr.length;
			global::System.Array.Copy(((global::System.Array) (native) ), ((int) (0) ), ((global::System.Array) (ret) ), ((int) (0) ), ((int) (len) ));
		}
		
		
		public static global::System.Type fromNativeType(global::System.Type t) {
			return t;
		}
		
		
		public static global::System.Type toNativeType(global::System.Type cl) {
			return cl;
		}
		
		
		public static global::System.Type toNativeEnum(global::System.Type cl) {
			return cl;
		}
		
		
		public static global::System.Type nativeType(object obj) {
			return obj.GetType();
		}
		
		
		public static global::System.Type getNativeType(object obj) {
			return obj.GetType();
		}
		
		
		public static global::ArrayHaxe<T> array<T>(T[] native) {
			return new global::ArrayHaxe<T>(((T[]) (native) ));
		}
		
		
		public static global::ArrayHaxe<T> arrayAlloc<T>(int size) {
			return new global::ArrayHaxe<T>(((T[]) (new T[size]) ));
		}
		
		
	}
}


