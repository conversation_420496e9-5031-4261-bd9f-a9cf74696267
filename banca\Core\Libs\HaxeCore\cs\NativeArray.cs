// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace cs._NativeArray {
	public class NativeArrayIterator<T> : global::haxe.lang.HxObject, global::cs._NativeArray.NativeArrayIterator {
		
		public NativeArrayIterator(global::haxe.lang.EmptyObject empty) {
		}
		
		
		public NativeArrayIterator(T[] arr) {
			global::cs._NativeArray.NativeArrayIterator<object>.__hx_ctor_cs__NativeArray_NativeArrayIterator<T>(((global::cs._NativeArray.NativeArrayIterator<T>) (this) ), ((T[]) (arr) ));
		}
		
		
		public static void __hx_ctor_cs__NativeArray_NativeArrayIterator<T_c>(global::cs._NativeArray.NativeArrayIterator<T_c> __hx_this, T_c[] arr) {
			__hx_this.arr = arr;
			__hx_this.idx = ((uint) (0) );
		}
		
		
		public static object __hx_cast<T_c_c>(global::cs._NativeArray.NativeArrayIterator me) {
			return ( (( me != null )) ? (me.cs__NativeArray_NativeArrayIterator_cast<T_c_c>()) : default(object) );
		}
		
		
		public virtual object cs__NativeArray_NativeArrayIterator_cast<T_c>() {
			unchecked {
				if (global::haxe.lang.Runtime.eq(typeof(T), typeof(T_c))) {
					return this;
				}
				
				global::cs._NativeArray.NativeArrayIterator<T_c> new_me = new global::cs._NativeArray.NativeArrayIterator<T_c>(((global::haxe.lang.EmptyObject) (global::haxe.lang.EmptyObject.EMPTY) ));
				global::ArrayHaxe<object> fields = global::ReflectHaxe.fields(this);
				int i = 0;
				while (( i < fields.length )) {
					string field = global::haxe.lang.Runtime.toString(fields[i++]);
					switch (field) {
						case "arr":
						{
							if (( this.arr != null )) {
								T_c[] __temp_new_arr5 = new T_c[this.arr.Length];
								int __temp_i6 = -1;
								while ((  ++ __temp_i6 < this.arr.Length )) {
									object __temp_obj7 = ((object) (this.arr[__temp_i6]) );
									if (( __temp_obj7 != null )) {
										__temp_new_arr5[__temp_i6] = global::haxe.lang.Runtime.genericCast<T_c>(__temp_obj7);
									}
									
								}
								
								new_me.arr = __temp_new_arr5;
							}
							else {
								new_me.arr = null;
							}
							
							break;
						}
						
						
						default:
						{
							global::ReflectHaxe.setField(new_me, field, global::ReflectHaxe.field(this, field));
							break;
						}
						
					}
					
				}
				
				return new_me;
			}
		}
		
		
		public T[] arr;
		
		public uint idx;
		
		public bool hasNext() {
			return ((bool) (( this.idx < ( this.arr as global::System.Array ).Length )) );
		}
		
		
		public T next() {
			return this.arr[((int) (this.idx++) )];
		}
		
		
		public override double __hx_setField_f(string field, int hash, double @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 5243965:
					{
						this.idx = ((uint) (@value) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField_f(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_setField(string field, int hash, object @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 5243965:
					{
						this.idx = ((uint) (global::haxe.lang.Runtime.toInt(@value)) );
						return @value;
					}
					
					
					case 4849249:
					{
						this.arr = ((T[]) (@value) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_getField(string field, int hash, bool throwErrors, bool isCheck, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1224901875:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "next", 1224901875)) );
					}
					
					
					case 407283053:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "hasNext", 407283053)) );
					}
					
					
					case 5243965:
					{
						return this.idx;
					}
					
					
					case 4849249:
					{
						return this.arr;
					}
					
					
					default:
					{
						return base.__hx_getField(field, hash, throwErrors, isCheck, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override double __hx_getField_f(string field, int hash, bool throwErrors, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 5243965:
					{
						return ((double) (this.idx) );
					}
					
					
					default:
					{
						return base.__hx_getField_f(field, hash, throwErrors, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_invokeField(string field, int hash, global::ArrayHaxe dynargs) {
			unchecked {
				switch (hash) {
					case 1224901875:
					{
						return this.next();
					}
					
					
					case 407283053:
					{
						return this.hasNext();
					}
					
					
					default:
					{
						return base.__hx_invokeField(field, hash, dynargs);
					}
					
				}
				
			}
		}
		
		
		public override void __hx_getFields(global::ArrayHaxe<object> baseArr) {
			baseArr.push("idx");
			baseArr.push("arr");
			base.__hx_getFields(baseArr);
		}
		
		
	}
}



#pragma warning disable 109, 114, 219, 429, 168, 162
namespace cs._NativeArray {
	[global::haxe.lang.GenericInterface(typeof(global::cs._NativeArray.NativeArrayIterator<object>))]
	public interface NativeArrayIterator : global::haxe.lang.IHxObject, global::haxe.lang.IGenericObject {
		
		object cs__NativeArray_NativeArrayIterator_cast<T_c>();
		
		bool hasNext();
		
	}
}


