// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace haxe._Int32 {
	public sealed class Int32_Impl_ {
		
		public static int preIncrement(int this1) {
			this1 =  ++ this1;
			return this1;
		}
		
		
		public static int postIncrement(int this1) {
			int ret = this1++;
			return ret;
		}
		
		
		public static int preDecrement(int this1) {
			this1 =  -- this1;
			return this1;
		}
		
		
		public static int postDecrement(int this1) {
			int ret = this1--;
			return ret;
		}
		
		
		public static int @add(int a, int b) {
			return ( ((int) (a) ) + ((int) (b) ) );
		}
		
		
		public static int addInt(int a, int b) {
			return ( ((int) (a) ) + ((int) (b) ) );
		}
		
		
		public static int sub(int a, int b) {
			return ( ((int) (a) ) - ((int) (b) ) );
		}
		
		
		public static int subInt(int a, int b) {
			return ( ((int) (a) ) - ((int) (b) ) );
		}
		
		
		public static int intSub(int a, int b) {
			return ( ((int) (a) ) - ((int) (b) ) );
		}
		
		
		public static double toFloat(int this1) {
			return ((double) (this1) );
		}
		
		
		public static int ucompare(int a, int b) {
			unchecked {
				if (((bool) (( a < 0 )) )) {
					if (((bool) (( b < 0 )) )) {
						return ((int) (( ((int) ( ~ (b) ) ) - ((int) ( ~ (a) ) ) )) );
					}
					else {
						return 1;
					}
					
				}
				
				if (((bool) (( b < 0 )) )) {
					return -1;
				}
				else {
					return ((int) (( ((int) (a) ) - ((int) (b) ) )) );
				}
				
			}
		}
		
		
		public static int clamp(int x) {
			return x;
		}
		
		
	}
}


