// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace haxe.ds._HashMap {
	public sealed class HashMap_Impl_ {
		
		public static global::haxe.ds._HashMap.HashMapData<K, V> _new<K, V>() {
			global::haxe.ds._HashMap.HashMapData<K, V> this1 = new global::haxe.ds._HashMap.HashMapData<K, V>();
			return ((global::haxe.ds._HashMap.HashMapData<K, V>) (this1) );
		}
		
		
		public static void @set<K, V>(global::haxe.ds._HashMap.HashMapData<K, V> this1, K k, V v) {
			this1.keys.@set(((int) (global::haxe.lang.Runtime.toInt(global::haxe.lang.Runtime.callField(k, "hashCode", 125111323, null))) ), k);
			this1.values.@set(((int) (global::haxe.lang.Runtime.toInt(global::haxe.lang.Runtime.callField(k, "hashCode", 125111323, null))) ), v);
		}
		
		
		public static global::haxe.lang.Null<V> @get<K, V>(global::haxe.ds._HashMap.HashMapData<K, V> this1, K k) {
			return this1.values.@get(((int) (global::haxe.lang.Runtime.toInt(global::haxe.lang.Runtime.callField(k, "hashCode", 125111323, null))) ));
		}
		
		
		public static bool exists<K, V>(global::haxe.ds._HashMap.HashMapData<K, V> this1, K k) {
			return this1.values.exists(((int) (global::haxe.lang.Runtime.toInt(global::haxe.lang.Runtime.callField(k, "hashCode", 125111323, null))) ));
		}
		
		
		public static bool @remove<K, V>(global::haxe.ds._HashMap.HashMapData<K, V> this1, K k) {
			this1.values.@remove(((int) (global::haxe.lang.Runtime.toInt(global::haxe.lang.Runtime.callField(k, "hashCode", 125111323, null))) ));
			return this1.keys.@remove(((int) (global::haxe.lang.Runtime.toInt(global::haxe.lang.Runtime.callField(k, "hashCode", 125111323, null))) ));
		}
		
		
		public static object keys<K, V>(global::haxe.ds._HashMap.HashMapData<K, V> this1) {
			return new global::haxe.ds._IntMap.IntMapValueIterator<K>(((global::haxe.ds.IntMap<K>) (this1.keys) ));
		}
		
		
		public static object iterator<K, V>(global::haxe.ds._HashMap.HashMapData<K, V> this1) {
			return new global::haxe.ds._IntMap.IntMapValueIterator<V>(((global::haxe.ds.IntMap<V>) (this1.values) ));
		}
		
		
	}
}



#pragma warning disable 109, 114, 219, 429, 168, 162
namespace haxe.ds._HashMap {
	public class HashMapData<K, V> : global::haxe.lang.HxObject, global::haxe.ds._HashMap.HashMapData {
		
		public HashMapData(global::haxe.lang.EmptyObject empty) {
		}
		
		
		public HashMapData() {
			global::haxe.ds._HashMap.HashMapData<object, object>.__hx_ctor_haxe_ds__HashMap_HashMapData<K, V>(((global::haxe.ds._HashMap.HashMapData<K, V>) (this) ));
		}
		
		
		public static void __hx_ctor_haxe_ds__HashMap_HashMapData<K_c, V_c>(global::haxe.ds._HashMap.HashMapData<K_c, V_c> __hx_this) {
			__hx_this.keys = new global::haxe.ds.IntMap<K_c>();
			__hx_this.values = new global::haxe.ds.IntMap<V_c>();
		}
		
		
		public static object __hx_cast<K_c_c, V_c_c>(global::haxe.ds._HashMap.HashMapData me) {
			return ( (( me != null )) ? (me.haxe_ds__HashMap_HashMapData_cast<K_c_c, V_c_c>()) : default(object) );
		}
		
		
		public virtual object haxe_ds__HashMap_HashMapData_cast<K_c, V_c>() {
			if (( global::haxe.lang.Runtime.eq(typeof(K), typeof(K_c)) && global::haxe.lang.Runtime.eq(typeof(V), typeof(V_c)) )) {
				return this;
			}
			
			global::haxe.ds._HashMap.HashMapData<K_c, V_c> new_me = new global::haxe.ds._HashMap.HashMapData<K_c, V_c>(global::haxe.lang.EmptyObject.EMPTY);
			global::ArrayHaxe<object> fields = global::ReflectHaxe.fields(this);
			int i = 0;
			while (( i < fields.length )) {
				string field = global::haxe.lang.Runtime.toString(fields[i++]);
				global::ReflectHaxe.setField(new_me, field, global::ReflectHaxe.field(this, field));
			}
			
			return new_me;
		}
		
		
		public global::haxe.ds.IntMap<K> keys;
		
		public global::haxe.ds.IntMap<V> values;
		
		public override object __hx_setField(string field, int hash, object @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1337394146:
					{
						this.values = ((global::haxe.ds.IntMap<V>) (global::haxe.ds.IntMap<object>.__hx_cast<V>(((global::haxe.ds.IntMap) (@value) ))) );
						return @value;
					}
					
					
					case 1191633396:
					{
						this.keys = ((global::haxe.ds.IntMap<K>) (global::haxe.ds.IntMap<object>.__hx_cast<K>(((global::haxe.ds.IntMap) (@value) ))) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_getField(string field, int hash, bool throwErrors, bool isCheck, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1337394146:
					{
						return this.values;
					}
					
					
					case 1191633396:
					{
						return this.keys;
					}
					
					
					default:
					{
						return base.__hx_getField(field, hash, throwErrors, isCheck, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override void __hx_getFields(global::ArrayHaxe<object> baseArr) {
			baseArr.push("values");
			baseArr.push("keys");
			base.__hx_getFields(baseArr);
		}
		
		
	}
}



#pragma warning disable 109, 114, 219, 429, 168, 162
namespace haxe.ds._HashMap {
	[global::haxe.lang.GenericInterface(typeof(global::haxe.ds._HashMap.HashMapData<object, object>))]
	public interface HashMapData : global::haxe.lang.IHxObject, global::haxe.lang.IGenericObject {
		
		object haxe_ds__HashMap_HashMapData_cast<K_c, V_c>();
		
	}
}


