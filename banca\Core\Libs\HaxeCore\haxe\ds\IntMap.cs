// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace haxe.ds {
	public class IntMap<T> : global::haxe.lang.HxObject, global::haxe.ds.IntMap, global::haxe.IMap<int, T> {
		
		static IntMap() {
			global::haxe.ds.IntMap<object>.HASH_UPPER = 0.7;
		}
		
		
		public IntMap(global::haxe.lang.EmptyObject empty) {
		}
		
		
		public IntMap() {
			global::haxe.ds.IntMap<object>.__hx_ctor_haxe_ds_IntMap<T>(((global::haxe.ds.IntMap<T>) (this) ));
		}
		
		
		public static void __hx_ctor_haxe_ds_IntMap<T_c>(global::haxe.ds.IntMap<T_c> __hx_this) {
			unchecked {
				__hx_this.cachedIndex = -1;
			}
		}
		
		
		public static object __hx_cast<T_c_c>(global::haxe.ds.IntMap me) {
			return ( (( me != null )) ? (me.haxe_ds_IntMap_cast<T_c_c>()) : default(object) );
		}
		
		
		public static double HASH_UPPER;
		
		public static void assert(bool x) {
		}
		
		
		public static int defaultK() {
			return 0;
		}
		
		
		public static void arrayCopy(global::System.Array sourceArray, int sourceIndex, global::System.Array destinationArray, int destinationIndex, int length) {
			global::System.Array.Copy(((global::System.Array) (sourceArray) ), ((int) (sourceIndex) ), ((global::System.Array) (destinationArray) ), ((int) (destinationIndex) ), ((int) (length) ));
		}
		
		
		public static int getInc(int k, int mask) {
			unchecked {
				return ( (( ( ( k >> 3 ) ^ ( k << 3 ) ) | 1 )) & mask );
			}
		}
		
		
		public static int hash(int i) {
			return i;
		}
		
		
		public static bool flagIsEmpty(int[] flag, int i) {
			unchecked {
				return ( (( ((int) (( ((uint) (flag[( i >> 4 )]) ) >> (( (( i & 15 )) << 1 )) )) ) & 2 )) != 0 );
			}
		}
		
		
		public static bool flagIsDel(int[] flag, int i) {
			unchecked {
				return ( (( ((int) (( ((uint) (flag[( i >> 4 )]) ) >> (( (( i & 15 )) << 1 )) )) ) & 1 )) != 0 );
			}
		}
		
		
		public static bool isEither(int[] flag, int i) {
			unchecked {
				return ( (( ((int) (( ((uint) (flag[( i >> 4 )]) ) >> (( (( i & 15 )) << 1 )) )) ) & 3 )) != 0 );
			}
		}
		
		
		public static void setIsDelFalse(int[] flag, int i) {
			unchecked {
				flag[( i >> 4 )] &=  ~ ((( 1 << (( (( i & 15 )) << 1 )) ))) ;
			}
		}
		
		
		public static void setIsEmptyFalse(int[] flag, int i) {
			unchecked {
				flag[( i >> 4 )] &=  ~ ((( 2 << (( (( i & 15 )) << 1 )) ))) ;
			}
		}
		
		
		public static void setIsBothFalse(int[] flag, int i) {
			unchecked {
				flag[( i >> 4 )] &=  ~ ((( 3 << (( (( i & 15 )) << 1 )) ))) ;
			}
		}
		
		
		public static void setIsDelTrue(int[] flag, int i) {
			unchecked {
				flag[( i >> 4 )] |= ( 1 << (( (( i & 15 )) << 1 )) );
			}
		}
		
		
		public static int roundUp(int x) {
			unchecked {
				 -- x;
				x |= ((int) (( ((uint) (x) ) >> 1 )) );
				x |= ((int) (( ((uint) (x) ) >> 2 )) );
				x |= ((int) (( ((uint) (x) ) >> 4 )) );
				x |= ((int) (( ((uint) (x) ) >> 8 )) );
				x |= ((int) (( ((uint) (x) ) >> 16 )) );
				return  ++ x;
			}
		}
		
		
		public static int flagsSize(int m) {
			unchecked {
				if (( m < 16 )) {
					return 1;
				}
				else {
					return ( m >> 4 );
				}
				
			}
		}
		
		
		public virtual object haxe_ds_IntMap_cast<T_c>() {
			unchecked {
				if (global::haxe.lang.Runtime.eq(typeof(T), typeof(T_c))) {
					return this;
				}
				
				global::haxe.ds.IntMap<T_c> new_me = new global::haxe.ds.IntMap<T_c>(global::haxe.lang.EmptyObject.EMPTY);
				global::ArrayHaxe<object> fields = global::ReflectHaxe.fields(this);
				int i = 0;
				while (( i < fields.length )) {
					string field = global::haxe.lang.Runtime.toString(fields[i++]);
					switch (field) {
						case "vals":
						{
							if (( this.vals != null )) {
								T_c[] __temp_new_arr32 = new T_c[this.vals.Length];
								int __temp_i33 = -1;
								while ((  ++ __temp_i33 < this.vals.Length )) {
									object __temp_obj34 = ((object) (this.vals[__temp_i33]) );
									if (( __temp_obj34 != null )) {
										__temp_new_arr32[__temp_i33] = global::haxe.lang.Runtime.genericCast<T_c>(__temp_obj34);
									}
									
								}
								
								new_me.vals = __temp_new_arr32;
							}
							else {
								new_me.vals = null;
							}
							
							break;
						}
						
						
						default:
						{
							global::ReflectHaxe.setField(new_me, field, global::ReflectHaxe.field(this, field));
							break;
						}
						
					}
					
				}
				
				return new_me;
			}
		}
		
		
		public virtual object haxe_IMap_cast<K_c, V_c>() {
			return this.haxe_ds_IntMap_cast<V_c>();
		}
		
		
		public int[] flags;
		
		public int[] _keys;
		
		public T[] vals;
		
		public int nBuckets;
		
		public int size;
		
		public int nOccupied;
		
		public int upperBound;
		
		public int cachedKey;
		
		public int cachedIndex;
		
		public virtual void @set(int key, T @value) {
			unchecked {
				int x = default(int);
				if (( this.nOccupied >= this.upperBound )) {
					if (( this.nBuckets > ( this.size << 1 ) )) {
						this.resize(( this.nBuckets - 1 ));
					}
					else {
						this.resize(( this.nBuckets + 1 ));
					}
					
				}
				
				int[] flags = this.flags;
				int[] _keys = this._keys;
				{
					int mask = ( this.nBuckets - 1 );
					x = this.nBuckets;
					int site = x;
					int k = key;
					int i = ( k & mask );
					int delKey = -1;
					if (( (( ((int) (( ((uint) (flags[( i >> 4 )]) ) >> (( (( i & 15 )) << 1 )) )) ) & 2 )) != 0 )) {
						x = i;
					}
					else {
						int inc = ( (( ( ( k >> 3 ) ^ ( k << 3 ) ) | 1 )) & mask );
						int last = i;
						while ( ! ((( ( (( ((int) (( ((uint) (flags[( i >> 4 )]) ) >> (( (( i & 15 )) << 1 )) )) ) & 2 )) != 0 ) || ( _keys[i] == key ) ))) ) {
							if (( ( delKey == -1 ) && ( (( ((int) (( ((uint) (flags[( i >> 4 )]) ) >> (( (( i & 15 )) << 1 )) )) ) & 1 )) != 0 ) )) {
								delKey = i;
							}
							
							i = ( ( i + inc ) & mask );
						}
						
						if (( ( (( ((int) (( ((uint) (flags[( i >> 4 )]) ) >> (( (( i & 15 )) << 1 )) )) ) & 2 )) != 0 ) && ( delKey != -1 ) )) {
							x = delKey;
						}
						else {
							x = i;
						}
						
					}
					
				}
				
				if (( (( ((int) (( ((uint) (flags[( x >> 4 )]) ) >> (( (( x & 15 )) << 1 )) )) ) & 2 )) != 0 )) {
					_keys[x] = key;
					this.vals[x] = @value;
					flags[( x >> 4 )] &=  ~ ((( 3 << (( (( x & 15 )) << 1 )) ))) ;
					this.size++;
					this.nOccupied++;
				}
				else if (( (( ((int) (( ((uint) (flags[( x >> 4 )]) ) >> (( (( x & 15 )) << 1 )) )) ) & 1 )) != 0 )) {
					_keys[x] = key;
					this.vals[x] = @value;
					flags[( x >> 4 )] &=  ~ ((( 3 << (( (( x & 15 )) << 1 )) ))) ;
					this.size++;
				}
				else {
					this.vals[x] = @value;
				}
				
			}
		}
		
		
		public int lookup(int key) {
			unchecked {
				if (( this.nBuckets != 0 )) {
					int[] flags = this.flags;
					int[] _keys = this._keys;
					int mask = ( this.nBuckets - 1 );
					int k = key;
					int i = ( k & mask );
					int inc = ( (( ( ( k >> 3 ) ^ ( k << 3 ) ) | 1 )) & mask );
					int last = i;
					while (( ( (( ((int) (( ((uint) (flags[( i >> 4 )]) ) >> (( (( i & 15 )) << 1 )) )) ) & 2 )) == 0 ) && (( ( (( ((int) (( ((uint) (flags[( i >> 4 )]) ) >> (( (( i & 15 )) << 1 )) )) ) & 1 )) != 0 ) || ( _keys[i] != key ) )) )) {
						i = ( ( i + inc ) & mask );
						if (( i == last )) {
							return -1;
						}
						
					}
					
					if (( (( ((int) (( ((uint) (flags[( i >> 4 )]) ) >> (( (( i & 15 )) << 1 )) )) ) & 3 )) != 0 )) {
						return -1;
					}
					else {
						return i;
					}
					
				}
				
				return -1;
			}
		}
		
		
		public virtual global::haxe.lang.Null<T> @get(int key) {
			unchecked {
				int idx = -1;
				bool tmp = default(bool);
				if (( this.cachedKey == key )) {
					idx = this.cachedIndex;
					tmp = ( idx != -1 );
				}
				else {
					tmp = false;
				}
				
				if (tmp) {
					return new global::haxe.lang.Null<T>(this.vals[idx], true);
				}
				
				idx = this.lookup(key);
				if (( idx != -1 )) {
					this.cachedKey = key;
					this.cachedIndex = idx;
					return new global::haxe.lang.Null<T>(this.vals[idx], true);
				}
				
				return default(global::haxe.lang.Null<T>);
			}
		}
		
		
		public virtual T getDefault(int key, T def) {
			unchecked {
				int idx = -1;
				bool tmp = default(bool);
				if (( this.cachedKey == key )) {
					idx = this.cachedIndex;
					tmp = ( idx != -1 );
				}
				else {
					tmp = false;
				}
				
				if (tmp) {
					return this.vals[idx];
				}
				
				idx = this.lookup(key);
				if (( idx != -1 )) {
					this.cachedKey = key;
					this.cachedIndex = idx;
					return this.vals[idx];
				}
				
				return def;
			}
		}
		
		
		public virtual bool exists(int key) {
			unchecked {
				int idx = -1;
				bool tmp = default(bool);
				if (( this.cachedKey == key )) {
					idx = this.cachedIndex;
					tmp = ( idx != -1 );
				}
				else {
					tmp = false;
				}
				
				if (tmp) {
					return true;
				}
				
				idx = this.lookup(key);
				if (( idx != -1 )) {
					this.cachedKey = key;
					this.cachedIndex = idx;
					return true;
				}
				
				return false;
			}
		}
		
		
		public virtual bool @remove(int key) {
			unchecked {
				int idx = -1;
				bool tmp = default(bool);
				if (( this.cachedKey == key )) {
					idx = this.cachedIndex;
					tmp = ( idx != -1 );
				}
				else {
					tmp = false;
				}
				
				if ( ! (tmp) ) {
					idx = this.lookup(key);
				}
				
				if (( idx == -1 )) {
					return false;
				}
				else {
					if (( this.cachedKey == key )) {
						this.cachedIndex = -1;
					}
					
					if (( (( ((int) (( ((uint) (this.flags[( idx >> 4 )]) ) >> (( (( idx & 15 )) << 1 )) )) ) & 3 )) == 0 )) {
						this.flags[( idx >> 4 )] |= ( 1 << (( (( idx & 15 )) << 1 )) );
						 -- this.size;
						this.vals[idx] = default(T);
						this._keys[idx] = 0;
					}
					
					return true;
				}
				
			}
		}
		
		
		public void resize(int newNBuckets) {
			unchecked {
				int[] newFlags = null;
				int j = 1;
				{
					int x = newNBuckets;
					 -- x;
					x |= ((int) (( ((uint) (x) ) >> 1 )) );
					x |= ((int) (( ((uint) (x) ) >> 2 )) );
					x |= ((int) (( ((uint) (x) ) >> 4 )) );
					x |= ((int) (( ((uint) (x) ) >> 8 )) );
					x |= ((int) (( ((uint) (x) ) >> 16 )) );
					newNBuckets =  ++ x;
					if (( newNBuckets < 4 )) {
						newNBuckets = 4;
					}
					
					if (( this.size >= ( ( newNBuckets * 0.7 ) + 0.5 ) )) {
						j = 0;
					}
					else {
						int nfSize = ( (( newNBuckets < 16 )) ? (1) : (( newNBuckets >> 4 )) );
						newFlags = new int[nfSize];
						{
							int _g1 = 0;
							int _g = nfSize;
							while (( _g1 < _g )) {
								int i = _g1++;
								newFlags[i] = -1431655766;
							}
							
						}
						
						if (( this.nBuckets < newNBuckets )) {
							int[] k = new int[newNBuckets];
							if (( this._keys != null )) {
								global::System.Array.Copy(((global::System.Array) (this._keys) ), ((int) (0) ), ((global::System.Array) (k) ), ((int) (0) ), ((int) (this.nBuckets) ));
							}
							
							this._keys = k;
							T[] v = new T[newNBuckets];
							if (( this.vals != null )) {
								global::System.Array.Copy(((global::System.Array) (this.vals) ), ((int) (0) ), ((global::System.Array) (v) ), ((int) (0) ), ((int) (this.nBuckets) ));
							}
							
							this.vals = v;
						}
						
					}
					
				}
				
				if (( j != 0 )) {
					this.cachedKey = 0;
					this.cachedIndex = -1;
					j = -1;
					int nBuckets = this.nBuckets;
					int[] _keys = this._keys;
					T[] vals = this.vals;
					int[] flags = this.flags;
					int newMask = ( newNBuckets - 1 );
					while ((  ++ j < nBuckets )) {
						if (( (( ((int) (( ((uint) (flags[( j >> 4 )]) ) >> (( (( j & 15 )) << 1 )) )) ) & 3 )) == 0 )) {
							int key = _keys[j];
							T val = vals[j];
							flags[( j >> 4 )] |= ( 1 << (( (( j & 15 )) << 1 )) );
							while (true) {
								int k1 = key;
								int inc = ( (( ( ( k1 >> 3 ) ^ ( k1 << 3 ) ) | 1 )) & newMask );
								int i1 = ( k1 & newMask );
								while (( (( ((int) (( ((uint) (newFlags[( i1 >> 4 )]) ) >> (( (( i1 & 15 )) << 1 )) )) ) & 2 )) == 0 )) {
									i1 = ( ( i1 + inc ) & newMask );
								}
								
								newFlags[( i1 >> 4 )] &=  ~ ((( 2 << (( (( i1 & 15 )) << 1 )) ))) ;
								if (( ( i1 < nBuckets ) && ( (( ((int) (( ((uint) (flags[( i1 >> 4 )]) ) >> (( (( i1 & 15 )) << 1 )) )) ) & 3 )) == 0 ) )) {
									{
										int tmp = _keys[i1];
										_keys[i1] = key;
										key = tmp;
									}
									
									{
										T tmp1 = vals[i1];
										vals[i1] = val;
										val = tmp1;
									}
									
									flags[( i1 >> 4 )] |= ( 1 << (( (( i1 & 15 )) << 1 )) );
								}
								else {
									_keys[i1] = key;
									vals[i1] = val;
									break;
								}
								
							}
							
						}
						
					}
					
					if (( nBuckets > newNBuckets )) {
						{
							int[] k2 = new int[newNBuckets];
							global::System.Array.Copy(((global::System.Array) (_keys) ), ((int) (0) ), ((global::System.Array) (k2) ), ((int) (0) ), ((int) (newNBuckets) ));
							this._keys = k2;
						}
						
						{
							T[] v1 = new T[newNBuckets];
							global::System.Array.Copy(((global::System.Array) (vals) ), ((int) (0) ), ((global::System.Array) (v1) ), ((int) (0) ), ((int) (newNBuckets) ));
							this.vals = v1;
						}
						
					}
					
					this.flags = newFlags;
					this.nBuckets = newNBuckets;
					this.nOccupied = this.size;
					this.upperBound = ((int) (( ( newNBuckets * 0.7 ) + .5 )) );
				}
				
			}
		}
		
		
		public object keys() {
			return new global::haxe.ds._IntMap.IntMapKeyIterator<T>(((global::haxe.ds.IntMap<T>) (this) ));
		}
		
		
		public object iterator() {
			return new global::haxe.ds._IntMap.IntMapValueIterator<T>(((global::haxe.ds.IntMap<T>) (this) ));
		}
		
		
		public virtual string toString() {
			global::System.Text.StringBuilder s_b = new global::System.Text.StringBuilder();
			s_b.Append(((string) ("{") ));
			object it = ((object) (new global::haxe.ds._IntMap.IntMapKeyIterator<T>(((global::haxe.ds.IntMap<T>) (this) ))) );
			{
				object i = it;
				while (global::haxe.lang.Runtime.toBool(global::haxe.lang.Runtime.callField(i, "hasNext", 407283053, null))) {
					int i1 = ((int) (global::haxe.lang.Runtime.toInt(global::haxe.lang.Runtime.callField(i, "next", 1224901875, null))) );
					s_b.Append(((string) (global::Std.@string(i1)) ));
					s_b.Append(((string) (" => ") ));
					s_b.Append(((string) (global::Std.@string(global::Std.@string((this.@get(i1)).toDynamic()))) ));
					if (global::haxe.lang.Runtime.toBool(global::haxe.lang.Runtime.callField(it, "hasNext", 407283053, null))) {
						s_b.Append(((string) (", ") ));
					}
					
				}
				
			}
			
			s_b.Append(((string) ("}") ));
			return s_b.ToString();
		}
		
		
		public override double __hx_setField_f(string field, int hash, double @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 922671056:
					{
						this.cachedIndex = ((int) (@value) );
						return @value;
					}
					
					
					case 1395555037:
					{
						this.cachedKey = ((int) (@value) );
						return @value;
					}
					
					
					case 2022294396:
					{
						this.upperBound = ((int) (@value) );
						return @value;
					}
					
					
					case 480756972:
					{
						this.nOccupied = ((int) (@value) );
						return @value;
					}
					
					
					case 1280549057:
					{
						this.size = ((int) (@value) );
						return @value;
					}
					
					
					case 1537812987:
					{
						this.nBuckets = ((int) (@value) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField_f(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_setField(string field, int hash, object @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 922671056:
					{
						this.cachedIndex = ((int) (global::haxe.lang.Runtime.toInt(@value)) );
						return @value;
					}
					
					
					case 1395555037:
					{
						this.cachedKey = ((int) (global::haxe.lang.Runtime.toInt(@value)) );
						return @value;
					}
					
					
					case 2022294396:
					{
						this.upperBound = ((int) (global::haxe.lang.Runtime.toInt(@value)) );
						return @value;
					}
					
					
					case 480756972:
					{
						this.nOccupied = ((int) (global::haxe.lang.Runtime.toInt(@value)) );
						return @value;
					}
					
					
					case 1280549057:
					{
						this.size = ((int) (global::haxe.lang.Runtime.toInt(@value)) );
						return @value;
					}
					
					
					case 1537812987:
					{
						this.nBuckets = ((int) (global::haxe.lang.Runtime.toInt(@value)) );
						return @value;
					}
					
					
					case 1313416818:
					{
						this.vals = ((T[]) (@value) );
						return @value;
					}
					
					
					case 2048392659:
					{
						this._keys = ((int[]) (@value) );
						return @value;
					}
					
					
					case 42740551:
					{
						this.flags = ((int[]) (@value) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_getField(string field, int hash, bool throwErrors, bool isCheck, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 946786476:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "toString", 946786476)) );
					}
					
					
					case 328878574:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "iterator", 328878574)) );
					}
					
					
					case 1191633396:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "keys", 1191633396)) );
					}
					
					
					case 142301684:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "resize", 142301684)) );
					}
					
					
					case 76061764:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "remove", 76061764)) );
					}
					
					
					case 1071652316:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "exists", 1071652316)) );
					}
					
					
					case 1797611211:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "getDefault", 1797611211)) );
					}
					
					
					case 5144726:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "get", 5144726)) );
					}
					
					
					case 1639293562:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "lookup", 1639293562)) );
					}
					
					
					case 5741474:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "set", 5741474)) );
					}
					
					
					case 922671056:
					{
						return this.cachedIndex;
					}
					
					
					case 1395555037:
					{
						return this.cachedKey;
					}
					
					
					case 2022294396:
					{
						return this.upperBound;
					}
					
					
					case 480756972:
					{
						return this.nOccupied;
					}
					
					
					case 1280549057:
					{
						return this.size;
					}
					
					
					case 1537812987:
					{
						return this.nBuckets;
					}
					
					
					case 1313416818:
					{
						return this.vals;
					}
					
					
					case 2048392659:
					{
						return this._keys;
					}
					
					
					case 42740551:
					{
						return this.flags;
					}
					
					
					default:
					{
						return base.__hx_getField(field, hash, throwErrors, isCheck, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override double __hx_getField_f(string field, int hash, bool throwErrors, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 922671056:
					{
						return ((double) (this.cachedIndex) );
					}
					
					
					case 1395555037:
					{
						return ((double) (this.cachedKey) );
					}
					
					
					case 2022294396:
					{
						return ((double) (this.upperBound) );
					}
					
					
					case 480756972:
					{
						return ((double) (this.nOccupied) );
					}
					
					
					case 1280549057:
					{
						return ((double) (this.size) );
					}
					
					
					case 1537812987:
					{
						return ((double) (this.nBuckets) );
					}
					
					
					default:
					{
						return base.__hx_getField_f(field, hash, throwErrors, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_invokeField(string field, int hash, global::ArrayHaxe dynargs) {
			unchecked {
				switch (hash) {
					case 946786476:
					{
						return this.toString();
					}
					
					
					case 328878574:
					{
						return this.iterator();
					}
					
					
					case 1191633396:
					{
						return this.keys();
					}
					
					
					case 142301684:
					{
						this.resize(((int) (global::haxe.lang.Runtime.toInt(dynargs[0])) ));
						break;
					}
					
					
					case 76061764:
					{
						return this.@remove(((int) (global::haxe.lang.Runtime.toInt(dynargs[0])) ));
					}
					
					
					case 1071652316:
					{
						return this.exists(((int) (global::haxe.lang.Runtime.toInt(dynargs[0])) ));
					}
					
					
					case 1797611211:
					{
						return this.getDefault(((int) (global::haxe.lang.Runtime.toInt(dynargs[0])) ), global::haxe.lang.Runtime.genericCast<T>(dynargs[1]));
					}
					
					
					case 5144726:
					{
						return (this.@get(((int) (global::haxe.lang.Runtime.toInt(dynargs[0])) ))).toDynamic();
					}
					
					
					case 1639293562:
					{
						return this.lookup(((int) (global::haxe.lang.Runtime.toInt(dynargs[0])) ));
					}
					
					
					case 5741474:
					{
						this.@set(((int) (global::haxe.lang.Runtime.toInt(dynargs[0])) ), global::haxe.lang.Runtime.genericCast<T>(dynargs[1]));
						break;
					}
					
					
					default:
					{
						return base.__hx_invokeField(field, hash, dynargs);
					}
					
				}
				
				return null;
			}
		}
		
		
		public override void __hx_getFields(global::ArrayHaxe<object> baseArr) {
			baseArr.push("cachedIndex");
			baseArr.push("cachedKey");
			baseArr.push("upperBound");
			baseArr.push("nOccupied");
			baseArr.push("size");
			baseArr.push("nBuckets");
			baseArr.push("vals");
			baseArr.push("_keys");
			baseArr.push("flags");
			base.__hx_getFields(baseArr);
		}
		
		
		public override string ToString(){
			return this.toString();
		}
		
		
	}
}



#pragma warning disable 109, 114, 219, 429, 168, 162
namespace haxe.ds {
	[global::haxe.lang.GenericInterface(typeof(global::haxe.ds.IntMap<object>))]
	public interface IntMap : global::haxe.lang.IHxObject, global::haxe.lang.IGenericObject {
		
		object haxe_ds_IntMap_cast<T_c>();
		
		object haxe_IMap_cast<K_c, V_c>();
		
		int lookup(int key);
		
		bool exists(int key);
		
		bool @remove(int key);
		
		void resize(int newNBuckets);
		
		object keys();
		
		object iterator();
		
		string toString();
		
	}
}



#pragma warning disable 109, 114, 219, 429, 168, 162
namespace haxe.ds._IntMap {
	public sealed class IntMapKeyIterator<T> : global::haxe.lang.HxObject, global::haxe.ds._IntMap.IntMapKeyIterator {
		
		public IntMapKeyIterator(global::haxe.lang.EmptyObject empty) {
		}
		
		
		public IntMapKeyIterator(global::haxe.ds.IntMap<T> m) {
			global::haxe.ds._IntMap.IntMapKeyIterator<object>.__hx_ctor_haxe_ds__IntMap_IntMapKeyIterator<T>(((global::haxe.ds._IntMap.IntMapKeyIterator<T>) (this) ), ((global::haxe.ds.IntMap<T>) (m) ));
		}
		
		
		public static void __hx_ctor_haxe_ds__IntMap_IntMapKeyIterator<T_c>(global::haxe.ds._IntMap.IntMapKeyIterator<T_c> __hx_this, global::haxe.ds.IntMap<T_c> m) {
			__hx_this.i = 0;
			__hx_this.m = m;
			__hx_this.len = m.nBuckets;
		}
		
		
		public static object __hx_cast<T_c_c>(global::haxe.ds._IntMap.IntMapKeyIterator me) {
			return ( (( me != null )) ? (me.haxe_ds__IntMap_IntMapKeyIterator_cast<T_c_c>()) : default(object) );
		}
		
		
		public object haxe_ds__IntMap_IntMapKeyIterator_cast<T_c>() {
			if (global::haxe.lang.Runtime.eq(typeof(T), typeof(T_c))) {
				return this;
			}
			
			global::haxe.ds._IntMap.IntMapKeyIterator<T_c> new_me = new global::haxe.ds._IntMap.IntMapKeyIterator<T_c>(((global::haxe.lang.EmptyObject) (global::haxe.lang.EmptyObject.EMPTY) ));
			global::ArrayHaxe<object> fields = global::ReflectHaxe.fields(this);
			int i = 0;
			while (( i < fields.length )) {
				string field = global::haxe.lang.Runtime.toString(fields[i++]);
				global::ReflectHaxe.setField(new_me, field, global::ReflectHaxe.field(this, field));
			}
			
			return new_me;
		}
		
		
		public global::haxe.ds.IntMap<T> m;
		
		public int i;
		
		public int len;
		
		public bool hasNext() {
			unchecked {
				{
					int _g1 = this.i;
					int _g = this.len;
					while (( _g1 < _g )) {
						int j = _g1++;
						if (( (( ((int) (( ((uint) (this.m.flags[( j >> 4 )]) ) >> (( (( j & 15 )) << 1 )) )) ) & 3 )) == 0 )) {
							this.i = j;
							return true;
						}
						
					}
					
				}
				
				return false;
			}
		}
		
		
		public int next() {
			int ret = this.m._keys[this.i];
			this.m.cachedIndex = this.i;
			this.m.cachedKey = ret;
			this.i++;
			return ret;
		}
		
		
		public override double __hx_setField_f(string field, int hash, double @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 5393365:
					{
						this.len = ((int) (@value) );
						return @value;
					}
					
					
					case 105:
					{
						this.i = ((int) (@value) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField_f(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_setField(string field, int hash, object @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 5393365:
					{
						this.len = ((int) (global::haxe.lang.Runtime.toInt(@value)) );
						return @value;
					}
					
					
					case 105:
					{
						this.i = ((int) (global::haxe.lang.Runtime.toInt(@value)) );
						return @value;
					}
					
					
					case 109:
					{
						this.m = ((global::haxe.ds.IntMap<T>) (global::haxe.ds.IntMap<object>.__hx_cast<T>(((global::haxe.ds.IntMap) (@value) ))) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_getField(string field, int hash, bool throwErrors, bool isCheck, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1224901875:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "next", 1224901875)) );
					}
					
					
					case 407283053:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "hasNext", 407283053)) );
					}
					
					
					case 5393365:
					{
						return this.len;
					}
					
					
					case 105:
					{
						return this.i;
					}
					
					
					case 109:
					{
						return this.m;
					}
					
					
					default:
					{
						return base.__hx_getField(field, hash, throwErrors, isCheck, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override double __hx_getField_f(string field, int hash, bool throwErrors, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 5393365:
					{
						return ((double) (this.len) );
					}
					
					
					case 105:
					{
						return ((double) (this.i) );
					}
					
					
					default:
					{
						return base.__hx_getField_f(field, hash, throwErrors, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_invokeField(string field, int hash, global::ArrayHaxe dynargs) {
			unchecked {
				switch (hash) {
					case 1224901875:
					{
						return this.next();
					}
					
					
					case 407283053:
					{
						return this.hasNext();
					}
					
					
					default:
					{
						return base.__hx_invokeField(field, hash, dynargs);
					}
					
				}
				
			}
		}
		
		
		public override void __hx_getFields(global::ArrayHaxe<object> baseArr) {
			baseArr.push("len");
			baseArr.push("i");
			baseArr.push("m");
			base.__hx_getFields(baseArr);
		}
		
		
	}
}



#pragma warning disable 109, 114, 219, 429, 168, 162
namespace haxe.ds._IntMap {
	[global::haxe.lang.GenericInterface(typeof(global::haxe.ds._IntMap.IntMapKeyIterator<object>))]
	public interface IntMapKeyIterator : global::haxe.lang.IHxObject, global::haxe.lang.IGenericObject {
		
		object haxe_ds__IntMap_IntMapKeyIterator_cast<T_c>();
		
		bool hasNext();
		
		int next();
		
	}
}



#pragma warning disable 109, 114, 219, 429, 168, 162
namespace haxe.ds._IntMap {
	public sealed class IntMapValueIterator<T> : global::haxe.lang.HxObject, global::haxe.ds._IntMap.IntMapValueIterator {
		
		public IntMapValueIterator(global::haxe.lang.EmptyObject empty) {
		}
		
		
		public IntMapValueIterator(global::haxe.ds.IntMap<T> m) {
			global::haxe.ds._IntMap.IntMapValueIterator<object>.__hx_ctor_haxe_ds__IntMap_IntMapValueIterator<T>(((global::haxe.ds._IntMap.IntMapValueIterator<T>) (this) ), ((global::haxe.ds.IntMap<T>) (m) ));
		}
		
		
		public static void __hx_ctor_haxe_ds__IntMap_IntMapValueIterator<T_c>(global::haxe.ds._IntMap.IntMapValueIterator<T_c> __hx_this, global::haxe.ds.IntMap<T_c> m) {
			__hx_this.i = 0;
			__hx_this.m = m;
			__hx_this.len = m.nBuckets;
		}
		
		
		public static object __hx_cast<T_c_c>(global::haxe.ds._IntMap.IntMapValueIterator me) {
			return ( (( me != null )) ? (me.haxe_ds__IntMap_IntMapValueIterator_cast<T_c_c>()) : default(object) );
		}
		
		
		public object haxe_ds__IntMap_IntMapValueIterator_cast<T_c>() {
			if (global::haxe.lang.Runtime.eq(typeof(T), typeof(T_c))) {
				return this;
			}
			
			global::haxe.ds._IntMap.IntMapValueIterator<T_c> new_me = new global::haxe.ds._IntMap.IntMapValueIterator<T_c>(((global::haxe.lang.EmptyObject) (global::haxe.lang.EmptyObject.EMPTY) ));
			global::ArrayHaxe<object> fields = global::ReflectHaxe.fields(this);
			int i = 0;
			while (( i < fields.length )) {
				string field = global::haxe.lang.Runtime.toString(fields[i++]);
				global::ReflectHaxe.setField(new_me, field, global::ReflectHaxe.field(this, field));
			}
			
			return new_me;
		}
		
		
		public global::haxe.ds.IntMap<T> m;
		
		public int i;
		
		public int len;
		
		public bool hasNext() {
			unchecked {
				{
					int _g1 = this.i;
					int _g = this.len;
					while (( _g1 < _g )) {
						int j = _g1++;
						if (( (( ((int) (( ((uint) (this.m.flags[( j >> 4 )]) ) >> (( (( j & 15 )) << 1 )) )) ) & 3 )) == 0 )) {
							this.i = j;
							return true;
						}
						
					}
					
				}
				
				return false;
			}
		}
		
		
		public T next() {
			return this.m.vals[this.i++];
		}
		
		
		public override double __hx_setField_f(string field, int hash, double @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 5393365:
					{
						this.len = ((int) (@value) );
						return @value;
					}
					
					
					case 105:
					{
						this.i = ((int) (@value) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField_f(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_setField(string field, int hash, object @value, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 5393365:
					{
						this.len = ((int) (global::haxe.lang.Runtime.toInt(@value)) );
						return @value;
					}
					
					
					case 105:
					{
						this.i = ((int) (global::haxe.lang.Runtime.toInt(@value)) );
						return @value;
					}
					
					
					case 109:
					{
						this.m = ((global::haxe.ds.IntMap<T>) (global::haxe.ds.IntMap<object>.__hx_cast<T>(((global::haxe.ds.IntMap) (@value) ))) );
						return @value;
					}
					
					
					default:
					{
						return base.__hx_setField(field, hash, @value, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_getField(string field, int hash, bool throwErrors, bool isCheck, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 1224901875:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "next", 1224901875)) );
					}
					
					
					case 407283053:
					{
						return ((global::haxe.lang.Function) (new global::haxe.lang.Closure(this, "hasNext", 407283053)) );
					}
					
					
					case 5393365:
					{
						return this.len;
					}
					
					
					case 105:
					{
						return this.i;
					}
					
					
					case 109:
					{
						return this.m;
					}
					
					
					default:
					{
						return base.__hx_getField(field, hash, throwErrors, isCheck, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override double __hx_getField_f(string field, int hash, bool throwErrors, bool handleProperties) {
			unchecked {
				switch (hash) {
					case 5393365:
					{
						return ((double) (this.len) );
					}
					
					
					case 105:
					{
						return ((double) (this.i) );
					}
					
					
					default:
					{
						return base.__hx_getField_f(field, hash, throwErrors, handleProperties);
					}
					
				}
				
			}
		}
		
		
		public override object __hx_invokeField(string field, int hash, global::ArrayHaxe dynargs) {
			unchecked {
				switch (hash) {
					case 1224901875:
					{
						return this.next();
					}
					
					
					case 407283053:
					{
						return this.hasNext();
					}
					
					
					default:
					{
						return base.__hx_invokeField(field, hash, dynargs);
					}
					
				}
				
			}
		}
		
		
		public override void __hx_getFields(global::ArrayHaxe<object> baseArr) {
			baseArr.push("len");
			baseArr.push("i");
			baseArr.push("m");
			base.__hx_getFields(baseArr);
		}
		
		
	}
}



#pragma warning disable 109, 114, 219, 429, 168, 162
namespace haxe.ds._IntMap {
	[global::haxe.lang.GenericInterface(typeof(global::haxe.ds._IntMap.IntMapValueIterator<object>))]
	public interface IntMapValueIterator : global::haxe.lang.IHxObject, global::haxe.lang.IGenericObject {
		
		object haxe_ds__IntMap_IntMapValueIterator_cast<T_c>();
		
		bool hasNext();
		
	}
}


