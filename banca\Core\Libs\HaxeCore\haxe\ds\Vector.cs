// Generated by Haxe 3.4.4

#pragma warning disable 109, 114, 219, 429, 168, 162
namespace haxe.ds._Vector {
	public sealed class Vector_Impl_ {
		
		public static T1[] _new<T1>(int length) {
			T1[] this1 = new T1[length];
			return ((T1[]) (this1) );
		}
		
		
		public static T1 @get<T1>(T1[] this1, int index) {
			return this1[index];
		}
		
		
		public static T1 @set<T1>(T1[] this1, int index, T1 val) {
			return this1[index] = val;
		}
		
		
		
		
		public static int get_length<T1>(T1[] this1) {
			return ( this1 as global::System.Array ).Length;
		}
		
		
		public static void blit<T>(T[] src, int srcPos, T[] dest, int destPos, int len) {
			global::System.Array.Copy(((global::System.Array) (src) ), ((int) (srcPos) ), ((global::System.Array) (dest) ), ((int) (destPos) ), ((int) (len) ));
		}
		
		
		public static global::ArrayHaxe<T1> toArray<T1>(T1[] this1) {
			global::ArrayHaxe<T1> a = new global::ArrayHaxe<T1>();
			int len = ( ((T1[]) (this1) ) as global::System.Array ).Length;
			{
				int _g1 = 0;
				int _g = len;
				while (( _g1 < _g )) {
					int i = _g1++;
					a[i] = global::haxe.lang.Runtime.genericCast<T1>(((T1[]) (this1) )[i]);
				}
				
			}
			
			return a;
		}
		
		
		public static T1[] toData<T1>(T1[] this1) {
			return ((T1[]) (this1) );
		}
		
		
		public static T[] fromData<T>(T[] data) {
			return ((T[]) (data) );
		}
		
		
		public static T[] fromArrayCopy<T>(global::ArrayHaxe<T> array) {
			T[] ret = new T[array.length];
			global::cs.Lib.p_nativeArray<T>(((global::ArrayHaxe<T>) (array) ), ((global::System.Array) (ret) ));
			return ((T[]) (ret) );
		}
		
		
		public static void sort<T, T1>(T[] this1, global::haxe.lang.Function f) {
			throw global::haxe.lang.HaxeException.wrap("not yet supported");
		}
		
		
	}
}


