﻿namespace Core.Libs.Loto
{
    public static class Status
    {
        public const string STATUS_ACTIVE = "Active";
        public const string STATUS_DEACTIVATE = "Deactivate";
        public const string STATUS_ERROR = "Error";
        public const string STATUS_COMPLETED = "Completed";
        public const string STATUS_SUCCESS = "Success";
        public const string STATUS_PENDING = "Pending";
        public const string STATUS_WAITING_CONFIRM = "WaitingConfirm";
        public const string STATUS_WAITING = "Waiting";
        public const string STATUS_WAITING_Bitcoin = "WaitingBitcoin";
        public const string STATUS_UNCONFIRMED = "Unconfirmed";
        public const string STATUS_2FA = "2FA";
        public const string STATUS_CANCEL = "Canceled";
        public const string STATUS_REVIEWING = "Reviewing";
    }
}