/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/cashinhigh.json
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/cashinlow.json
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/cashinmid.json
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/cer.pfx
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/config.json
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/data.json
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/default.json
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/high.json
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/low.json
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/mid.json
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/now.sh
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/stop.js
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/Core.deps.json
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/Core.dll
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/Core.pdb
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/Fleck.dll
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/websocket-sharp.dll
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/Fleck.pdb
/root/AllBackend/banca/Core/bin/Debug/netstandard2.0/websocket-sharp.pdb
/root/AllBackend/banca/Core/obj/Debug/netstandard2.0/Core.csprojAssemblyReference.cache
/root/AllBackend/banca/Core/obj/Debug/netstandard2.0/Core.AssemblyInfoInputs.cache
/root/AllBackend/banca/Core/obj/Debug/netstandard2.0/Core.AssemblyInfo.cs
/root/AllBackend/banca/Core/obj/Debug/netstandard2.0/Core.csproj.CoreCompileInputs.cache
/root/AllBackend/banca/Core/obj/Debug/netstandard2.0/Core.csproj.CopyComplete
/root/AllBackend/banca/Core/obj/Debug/netstandard2.0/Core.dll
/root/AllBackend/banca/Core/obj/Debug/netstandard2.0/Core.pdb
/var/app/banca/Core/bin/Debug/netstandard2.0/cashinhigh.json
/var/app/banca/Core/bin/Debug/netstandard2.0/cashinlow.json
/var/app/banca/Core/bin/Debug/netstandard2.0/cashinmid.json
/var/app/banca/Core/bin/Debug/netstandard2.0/cer.pfx
/var/app/banca/Core/bin/Debug/netstandard2.0/data.json
/var/app/banca/Core/bin/Debug/netstandard2.0/default.json
/var/app/banca/Core/bin/Debug/netstandard2.0/high.json
/var/app/banca/Core/bin/Debug/netstandard2.0/low.json
/var/app/banca/Core/bin/Debug/netstandard2.0/mid.json
/var/app/banca/Core/bin/Debug/netstandard2.0/now.sh
/var/app/banca/Core/bin/Debug/netstandard2.0/stop.js
/var/app/banca/Core/bin/Debug/netstandard2.0/config.json
/var/app/banca/Core/bin/Debug/netstandard2.0/Core.deps.json
/var/app/banca/Core/bin/Debug/netstandard2.0/Core.dll
/var/app/banca/Core/bin/Debug/netstandard2.0/Core.pdb
/var/app/banca/Core/bin/Debug/netstandard2.0/Fleck.dll
/var/app/banca/Core/bin/Debug/netstandard2.0/websocket-sharp.dll
/var/app/banca/Core/bin/Debug/netstandard2.0/Fleck.pdb
/var/app/banca/Core/bin/Debug/netstandard2.0/websocket-sharp.pdb
/var/app/banca/Core/obj/Debug/netstandard2.0/Core.csprojAssemblyReference.cache
/var/app/banca/Core/obj/Debug/netstandard2.0/Core.AssemblyInfoInputs.cache
/var/app/banca/Core/obj/Debug/netstandard2.0/Core.AssemblyInfo.cs
/var/app/banca/Core/obj/Debug/netstandard2.0/Core.csproj.CoreCompileInputs.cache
/var/app/banca/Core/obj/Debug/netstandard2.0/Core.csproj.CopyComplete
/var/app/banca/Core/obj/Debug/netstandard2.0/Core.dll
/var/app/banca/Core/obj/Debug/netstandard2.0/Core.pdb
