{"format": 1, "restore": {"/var/app/banca/websocket-sharp/websocket-sharp.csproj": {}}, "projects": {"/var/app/banca/websocket-sharp/websocket-sharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/var/app/banca/websocket-sharp/websocket-sharp.csproj", "projectName": "websocket-sharp", "projectPath": "/var/app/banca/websocket-sharp/websocket-sharp.csproj", "packagesPath": "/root/.nuget/packages/", "outputPath": "/var/app/banca/websocket-sharp/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/root/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netstandard2.0": {"dependencies": {"NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/3.1.426/RuntimeIdentifierGraph.json"}}}}}