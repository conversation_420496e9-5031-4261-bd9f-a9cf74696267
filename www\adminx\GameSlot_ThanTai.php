<?php
session_start();

// Kiểm tra session
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401); // Tr<PERSON> về mã lỗi 401
    header("Location: index.php"); // Chuyển hướng về trang index.php
    exit; // Dừng thực thi các mã PHP tiếp theo
}

// Kiểm tra nếu nickname đã được gửi qua form
if (isset($_GET['nickname']) && !empty($_GET['nickname'])) {
    $nickname = isset($_GET['nickname']) ? $_GET['nickname'] : '';
    $start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
    $end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';
    $lastTransaction = isset($_GET['lastTransaction']) ? $_GET['lastTransaction'] : null;
    $page = isset($_GET['p']) ? (int)$_GET['p'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] :100	; // Kiểm tra giá trị limit, mặc định là 10
    $url = "https://gameplus.789as.site/log_slot?page={$page}&limit={$limit}";
    

   $data = json_encode([
    'nick_name' => $nickname,
    'start_time' => $start_date,
    'end_time' => $end_date,
    'lastTransaction' =>$lastTransaction,
    'game'=>'ThanTai'

	]);
    // Cấu hình context cho yêu cầu POST
    $options = [
        'http' => [
            'method'  => 'POST',
            'header'  => "Content-Type: application/json\r\n" .
                         "Content-Length: " . strlen($data) . "\r\n",
            'content' => $data
        ]
    ];
    $context = stream_context_create($options);

    // Gửi yêu cầu và nhận phản hồi
    $response = file_get_contents($url, false, $context);

    // Kiểm tra lỗi
    if ($response === FALSE) {
        echo 'Lỗi khi gửi yêu cầu đến API';
        exit;
    }

    // Giải mã JSON
    $data = json_decode($response, true);
    
    // Kiểm tra nếu API trả về thành công
    if (isset($data['log_data']) ) {
        $transactions = $data['log_data'];
        // print_r($transactions);

        try {
            $lastTransaction = end($transactions)['_id'];
            // Kiểm tra nếu _id không tồn tại
            if (!isset($lastTransaction)) {
                throw new Exception("_id not found in the last transaction.");
            }
        } catch (Exception $e) {
            $lastTransaction = null;
        }
        $data = [];
        if ($result->num_rows > 0) {
            while($row = $result->fetch_assoc()) {
                $data[] = $row;
            }
        }
    } else {
        echo "Không thể lấy dữ liệu từ API";
        exit;
    }
}
?>


<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Adm 789</title><!--begin::Primary Meta Tags-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="title" content="AdminLTE 4 | Simple Tables">
    <meta name="author" content="ColorlibHQ">
    <meta name="description" content="AdminLTE is a Free Bootstrap 5 Admin Dashboard, 30 example pages using Vanilla JS.">
    <meta name="keywords" content="bootstrap 5, bootstrap, bootstrap 5 admin dashboard, bootstrap 5 dashboard, bootstrap 5 charts, bootstrap 5 calendar, bootstrap 5 datepicker, bootstrap 5 tables, bootstrap 5 datatable, vanilla js datatable, colorlibhq, colorlibhq dashboard, colorlibhq admin dashboard"><!--end::Primary Meta Tags--><!--begin::Fonts-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fontsource/source-sans-3@5.0.12/index.css" integrity="sha256-tXJfXfp6Ewt1ilPzLDtQnJV4hclT9XuaZUKyUvmyr+Q=" crossorigin="anonymous"><!--end::Fonts--><!--begin::Third Party Plugin(OverlayScrollbars)-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.3.0/styles/overlayscrollbars.min.css" integrity="sha256-dSokZseQNT08wYEWiz5iLI8QPlKxG+TswNRD8k35cpg=" crossorigin="anonymous"><!--end::Third Party Plugin(OverlayScrollbars)--><!--begin::Third Party Plugin(Bootstrap Icons)-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.min.css" integrity="sha256-Qsx5lrStHZyR9REqhUF8iQt73X06c8LGIUPzpOhwRrI=" crossorigin="anonymous"><!--end::Third Party Plugin(Bootstrap Icons)--><!--begin::Required Plugin(AdminLTE)-->
    <link rel="stylesheet" href="dist/css/adminlte.css"><!--end::Required Plugin(AdminLTE)-->
	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        .sort-button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        .sort-icon {
            font-size: 16px;
        }
    </style>
    <script>
        function sortTable(order) {
            // Lấy tbody
            var tbody = document.getElementById('dataTableBody');
            if (!tbody) {
                console.error('Element with ID "dataTableBody" not found.');
                return;
            }

            // Lấy tất cả các hàng trong tbody
            var rows = Array.from(tbody.getElementsByTagName('tr'));

            // Sắp xếp dữ liệu
            rows.sort(function(a, b) {
                var dateA = new Date(a.cells[5].textContent);
                var dateB = new Date(b.cells[5].textContent);
                return order === 'asc' ? dateA - dateB : dateB - dateA;
            });

            // Xóa các hàng cũ
            tbody.innerHTML = '';

            // Thêm các hàng đã sắp xếp vào tbody
            rows.forEach(function(row) {
                tbody.appendChild(row);
            });

            // Cập nhật trạng thái của nút sắp xếp
            document.getElementById('sortAsc').style.display = (order === 'asc') ? 'none' : 'inline';
            document.getElementById('sortDesc').style.display = (order === 'asc') ? 'inline' : 'none';
        }

        // Thực hiện sắp xếp mặc định khi tải trang
        window.onload = function() {
            sortTable('desc');
        };
    </script>
    <script>
    // Gửi yêu cầu định kỳ tới keep_alive.php để duy trì session
    setInterval(function() {
        fetch('keep_alive.php', {
            method: 'GET',
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'session_refreshed') {
                console.log('Session refreshed successfully.');
            } else {
                console.warn('Unexpected response:', data);
            }
        })
        .catch(error => {
            console.error('Error refreshing session:', error);
        });
    }, 30000);
    </script>
</head> <!--end::Head--> <!--begin::Body-->

<body class="layout-fixed sidebar-expand-lg bg-body-tertiary"> <!--begin::App Wrapper-->
    <div class="app-wrapper"> <!--begin::Header-->
        <nav class="app-header navbar navbar-expand bg-body"> <!--begin::Container-->
            <div class="container-fluid"> <!--begin::Start Navbar Links-->
                <ul class="navbar-nav">
                    <li class="nav-item"> <a class="nav-link" data-lte-toggle="sidebar" href="#" role="button"> <i class="bi bi-list"></i> </a> </li>
                    <li class="nav-item d-none d-md-block"> <a href="#" class="nav-link">Trang chủ</a> </li>
                </ul> <!--end::Start Navbar Links--> <!--begin::End Navbar Links-->
                <ul class="navbar-nav ms-auto">  <!--begin::Fullscreen Toggle-->
                    <li class="nav-item"> <a class="nav-link" href="#" data-lte-toggle="fullscreen"> <i data-lte-icon="maximize" class="bi bi-arrows-fullscreen"></i> <i data-lte-icon="minimize" class="bi bi-fullscreen-exit" style="display: none;"></i> </a> </li> <!--end::Fullscreen Toggle--> <!--begin::User Menu Dropdown-->
                    <li class="nav-item dropdown user-menu"> <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown"> <img src="dist/assets/img/user2-160x160.jpg" class="user-image rounded-circle shadow" alt="User Image"> <span class="d-none d-md-inline">SuperAdmin</span> </a>
                        <?php include 'Head.php'; ?>
                    </li> <!--end::User Menu Dropdown-->
                </ul> <!--end::End Navbar Links-->
            </div> <!--end::Container-->
        </nav> <!--end::Header--> <!--begin::Sidebar-->
			<?php include 'menu.php'; ?>  
		<!--begin::App Main-->
        <main class="app-main"> <!--begin::App Content Header-->
            <div class="app-content-header"> <!--begin::Container-->
                <div class="container-fluid"> <!--begin::Row-->
                    <div class="row">
                        <div class="col-sm-6">
                            <span class='badge text-bg-info' style="font-size: 15px;">Thần Tài</span>
                        </div>
                        <div class="col-sm-6">
                            <ol class="breadcrumb float-sm-end">
                                <li class="breadcrumb-item"><a href="#">Admin</a></li>
                                <li class="breadcrumb-item active" aria-current="page">
                                    Game Slot
                                </li>
								<li class="breadcrumb-item active" aria-current="page">
                                    Thần Tài
                                </li>
                            </ol>
                        </div>
                    </div> <!--end::Row-->
                </div> <!--end::Container-->
            </div> <!--end::App Content Header--> <!--begin::App Content-->

            <!--begin::Quick Example-->
<div class="card card-primary card-outline mb-4"> <!--begin::Header-->
    <!--begin::Form-->
    <form method="GET"> <!--begin::Body-->
        <div class="card-body">
            <div class="form-group">
                <label for="start_date" class="form-label">Nick Name</label>
                <input type="text" name="nickname" class="form-control date-input" id="nickname" value="<?php echo isset($_GET['nickname']) ? htmlspecialchars($_GET['nickname']) : ''; ?>">
                                        <div class="mb-3"> <label for="exampleInputEmail1" class="form-label">Ngày Bắt Đầu</label>
										<input type="datetime-local" id="start_date" name="start_date" class="form-control" aria-describedby="emailHelp" value="<?php echo isset($_GET['start_date']) ? htmlspecialchars($_GET['start_date']) : ''; ?>">

                                            
                                        </div>
                                        <div class="mb-3"> <label for="exampleInputEmail1" class="form-label">Ngày Kết Thúc</label>
										<input type="datetime-local" id="end_date" name="end_date" class="form-control" aria-describedby="emailHelp" value="<?php echo isset($_GET['end_date']) ? htmlspecialchars($_GET['end_date']) : ''; ?>">

            </div>		
		
        </div> <!--end::Body--> <!--begin::Footer-->
        <div class="card-footer"> <button type="submit" class="btn btn-primary">Tìm Kiếm</button> </div> <!--end::Footer-->
    </form> <!--end::Form-->
</div> <!--end::Quick Example-->



            <div class="card card-primary card-outline mb-4"> <!--begin::Header-->
                
                
            </div>

            <div class="app-content"> <!--begin::Container-->
                <div class="container-fluid"> <!--begin::Row-->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card mb-12">
                                <div class="card-header">
                                    <h3 class="card-title">
                                    
                                    </h3>
                                </div> <!-- /.card-header -->
                                <div class="card-body">
                                    <table class="table table-bordered" id="dataTableBody">
                                        <thead>
                                            <tr>
                                                <th>Phiên</th>
                                                <th>Nick Name</th>
                                                <th>Tiền Cược</th>
                                                <!-- Đây là một bình luận trong HTML
                                                <th>Ô đặt cược</th>
                                                <th>Các ô thắng</th>
                                                <th>Phần thưởng trên các ô	</th>
                                                 -->
                                                <th>Tiền Thưởng</th>
                                                <th>Kết Quả</th>
                                                <th>Thời Gian Giao Dịch
                                                <button id="sortAsc" onclick="sortTable('asc')" class="sort-button">
                        <span class="sort-icon">▲</span>
                    </button>
                    <button id="sortDesc" onclick="sortTable('desc')" class="sort-button" style="display: none;">
                        <span class="sort-icon">▼</span>
                    </button>              </th>                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($transactions as $transaction): ?>
												<tr>
													<td><?php echo $transaction['reference_id']; ?></td>
													<td><?php echo $nickname ?></td>
													<td><?php echo number_format($transaction['bet_value']); ?></td>
                                                     <!--
													<td><?php echo $transaction['lines_betting']; ?></td>
													<td><?php echo $transaction['lines_win']; ?></td>
													<td><?php echo $transaction['prizes_on_line']; ?></td>
                                                    -->
                                                    <td><?php echo number_format($transaction['prize']); ?></td>
                                                    <td><?php echo $transaction['result']; ?></td>
                                                    <td><?php echo $transaction['time_log']; ?></td>
                                                    
												</tr>
											<?php endforeach; ?>
                                        </tbody>
                                    </table>
									<br>
									<a href="?nickname=<?php echo htmlspecialchars($nickname); ?>&start_date=<?php echo htmlspecialchars($start_date); ?>&end_date=<?php echo htmlspecialchars($end_date); ?>&p=<?php echo $page > 1 ? $page - 1 : 1; ?>"><button type="submit" class="btn btn-primary">← Back</button></a>
									<a href="?nickname=<?php echo htmlspecialchars($nickname); ?>&start_date=<?php echo htmlspecialchars($start_date); ?>&end_date=<?php echo htmlspecialchars($end_date); ?>&p=<?php echo $page + 1; ?>&lastTransaction=<?php echo htmlspecialchars($lastTransaction); ?>">
    <button type="submit" class="btn btn-primary">Next →</button>
</a>

                                </div>
								
									
                            </div>
                        </div>
                    </div>
					
                </div>
            </div>
        </main>

        <footer class="app-footer"> <!--begin::To the end-->
                <div class="float-end d-none d-sm-inline"></div> <!--end::To the end--> <!--begin::Copyright--> <strong>
                    Copyright &copy; &nbsp;
                    <a href="#" class="text-decoration-none">Dark Casino</a>.
                </strong>
                All rights reserved.
                <!--end::Copyright-->
            </footer> <!--end::Footer-->
    </div> <!--end::App Wrapper--> <!--begin::Script--> <!--begin::Third Party Plugin(OverlayScrollbars)-->
    <script src="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.3.0/browser/overlayscrollbars.browser.es6.min.js" integrity="sha256-H2VM7BKda+v2Z4+DRy69uknwxjyDRhszjXFhsL4gD3w=" crossorigin="anonymous"></script> <!--end::Third Party Plugin(OverlayScrollbars)--><!--begin::Required Plugin(popperjs for Bootstrap 5)-->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js" integrity="sha256-whL0tQWoY1Ku1iskqPFvmZ+CHsvmRWx/PIoEvIeWh4I=" crossorigin="anonymous"></script> <!--end::Required Plugin(popperjs for Bootstrap 5)--><!--begin::Required Plugin(Bootstrap 5)-->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.min.js" integrity="sha256-YMa+wAM6QkVyz999odX7lPRxkoYAan8suedu4k2Zur8=" crossorigin="anonymous"></script> <!--end::Required Plugin(Bootstrap 5)--><!--begin::Required Plugin(AdminLTE)-->
    <script src="dist/js/adminlte.js"></script> <!--end::Required Plugin(AdminLTE)--><!--begin::OverlayScrollbars Configure-->
    <script>
        const SELECTOR_SIDEBAR_WRAPPER = ".sidebar-wrapper";
        const Default = {
            scrollbarTheme: "os-theme-light",
            scrollbarAutoHide: "leave",
            scrollbarClickScroll: true,
        };
        document.addEventListener("DOMContentLoaded", function() {
            const sidebarWrapper = document.querySelector(SELECTOR_SIDEBAR_WRAPPER);
            if (
                sidebarWrapper &&
                typeof OverlayScrollbarsGlobal?.OverlayScrollbars !== "undefined"
            ) {
                OverlayScrollbarsGlobal.OverlayScrollbars(sidebarWrapper, {
                    scrollbars: {
                        theme: Default.scrollbarTheme,
                        autoHide: Default.scrollbarAutoHide,
                        clickScroll: Default.scrollbarClickScroll,
                    },
                });
            }
        });
    </script> <!--end::OverlayScrollbars Configure--> <!--end::Script-->
</body><!--end::Body-->

</html>