<?php
session_start();

// <PERSON><PERSON>m tra đăng nhập
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header("Location: index.php");
    exit;
}

// Function to call API
function callApi($url, $data = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode == 200) {
        return json_decode($response, true);
    }
    return null;
}

// Auto detect domain
function getCurrentDomain() {
    $host = $_SERVER['HTTP_HOST'];
    $parts = explode('.', $host);
    if (count($parts) >= 2) {
        return $parts[count($parts) - 2] . '.' . $parts[count($parts) - 1];
    }
    return '789as.site'; // fallback
}

$currentDomain = getCurrentDomain();
$baseApiUrl = "https://iportal.{$currentDomain}/api";

$message = "";
$messageType = "";

// Xử lý các action
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    $transactionId = $_POST['transaction_id'] ?? '';
    $cartId = $_POST['cart_id'] ?? '';
    $bankNumber = $_POST['bank_number'] ?? '';
    $reason = $_POST['reason'] ?? '';
    $adminName = $_SESSION['username'] ?? 'admin';
    
    switch ($action) {
        case 'approve_withdrawal':
            // Gọi API duyệt rút tiền
            $apiData = [
                'type' => 'withdrawal',
                'action' => 'approve',
                'transaction_id' => $transactionId,
                'cart_id' => $cartId,
                'bank_number' => $bankNumber,
                'admin_name' => $adminName,
                'key' => 'BopVuEmVo123'
            ];

            $apiUrl = $baseApiUrl . "/transaction-approval?" . http_build_query($apiData);
            $result = callApi($apiUrl);

            if ($result && isset($result['success']) && $result['success'] == true) {
                $message = "Đã duyệt rút tiền thành công cho giao dịch: " . $transactionId;
                $messageType = "success";
            } else {
                $message = "Lỗi khi duyệt rút tiền: " . ($result['message'] ?? 'Unknown error');
                $messageType = "error";
            }
            break;

        case 'reject_withdrawal':
            // Gọi API từ chối rút tiền
            $apiData = [
                'type' => 'withdrawal',
                'action' => 'reject',
                'transaction_id' => $transactionId,
                'reason' => $reason ?: 'Rejected by admin: ' . $adminName,
                'admin_name' => $adminName,
                'key' => 'BopVuEmVo123'
            ];

            $apiUrl = $baseApiUrl . "/transaction-approval?" . http_build_query($apiData);
            $result = callApi($apiUrl);

            if ($result && isset($result['success']) && $result['success'] == true) {
                $message = "Đã từ chối rút tiền cho giao dịch: " . $transactionId . ". Lý do: " . $reason;
                $messageType = "success";
            } else {
                $message = "Lỗi khi từ chối rút tiền: " . ($result['message'] ?? 'Unknown error');
                $messageType = "error";
            }
            break;
    }
}

// Lấy danh sách giao dịch rút tiền đang chờ duyệt
$pendingWithdrawals = [];
$fromDate = $_GET['from_date'] ?? date('Y-m-d', strtotime('-7 days'));
$toDate = $_GET['to_date'] ?? date('Y-m-d');
$status = $_GET['status'] ?? '0'; // 0 = pending, 12 = request

// Gọi API để lấy danh sách giao dịch
$apiData = [
    'c' => '3063', // Command for getting pending withdrawals
    'from_date' => $fromDate,
    'to_date' => $toDate,
    'status' => $status
];

$apiUrl = $baseApiUrl . "/transaction-approval?" . http_build_query($apiData);
$result = callApi($apiUrl);

if ($result && isset($result['success']) && $result['success'] == true) {
    $pendingWithdrawals = $result['data'] ?? [];
} else {
    // Fallback to demo data if API fails
    $pendingWithdrawals = [
        [
            'id' => 'WD001',
            'cart_id' => 'CART_WD001',
            'username' => 'user123',
            'nickname' => 'Player123',
            'amount' => 2000000,
            'amount_real' => 1900000,
            'bank_name' => 'VCB',
            'bank_account' => '**********',
            'account_name' => 'NGUYEN VAN A',
            'request_time' => '2024-01-15 14:30:00',
            'status' => 12,
            'description' => 'Yêu cầu rút tiền',
            'fee' => 100000
        ]
    ];
}

?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duyệt Rút Tiền - Admin 789</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .status-pending { color: #ffc107; }
        .status-request { color: #17a2b8; }
        .status-approved { color: #28a745; }
        .status-rejected { color: #dc3545; }
        .domain-info {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 10px;
            margin-bottom: 20px;
        }
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        .transaction-row:hover {
            background-color: #f8f9fa;
        }
        .amount-info {
            display: flex;
            flex-direction: column;
        }
        .amount-original {
            font-weight: bold;
            color: #007bff;
        }
        .amount-real {
            font-size: 0.9em;
            color: #28a745;
        }
        .fee-info {
            font-size: 0.8em;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <?php include 'menu.php'; ?>
    
    <div class="container-fluid mt-4">
        <!-- Domain Info -->
        <div class="domain-info">
            <h6><i class="fas fa-globe"></i> Tên miền hiện tại: <strong><?php echo $currentDomain; ?></strong></h6>
            <small>API Base URL: <?php echo $baseApiUrl; ?></small>
        </div>
        
        <!-- Message Alert -->
        <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
            <i class="fas fa-<?php echo $messageType == 'success' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <!-- Filter Form -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> Bộ Lọc Giao Dịch</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Từ ngày:</label>
                        <input type="date" class="form-control" name="from_date" value="<?php echo $fromDate; ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Đến ngày:</label>
                        <input type="date" class="form-control" name="to_date" value="<?php echo $toDate; ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Trạng thái:</label>
                        <select class="form-control" name="status">
                            <option value="0" <?php echo $status == '0' ? 'selected' : ''; ?>>Đang chờ duyệt</option>
                            <option value="12" <?php echo $status == '12' ? 'selected' : ''; ?>>Yêu cầu rút tiền</option>
                            <option value="1" <?php echo $status == '1' ? 'selected' : ''; ?>>Đang xem xét</option>
                            <option value="4" <?php echo $status == '4' ? 'selected' : ''; ?>>Đã duyệt</option>
                            <option value="3" <?php echo $status == '3' ? 'selected' : ''; ?>>Đã từ chối</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block">
                            <i class="fas fa-search"></i> Tìm kiếm
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Pending Withdrawals List -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-money-bill-wave"></i> Danh Sách Giao Dịch Rút Tiền</h5>
                <small>Tổng số: <?php echo count($pendingWithdrawals); ?> giao dịch</small>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Người chơi</th>
                                <th>Số tiền</th>
                                <th>Ngân hàng</th>
                                <th>Tài khoản</th>
                                <th>Thời gian</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pendingWithdrawals as $withdrawal): ?>
                            <tr class="transaction-row">
                                <td><strong><?php echo $withdrawal['cart_id']; ?></strong></td>
                                <td>
                                    <div><strong><?php echo $withdrawal['nickname']; ?></strong></div>
                                    <small class="text-muted"><?php echo $withdrawal['username']; ?></small>
                                </td>
                                <td>
                                    <div class="amount-info">
                                        <span class="amount-original">
                                            <?php echo number_format($withdrawal['amount']); ?> VNĐ
                                        </span>
                                        <span class="amount-real">
                                            Thực nhận: <?php echo number_format($withdrawal['amount_real']); ?> VNĐ
                                        </span>
                                        <span class="fee-info">
                                            Phí: <?php echo number_format($withdrawal['fee']); ?> VNĐ
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    <div><strong><?php echo $withdrawal['bank_name']; ?></strong></div>
                                    <small><?php echo $withdrawal['bank_account']; ?></small>
                                </td>
                                <td><?php echo $withdrawal['account_name']; ?></td>
                                <td><?php echo date('d/m/Y H:i', strtotime($withdrawal['request_time'])); ?></td>
                                <td>
                                    <?php
                                    switch ($withdrawal['status']) {
                                        case 0:
                                            echo '<span class="status-pending"><i class="fas fa-clock"></i> Đang chờ</span>';
                                            break;
                                        case 12:
                                            echo '<span class="status-request"><i class="fas fa-paper-plane"></i> Yêu cầu rút</span>';
                                            break;
                                        case 1:
                                            echo '<span class="status-pending"><i class="fas fa-eye"></i> Đang xem xét</span>';
                                            break;
                                        case 4:
                                            echo '<span class="status-approved"><i class="fas fa-check"></i> Đã duyệt</span>';
                                            break;
                                        case 3:
                                            echo '<span class="status-rejected"><i class="fas fa-times"></i> Đã từ chối</span>';
                                            break;
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php if ($withdrawal['status'] == 0 || $withdrawal['status'] == 12 || $withdrawal['status'] == 1): ?>
                                    <div class="action-buttons">
                                        <!-- Approve Button -->
                                        <button type="button" class="btn btn-success btn-sm" title="Duyệt"
                                                onclick="showApproveModal('<?php echo $withdrawal['id']; ?>', '<?php echo $withdrawal['cart_id']; ?>', '<?php echo $withdrawal['nickname']; ?>', '<?php echo $withdrawal['bank_account']; ?>')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        
                                        <!-- Reject Button -->
                                        <button type="button" class="btn btn-danger btn-sm" title="Từ chối" 
                                                onclick="showRejectModal('<?php echo $withdrawal['id']; ?>', '<?php echo $withdrawal['cart_id']; ?>', '<?php echo $withdrawal['nickname']; ?>')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <?php else: ?>
                                    <span class="text-muted">Đã xử lý</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            
                            <?php if (empty($pendingWithdrawals)): ?>
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <div>Không có giao dịch nào cần duyệt</div>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Approve Modal -->
    <div class="modal fade" id="approveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title"><i class="fas fa-check-circle"></i> Duyệt Rút Tiền</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="approveForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="approve_withdrawal">
                        <input type="hidden" name="transaction_id" id="approveTransactionId">
                        <input type="hidden" name="cart_id" id="approveCartId">
                        <input type="hidden" name="bank_number" id="approveBankNumber">
                        
                        <div class="mb-3">
                            <label class="form-label">Giao dịch:</label>
                            <div id="approveOrderInfo" class="form-control-plaintext"></div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Xác nhận:</strong> Bạn có chắc chắn muốn duyệt giao dịch rút tiền này?
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check"></i> Duyệt
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Reject Modal -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title"><i class="fas fa-times-circle"></i> Từ Chối Rút Tiền</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="rejectForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="reject_withdrawal">
                        <input type="hidden" name="transaction_id" id="rejectTransactionId">
                        <input type="hidden" name="cart_id" id="rejectCartId">
                        
                        <div class="mb-3">
                            <label class="form-label">Giao dịch:</label>
                            <div id="rejectOrderInfo" class="form-control-plaintext"></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="rejectReason" class="form-label">Lý do từ chối: <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="rejectReason" name="reason" rows="3" 
                                      placeholder="Nhập lý do từ chối giao dịch..." required></textarea>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Lưu ý:</strong> Khi từ chối, tiền sẽ được hoàn lại vào tài khoản người chơi.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-times"></i> Từ Chối
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showApproveModal(transactionId, cartId, nickname, bankNumber) {
            document.getElementById('approveTransactionId').value = transactionId;
            document.getElementById('approveCartId').value = cartId;
            document.getElementById('approveBankNumber').value = bankNumber;
            document.getElementById('approveOrderInfo').textContent = `${cartId} - ${nickname}`;
            
            var modal = new bootstrap.Modal(document.getElementById('approveModal'));
            modal.show();
        }
        
        function showRejectModal(transactionId, cartId, nickname) {
            document.getElementById('rejectTransactionId').value = transactionId;
            document.getElementById('rejectCartId').value = cartId;
            document.getElementById('rejectOrderInfo').textContent = `${cartId} - ${nickname}`;
            document.getElementById('rejectReason').value = '';
            
            var modal = new bootstrap.Modal(document.getElementById('rejectModal'));
            modal.show();
        }
        
        // Auto refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
