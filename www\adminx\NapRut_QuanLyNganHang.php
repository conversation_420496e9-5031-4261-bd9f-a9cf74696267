<?php
session_start();

// <PERSON><PERSON>m tra đăng nhập
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header("Location: index.php");
    exit;
}

// Function to call API
function callApi($url, $data = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen(json_encode($data))
        ));
    }
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    return json_decode($response, true);
}

// Auto detect domain
function getCurrentDomain() {
    $host = $_SERVER['HTTP_HOST'];
    $parts = explode('.', $host);
    if (count($parts) >= 2) {
        return $parts[count($parts) - 2] . '.' . $parts[count($parts) - 1];
    }
    return '789as.site'; // fallback
}

$currentDomain = getCurrentDomain();
$baseApiUrl = "https://iportal.{$currentDomain}/api/bank-management";

// Load bank accounts configuration
$bankConfigFile = '../config/bank_accounts.json';
$bankConfig = [];
if (file_exists($bankConfigFile)) {
    $bankConfig = json_decode(file_get_contents($bankConfigFile), true);
} else {
    // Create default config
    $bankConfig = [
        'accounts' => [],
        'settings' => [
            'auto_switch' => true,
            'switch_threshold' => 80,
            'maintenance_mode' => false
        ]
    ];
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'switch_account':
                $accountId = $_POST['account_id'];
                // Call API to switch bank account
                $apiUrl = "{$baseApiUrl}?c=3057&account_id={$accountId}&key=BopVuEmVo123";
                $result = callApi($apiUrl);

                if ($result && isset($result['success']) && $result['success']) {
                    $message = "Đã chuyển đổi tài khoản ngân hàng thành công!";
                    $messageType = "success";
                    // Reload bank config after successful switch
                    $bankConfig = json_decode(file_get_contents($bankConfigFile), true);
                } else {
                    $message = "Lỗi khi chuyển đổi tài khoản ngân hàng: " . (isset($result['message']) ? $result['message'] : 'Unknown error');
                    $messageType = "error";
                }
                break;
                
            case 'add_account':
                $newAccount = [
                    'id' => $_POST['account_id'],
                    'bank_name' => $_POST['bank_name'],
                    'account_number' => $_POST['account_number'],
                    'account_name' => $_POST['account_name'],
                    'status' => 0,
                    'priority' => (int)$_POST['priority'],
                    'daily_limit' => (int)$_POST['daily_limit'],
                    'current_amount' => 0,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                $bankConfig['accounts'][] = $newAccount;
                file_put_contents($bankConfigFile, json_encode($bankConfig, JSON_PRETTY_PRINT));
                
                $message = "Đã thêm tài khoản ngân hàng mới thành công!";
                $messageType = "success";
                break;
                
            case 'update_status':
                $accountId = $_POST['account_id'];
                $status = (int)$_POST['status'];

                // Call API to update status
                $apiUrl = "{$baseApiUrl}?c=3061&account_id={$accountId}&status={$status}&key=BopVuEmVo123";
                $result = callApi($apiUrl);

                if ($result && isset($result['success']) && $result['success']) {
                    // Update local config
                    foreach ($bankConfig['accounts'] as &$account) {
                        if ($account['id'] === $accountId) {
                            $account['status'] = $status;
                            $account['updated_at'] = date('Y-m-d H:i:s');
                            break;
                        }
                    }
                    file_put_contents($bankConfigFile, json_encode($bankConfig, JSON_PRETTY_PRINT));

                    $message = "Đã cập nhật trạng thái tài khoản thành công!";
                    $messageType = "success";
                } else {
                    $message = "Lỗi khi cập nhật trạng thái: " . (isset($result['message']) ? $result['message'] : 'Unknown error');
                    $messageType = "error";
                }
                break;
                
            case 'update_settings':
                $bankConfig['settings']['auto_switch'] = isset($_POST['auto_switch']);
                $bankConfig['settings']['switch_threshold'] = (int)$_POST['switch_threshold'];
                $bankConfig['settings']['maintenance_mode'] = isset($_POST['maintenance_mode']);
                
                file_put_contents($bankConfigFile, json_encode($bankConfig, JSON_PRETTY_PRINT));
                
                $message = "Đã cập nhật cài đặt thành công!";
                $messageType = "success";
                break;
        }
    }
}

// Get current active account
$activeAccount = null;
foreach ($bankConfig['accounts'] as $account) {
    if ($account['status'] == 1) {
        $activeAccount = $account;
        break;
    }
}

?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản Lý Ngân Hàng - Admin 789</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .status-active { color: #28a745; }
        .status-inactive { color: #6c757d; }
        .status-maintenance { color: #ffc107; }
        .domain-info {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <?php include 'menu.php'; ?>
    
    <div class="container-fluid mt-4">
        <!-- Domain Info -->
        <div class="domain-info">
            <h6><i class="fas fa-globe"></i> Tên miền hiện tại: <strong><?php echo $currentDomain; ?></strong></h6>
            <small>API Base URL: <?php echo $baseApiUrl; ?></small>
        </div>
        
        <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <!-- Current Active Account -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-university"></i> Tài Khoản Ngân Hàng Hiện Tại</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($activeAccount): ?>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Ngân hàng:</strong> <?php echo $activeAccount['bank_name']; ?>
                            </div>
                            <div class="col-md-3">
                                <strong>Số tài khoản:</strong> <?php echo $activeAccount['account_number']; ?>
                            </div>
                            <div class="col-md-3">
                                <strong>Tên tài khoản:</strong> <?php echo $activeAccount['account_name']; ?>
                            </div>
                            <div class="col-md-3">
                                <strong>Trạng thái:</strong> <span class="status-active"><i class="fas fa-check-circle"></i> Đang hoạt động</span>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> Không có tài khoản ngân hàng nào đang hoạt động!
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Switch -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-exchange-alt"></i> Chuyển Đổi Nhanh</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="switch_account">
                            <div class="row">
                                <div class="col-md-6">
                                    <select name="account_id" class="form-select" required>
                                        <option value="">Chọn tài khoản ngân hàng...</option>
                                        <?php foreach ($bankConfig['accounts'] as $account): ?>
                                        <?php if ($account['status'] != 1): ?>
                                        <option value="<?php echo $account['id']; ?>">
                                            <?php echo $account['bank_name']; ?> - <?php echo $account['account_number']; ?> (<?php echo $account['account_name']; ?>)
                                        </option>
                                        <?php endif; ?>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-exchange-alt"></i> Chuyển Đổi
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bank Accounts List -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list"></i> Danh Sách Tài Khoản Ngân Hàng</h5>
                        <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addAccountModal">
                            <i class="fas fa-plus"></i> Thêm Tài Khoản
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Ngân Hàng</th>
                                        <th>Số Tài Khoản</th>
                                        <th>Tên Tài Khoản</th>
                                        <th>Trạng Thái</th>
                                        <th>Ưu Tiên</th>
                                        <th>Hạn Mức</th>
                                        <th>Thao Tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($bankConfig['accounts'] as $account): ?>
                                    <tr>
                                        <td><?php echo $account['id']; ?></td>
                                        <td><?php echo $account['bank_name']; ?></td>
                                        <td><?php echo $account['account_number']; ?></td>
                                        <td><?php echo $account['account_name']; ?></td>
                                        <td>
                                            <?php
                                            switch ($account['status']) {
                                                case 1:
                                                    echo '<span class="status-active"><i class="fas fa-check-circle"></i> Hoạt động</span>';
                                                    break;
                                                case 2:
                                                    echo '<span class="status-maintenance"><i class="fas fa-tools"></i> Bảo trì</span>';
                                                    break;
                                                default:
                                                    echo '<span class="status-inactive"><i class="fas fa-times-circle"></i> Không hoạt động</span>';
                                            }
                                            ?>
                                        </td>
                                        <td><?php echo $account['priority']; ?></td>
                                        <td><?php echo number_format($account['daily_limit']); ?> VNĐ</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <?php if ($account['status'] != 1): ?>
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="action" value="switch_account">
                                                    <input type="hidden" name="account_id" value="<?php echo $account['id']; ?>">
                                                    <button type="submit" class="btn btn-primary btn-sm" title="Kích hoạt">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                </form>
                                                <?php endif; ?>

                                                <div class="dropdown">
                                                    <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-cog"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <form method="POST" class="d-inline">
                                                                <input type="hidden" name="action" value="update_status">
                                                                <input type="hidden" name="account_id" value="<?php echo $account['id']; ?>">
                                                                <input type="hidden" name="status" value="0">
                                                                <button type="submit" class="dropdown-item">
                                                                    <i class="fas fa-pause"></i> Tạm dừng
                                                                </button>
                                                            </form>
                                                        </li>
                                                        <li>
                                                            <form method="POST" class="d-inline">
                                                                <input type="hidden" name="action" value="update_status">
                                                                <input type="hidden" name="account_id" value="<?php echo $account['id']; ?>">
                                                                <input type="hidden" name="status" value="2">
                                                                <button type="submit" class="dropdown-item">
                                                                    <i class="fas fa-tools"></i> Bảo trì
                                                                </button>
                                                            </form>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs"></i> Cài Đặt Hệ Thống</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="update_settings">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="auto_switch" id="auto_switch"
                                               <?php echo $bankConfig['settings']['auto_switch'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="auto_switch">
                                            Tự động chuyển đổi tài khoản
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="switch_threshold">Ngưỡng chuyển đổi (%):</label>
                                        <input type="number" class="form-control" name="switch_threshold" id="switch_threshold"
                                               value="<?php echo $bankConfig['settings']['switch_threshold']; ?>" min="1" max="100">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="maintenance_mode" id="maintenance_mode"
                                               <?php echo $bankConfig['settings']['maintenance_mode'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="maintenance_mode">
                                            Chế độ bảo trì
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Lưu Cài Đặt
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Account Modal -->
    <div class="modal fade" id="addAccountModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Thêm Tài Khoản Ngân Hàng</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="add_account">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="account_id" class="form-label">ID Tài Khoản:</label>
                            <input type="text" class="form-control" name="account_id" id="account_id" required>
                        </div>
                        <div class="mb-3">
                            <label for="bank_name" class="form-label">Ngân Hàng:</label>
                            <select class="form-select" name="bank_name" id="bank_name" required>
                                <option value="">Chọn ngân hàng...</option>
                                <option value="VCB">Vietcombank</option>
                                <option value="TCB">Techcombank</option>
                                <option value="VIB">VIB Bank</option>
                                <option value="VPB">VPBank</option>
                                <option value="MB">MBBank</option>
                                <option value="ACB">ACB Bank</option>
                                <option value="SHB">SHB Bank</option>
                                <option value="EIB">Eximbank</option>
                                <option value="SCB">Sacombank</option>
                                <option value="TPB">TPBank</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="account_number" class="form-label">Số Tài Khoản:</label>
                            <input type="text" class="form-control" name="account_number" id="account_number" required>
                        </div>
                        <div class="mb-3">
                            <label for="account_name" class="form-label">Tên Tài Khoản:</label>
                            <input type="text" class="form-control" name="account_name" id="account_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="priority" class="form-label">Ưu Tiên:</label>
                            <input type="number" class="form-control" name="priority" id="priority" value="1" min="1" required>
                        </div>
                        <div class="mb-3">
                            <label for="daily_limit" class="form-label">Hạn Mức Hàng Ngày (VNĐ):</label>
                            <input type="number" class="form-control" name="daily_limit" id="daily_limit" value="********" min="0" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-primary">Thêm Tài Khoản</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
