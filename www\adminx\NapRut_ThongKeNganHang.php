<?php
session_start();

// <PERSON><PERSON>m tra đăng nhập
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header("Location: index.php");
    exit;
}

// Function to call API
function callApi($url, $data = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen(json_encode($data))
        ));
    }
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    return json_decode($response, true);
}

// Auto detect domain
function getCurrentDomain() {
    $host = $_SERVER['HTTP_HOST'];
    $parts = explode('.', $host);
    if (count($parts) >= 2) {
        return $parts[count($parts) - 2] . '.' . $parts[count($parts) - 1];
    }
    return '789as.site'; // fallback
}

$currentDomain = getCurrentDomain();
$baseApiUrl = "https://iportal.{$currentDomain}/api/bank-management";

// Get all bank accounts from API
$apiUrl = "{$baseApiUrl}?c=3060&key=BopVuEmVo123";
$apiResult = callApi($apiUrl);
$bankAccounts = [];
if ($apiResult && isset($apiResult['success']) && $apiResult['success']) {
    $bankAccounts = json_decode($apiResult['accounts'], true);
}

// Get current active account
$currentApiUrl = "{$baseApiUrl}?c=3058&key=BopVuEmVo123";
$currentResult = callApi($currentApiUrl);
$currentAccount = null;
if ($currentResult && isset($currentResult['success']) && $currentResult['success']) {
    $currentAccount = $currentResult['account'];
}

// Calculate statistics
$totalAccounts = count($bankAccounts);
$activeAccounts = 0;
$inactiveAccounts = 0;
$maintenanceAccounts = 0;
$totalDailyLimit = 0;

foreach ($bankAccounts as $account) {
    switch ($account['status']) {
        case 1:
            $activeAccounts++;
            break;
        case 2:
            $maintenanceAccounts++;
            break;
        default:
            $inactiveAccounts++;
    }
    $totalDailyLimit += $account['daily_limit'];
}

?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thống Kê Ngân Hàng - Admin 789</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .status-active { color: #28a745; }
        .status-inactive { color: #6c757d; }
        .status-maintenance { color: #ffc107; }
        .domain-info {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 10px;
            margin-bottom: 20px;
        }
        .stat-card {
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <?php include 'menu.php'; ?>
    
    <div class="container-fluid mt-4">
        <!-- Domain Info -->
        <div class="domain-info">
            <h6><i class="fas fa-globe"></i> Tên miền hiện tại: <strong><?php echo $currentDomain; ?></strong></h6>
            <small>API Base URL: <?php echo $baseApiUrl; ?></small>
        </div>
        
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?php echo $totalAccounts; ?></h4>
                                <p class="mb-0">Tổng Tài Khoản</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-university fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?php echo $activeAccounts; ?></h4>
                                <p class="mb-0">Đang Hoạt Động</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?php echo $maintenanceAccounts; ?></h4>
                                <p class="mb-0">Bảo Trì</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-tools fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card bg-secondary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?php echo $inactiveAccounts; ?></h4>
                                <p class="mb-0">Không Hoạt Động</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-times-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Current Active Account -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-star"></i> Tài Khoản Đang Hoạt Động</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($currentAccount): ?>
                        <div class="row">
                            <div class="col-md-2">
                                <strong>ID:</strong> <?php echo $currentAccount['id']; ?>
                            </div>
                            <div class="col-md-2">
                                <strong>Ngân hàng:</strong> <?php echo $currentAccount['bank_name']; ?>
                            </div>
                            <div class="col-md-3">
                                <strong>Số tài khoản:</strong> <?php echo $currentAccount['account_number']; ?>
                            </div>
                            <div class="col-md-3">
                                <strong>Tên tài khoản:</strong> <?php echo $currentAccount['account_name']; ?>
                            </div>
                            <div class="col-md-2">
                                <strong>Ưu tiên:</strong> <?php echo $currentAccount['priority']; ?>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-4">
                                <strong>Hạn mức hàng ngày:</strong> <?php echo number_format($currentAccount['daily_limit']); ?> VNĐ
                            </div>
                            <div class="col-md-4">
                                <strong>Đã sử dụng:</strong> <?php echo number_format($currentAccount['current_amount']); ?> VNĐ
                            </div>
                            <div class="col-md-4">
                                <strong>Còn lại:</strong> <?php echo number_format($currentAccount['daily_limit'] - $currentAccount['current_amount']); ?> VNĐ
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> Không có tài khoản ngân hàng nào đang hoạt động!
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Bank Accounts Summary -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> Tổng Quan Tài Khoản Ngân Hàng</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Ngân Hàng</th>
                                        <th>Số Tài Khoản</th>
                                        <th>Tên Tài Khoản</th>
                                        <th>Trạng Thái</th>
                                        <th>Ưu Tiên</th>
                                        <th>Hạn Mức</th>
                                        <th>Đã Sử Dụng</th>
                                        <th>Tỷ Lệ (%)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($bankAccounts as $account): ?>
                                    <tr <?php echo ($currentAccount && $account['id'] == $currentAccount['id']) ? 'class="table-success"' : ''; ?>>
                                        <td><?php echo $account['id']; ?></td>
                                        <td><?php echo $account['bank_name']; ?></td>
                                        <td><?php echo $account['account_number']; ?></td>
                                        <td><?php echo $account['account_name']; ?></td>
                                        <td>
                                            <?php
                                            switch ($account['status']) {
                                                case 1:
                                                    echo '<span class="status-active"><i class="fas fa-check-circle"></i> Hoạt động</span>';
                                                    break;
                                                case 2:
                                                    echo '<span class="status-maintenance"><i class="fas fa-tools"></i> Bảo trì</span>';
                                                    break;
                                                default:
                                                    echo '<span class="status-inactive"><i class="fas fa-times-circle"></i> Không hoạt động</span>';
                                            }
                                            ?>
                                        </td>
                                        <td><?php echo $account['priority']; ?></td>
                                        <td><?php echo number_format($account['daily_limit']); ?> VNĐ</td>
                                        <td><?php echo number_format($account['current_amount']); ?> VNĐ</td>
                                        <td>
                                            <?php 
                                            $percentage = $account['daily_limit'] > 0 ? ($account['current_amount'] / $account['daily_limit']) * 100 : 0;
                                            $progressClass = $percentage > 80 ? 'bg-danger' : ($percentage > 60 ? 'bg-warning' : 'bg-success');
                                            ?>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar <?php echo $progressClass; ?>" role="progressbar" 
                                                     style="width: <?php echo $percentage; ?>%" 
                                                     aria-valuenow="<?php echo $percentage; ?>" 
                                                     aria-valuemin="0" aria-valuemax="100">
                                                    <?php echo number_format($percentage, 1); ?>%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Summary Info -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> Thông Tin Tổng Hợp</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Tổng hạn mức hàng ngày:</strong> <?php echo number_format($totalDailyLimit); ?> VNĐ</p>
                        <p><strong>Tỷ lệ tài khoản hoạt động:</strong> <?php echo $totalAccounts > 0 ? number_format(($activeAccounts / $totalAccounts) * 100, 1) : 0; ?>%</p>
                        <p><strong>Cập nhật lần cuối:</strong> <?php echo date('d/m/Y H:i:s'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs"></i> Thao Tác Nhanh</h5>
                    </div>
                    <div class="card-body">
                        <a href="NapRut_QuanLyNganHang.php" class="btn btn-primary mb-2">
                            <i class="fas fa-edit"></i> Quản Lý Tài Khoản
                        </a><br>
                        <button class="btn btn-success mb-2" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i> Làm Mới Dữ Liệu
                        </button><br>
                        <form method="POST" action="NapRut_QuanLyNganHang.php" class="d-inline">
                            <input type="hidden" name="action" value="switch_account">
                            <input type="hidden" name="account_id" value="next">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-exchange-alt"></i> Chuyển Tài Khoản Tiếp Theo
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
