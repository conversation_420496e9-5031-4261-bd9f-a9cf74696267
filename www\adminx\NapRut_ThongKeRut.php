<?php
session_start();

// Kiểm tra session
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401); // Tr<PERSON> về mã lỗi 401
    echo "Unauthorized access. Please <a href='index.php'>login</a>."; // Hiển thị thông báo lỗi
    exit; // Dừng thực thi các mã PHP tiếp theo
}
$nickname = $_GET['nickname'] ?? null;
$start_date = isset($_GET['start_date']) && $_GET['start_date'] !== '' ? $_GET['start_date'] : null;
$end_date = isset($_GET['end_date']) && $_GET['end_date'] !== '' ? $_GET['end_date'] : null;
// $selectedStatuses = isset($_GET['status']) ? $_GET['status'] : [];
$page = isset($_GET['p']) ? (int)$_GET['p'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 100;	; // Kiểm tra giá trị limit, mặc định là 10
$url = "https://gameplus.789as.site/withdraw_statistical?page={$page}&limit={$limit}";
$data = json_encode([
    'nick_name' => $nickname,
    'start_time' => $start_date,
    'end_time' => $end_date,
    'status'=> $selectedStatuses
	]);
    // Cấu hình context cho yêu cầu POST
    $options = [
        'http' => [
            'method'  => 'POST',
            'header'  => "Content-Type: application/json\r\n" .
                         "Content-Length: " . strlen($data) . "\r\n",
            'content' => $data
        ]
    ];
    $context = stream_context_create($options);

    // Gửi yêu cầu và nhận phản hồi
    $response = file_get_contents($url, false, $context);

    // Kiểm tra lỗi
    if ($response === FALSE) {
        echo 'Lỗi khi gửi yêu cầu đến API';
        exit;
    }
    $data = json_decode($response, true);
    $total_count = $data['total_count']
   
?>
<!DOCTYPE html>
<html lang="en"> <!--begin::Head-->

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Adm 789</title><!--begin::Primary Meta Tags-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="title" content="AdminLTE 4 | Simple Tables">
    <meta name="author" content="ColorlibHQ">
    <meta name="description" content="AdminLTE is a Free Bootstrap 5 Admin Dashboard, 30 example pages using Vanilla JS.">
    <meta name="keywords" content="bootstrap 5, bootstrap, bootstrap 5 admin dashboard, bootstrap 5 dashboard, bootstrap 5 charts, bootstrap 5 calendar, bootstrap 5 datepicker, bootstrap 5 tables, bootstrap 5 datatable, vanilla js datatable, colorlibhq, colorlibhq dashboard, colorlibhq admin dashboard"><!--end::Primary Meta Tags--><!--begin::Fonts-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fontsource/source-sans-3@5.0.12/index.css" integrity="sha256-tXJfXfp6Ewt1ilPzLDtQnJV4hclT9XuaZUKyUvmyr+Q=" crossorigin="anonymous"><!--end::Fonts--><!--begin::Third Party Plugin(OverlayScrollbars)-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.3.0/styles/overlayscrollbars.min.css" integrity="sha256-dSokZseQNT08wYEWiz5iLI8QPlKxG+TswNRD8k35cpg=" crossorigin="anonymous"><!--end::Third Party Plugin(OverlayScrollbars)--><!--begin::Third Party Plugin(Bootstrap Icons)-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.min.css" integrity="sha256-Qsx5lrStHZyR9REqhUF8iQt73X06c8LGIUPzpOhwRrI=" crossorigin="anonymous"><!--end::Third Party Plugin(Bootstrap Icons)--><!--begin::Required Plugin(AdminLTE)-->
    <link rel="stylesheet" href="dist/css/adminlte.css"><!--end::Required Plugin(AdminLTE)-->
    <script>
    // Gửi yêu cầu định kỳ tới keep_alive.php để duy trì session
    setInterval(function() {
        fetch('keep_alive.php', {
            method: 'GET',
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'session_refreshed') {
                console.log('Session refreshed successfully.');
            } else {
                console.warn('Unexpected response:', data);
            }
        })
        .catch(error => {
            console.error('Error refreshing session:', error);
        });
    }, 30000);
    </script>
</head> <!--end::Head--> <!--begin::Body-->

<body class="layout-fixed sidebar-expand-lg bg-body-tertiary"> <!--begin::App Wrapper-->
    <div class="app-wrapper"> <!--begin::Header-->
        <nav class="app-header navbar navbar-expand bg-body"> <!--begin::Container-->
            <div class="container-fluid"> <!--begin::Start Navbar Links-->
                <ul class="navbar-nav">
                    <li class="nav-item"> <a class="nav-link" data-lte-toggle="sidebar" href="#" role="button"> <i class="bi bi-list"></i> </a> </li>
                    <li class="nav-item d-none d-md-block"> <a href="#" class="nav-link">Thống Kê Rút Tiền</a> </li>
                </ul> <!--end::Start Navbar Links--> <!--begin::End Navbar Links-->
                <ul class="navbar-nav ms-auto">  <!--begin::Fullscreen Toggle-->
                    <li class="nav-item"> <a class="nav-link" href="#" data-lte-toggle="fullscreen"> <i data-lte-icon="maximize" class="bi bi-arrows-fullscreen"></i> <i data-lte-icon="minimize" class="bi bi-fullscreen-exit" style="display: none;"></i> </a> </li> <!--end::Fullscreen Toggle--> <!--begin::User Menu Dropdown-->
                    <li class="nav-item dropdown user-menu"> <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown"> <img src="dist/assets/img/user2-160x160.jpg" class="user-image rounded-circle shadow" alt="User Image"> <span class="d-none d-md-inline">SuperAdmin</span> </a>
                        <?php include 'Head.php'; ?>
                    </li> <!--end::User Menu Dropdown-->
                </ul> <!--end::End Navbar Links-->
            </div> <!--end::Container-->
        </nav> <!--end::Header--> <!--begin::Sidebar-->
			<?php include 'menu.php'; ?>  
		<!--begin::App Main-->
        <main class="app-main"> <!--begin::App Content Header-->
            <div class="app-content-header"> <!--begin::Container-->
                <div class="container-fluid"> <!--begin::Row-->
                    <div class="row">
                        <div class="col-sm-6">
                            <span class='badge text-bg-info' style="font-size: 15px;">Thống Kê Rút Tiền</span>
                        </div>
                        <div class="col-sm-6">
                           <ol class="breadcrumb float-sm-end">
                                <li class="breadcrumb-item"><a href="#">Admin</a></li>
                                <li class="breadcrumb-item active" aria-current="page">
                                    Nạp Rút
                                </li>
								<li class="breadcrumb-item active" aria-current="page">
                                    Thống Kê Rút Tiền
                                </li>
                            </ol>
                        </div>
                    </div> <!--end::Row-->
                </div> <!--end::Container-->
            </div> <!--end::App Content Header--> <!--begin::App Content-->

            <div class="card card-primary card-outline mb-4"> <!--begin::Header-->
                                <div class="card-header">
                                    <div class="card-title">Tìm Kiếm</div>
                                </div> <!--end::Header--> <!--begin::Form-->
                                <form method="GET"> <!--begin::Body-->
                                    <div class="card-body">
                                        <div class="mb-3"> <label for="exampleInputEmail1" class="form-label">NickName</label> <input type="text" name="nickname" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
                                            
                                        </div>
                                        <div class="mb-3"> <label for="exampleInputEmail1" class="form-label">Ngày Bắt Đầu</label> <input type="date" name="st" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
                                            
                                        </div>
                                        <div class="mb-3"> <label for="exampleInputEmail1" class="form-label">Ngày Kết Thúc</label> <input type="date" name="et" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
                                            
                                        </div>
                                        
                                    </div> <!--end::Body--> <!--begin::Footer-->
                                    <div class="card-footer"> <button type="submit" class="btn btn-primary">Tra Cứu</button> </div> <!--end::Footer-->
                                </form> <!--end::Form-->
                            </div> <!--end::Quick Example-->

            <div class="app-content"> <!--begin::Container-->
                <div class="container-fluid"> <!--begin::Row-->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card mb-12">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        Thống kê rút                                       
                                    </h3>
                                </div> <!-- /.card-header -->
                                <div class="card-body">
                                     <!-- Pagination Navigation -->
                                     <style>
                                    .pagination a {
                                        margin: 0 5px;
                                        text-decoration: none;
                                        color: blue;
                                    }
                                    .pagination .current {
                                        font-weight: bold;
                                        color: red;
                                    }
                                    </style>
                                                                        
                                                                        <?php
                                            function renderPagination($page, $totalPages, $limit, $displayRange = 5, $nickname = null, $start_date = null, $end_date = null) {
                                                $startPage = max(1, $page - floor($displayRange / 2));
                                                $endPage = min($totalPages, $startPage + $displayRange - 1);

                                                if ($endPage - $startPage + 1 < $displayRange) {
                                                    $startPage = max(1, $endPage - $displayRange + 1);
                                                }

                                                // Tạo URL cơ sở để giữ lại các tham số
                                                $baseUrl = "?limit={$limit}";
                                                if ($nickname) $baseUrl .= "&nickname=" . urlencode($nickname);
                                                if ($start_date) $baseUrl .= "&start_date=" . urlencode($start_date);
                                                if ($end_date) $baseUrl .= "&end_date=" . urlencode($end_date);

                                                // Thay đổi HTML để phù hợp với Bootstrap
                                                echo '<nav aria-label="Page navigation"><ul class="pagination justify-content-end">';

                                                // Nút <<
                                                if ($page > 1) {
                                                    echo '<li class="page-item"><a class="page-link" href="' . $baseUrl . '&p=' . max(1, $page - 1) . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
                                                }

                                                // Hiển thị các trang trong phạm vi
                                                for ($i = $startPage; $i <= $endPage; $i++) {
                                                    if ($i == $page) {
                                                        echo '<li class="page-item active"><span class="page-link">' . $i . '</span></li>';
                                                    } else {
                                                        echo '<li class="page-item"><a class="page-link" href="' . $baseUrl . '&p=' . $i . '">' . $i . '</a></li>';
                                                    }
                                                }

                                                // Nút >>
                                                if ($page < $totalPages) {
                                                    echo '<li class="page-item"><a class="page-link" href="' . $baseUrl . '&p=' . min($totalPages, $page + 1) . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
                                                }

                                                echo '</ul></nav>';
                                            }
                                            // Sử dụng hàm renderPagination với các tham số từ URL
                                            $page = isset($_GET['p']) ? (int)$_GET['p'] : 1;
                                            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 100;
                                            $nickname = $_GET['nickname'] ?? null;
                                            $start_date = isset($_GET['start_date']) && $_GET['start_date'] !== '' ? $_GET['start_date'] : null;
                                            $end_date = isset($_GET['end_date']) && $_GET['end_date'] !== '' ? $_GET['end_date'] : null;
                                            $totalPages = 2000; // Giả sử tổng số trang là 2000
                                            // Gọi hàm renderPagination
                                            renderPagination($page, $totalPages, $limit, 5, $nickname, $start_date, $end_date);
                                            ?>


                                    <table class="table table-bordered">
                                        <thead>
                                        <tr>
                                                <th>ID Giao Dịch</th>
                                                <th>NickName</th>
                                                <th>Tiền</th>
                                                <th>STK Bank</th>
                                                <th>Người nhận</th>
                                                <th>Bank nhận</th>
                                                <th>Ngày Tạo Đơn</th>
                                                <th>Ngày Update</th>
                                                <th>Trạng Thái</th>
                                                <th>Hành Động</th>
                                                <th>Người Duyệt</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        <?php if (!empty($data )): ?>
                                                <?php foreach ($data  as $record): ?>
                                                    <tr class="align-middle">
                                                    <td><?php echo htmlspecialchars($record['Id']); ?></td>
                                                        <td><?php echo htmlspecialchars($record['Nickname']); ?></td>
                                                        <td><?php echo htmlspecialchars(number_format($record['Amount'], 0, ',', '.')); ?></td>
                                                        <td><?php echo htmlspecialchars($record['BankAccountNumber']); ?></td>
														<td><?php echo htmlspecialchars($record['BankAccountName']); ?></td>
														<td><?php echo htmlspecialchars($record['BankBranch']); ?></td>
                                                        <td><?php echo htmlspecialchars($record['CreatedAt']); ?></td>
                                                        <td><?php echo htmlspecialchars($record['ModifiedAt']); ?></td>
                                                        <?php echo "<td class='";
                                                  switch ($record['Status']) {
                                                      case 0:
                                                      case 12:
                                                          echo "badge text-bg-primary'>Đang Chờ";
                                                          break;
                                                      case 4:
                                                      case 2:
                                                          echo "badge text-bg-success'>Thành Công";
                                                          break;
                                                      case 3:
                                                      case 11:
                                                          echo "badge text-bg-danger'>Thất Bại";
                                                          break;
                                                      case 5:
                                                          echo "badge text-bg-warning'>Đang Xem Xét";
                                                          break;
                                                      default:
                                                          echo "'>Không Xác Định";
                                                  }
                                                  echo "</td>"; 
												  echo "<td>";
                                                  if ($record['Status'] == 12 || $record['Status'] == 0 || $record['Status'] == 5) {
                                                    echo '<button class="btn btn-info" onclick="approveTransaction(\'' . htmlspecialchars($record['Nickname'], ENT_QUOTES, 'UTF-8') . '\', \'' . htmlspecialchars($record['BankAccountNumber'], ENT_QUOTES, 'UTF-8') . '\', \'' . htmlspecialchars($record['Amount'], ENT_QUOTES, 'UTF-8') . '\', \'' . htmlspecialchars($record['Id'], ENT_QUOTES, 'UTF-8') . '\')">Duyệt Tay</button><br>';
                                                    echo '<input type="text" class="form-control mt-2 mb-2" id="cancelReason_' . htmlspecialchars($record['Id'], ENT_QUOTES, 'UTF-8') . '" placeholder="Nhập lý do hủy">';
													echo '<button class="btn btn-warning" onclick="cancelTransaction(\'' . htmlspecialchars($record['Id'], ENT_QUOTES, 'UTF-8') . '\', document.getElementById(\'cancelReason_' . htmlspecialchars($record['Id'], ENT_QUOTES, 'UTF-8') . '\').value.replace(/\\s/g, \'_\'))">Hủy</button><br>';

													
                                                    echo '<button class="btn btn-success" onclick="autoApproveTransaction(\'' . htmlspecialchars($record['Nickname'], ENT_QUOTES, 'UTF-8') . '\', \'' . htmlspecialchars($record['BankAccountNumber'], ENT_QUOTES, 'UTF-8') . '\', \'' . htmlspecialchars($record['Amount'], ENT_QUOTES, 'UTF-8') . '\', \'' . htmlspecialchars($record['Id'], ENT_QUOTES, 'UTF-8') . '\')">Duyệt Auto</button><br>';
                                                    } else if ($record['Status'] == 3) { // Thêm điều kiện 'else if'
                                                        echo htmlspecialchars($record['Description']); 
                                                    }

                                                  echo "</td>";
												  ?>
														
                                                        <td><?php echo htmlspecialchars($record['UserApprove']); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="8" class="text-center">Không có dữ liệu</td>
                                                </tr>
                                            <?php endif; ?>
                                            
                                        </tbody>
                                    </table>
									<!-- Pagination Navigation -->
                <?php if ($total_pages > 1): 
                    $start_page = isset($start_page) ? $start_page : 1; // Default to the first page
                    $end_page = isset($end_page) ? $end_page : $total_pages; // Default to the last page
                    ?>
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-end">
                                <?php if ($start_page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?= $start_page - 1 ?>&nickname=<?php echo htmlspecialchars($nickname); ?>&st=<?php echo htmlspecialchars($startDate); ?>&et=<?php echo htmlspecialchars($endDate); ?>" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php if ($start_page > 2): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1&nickname=<?php echo htmlspecialchars($nickname); ?>&st=<?php echo htmlspecialchars($startDate); ?>&et=<?php echo htmlspecialchars($endDate); ?>">1</a>
                                    </li>
                                    <?php if ($start_page > 3): ?>
                                        <li class="page-item">
                                            <span class="page-link">...</span>
                                        </li>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                    <li class="page-item <?= $i == $current_page ? 'active' : '' ?>">
                                        <a class="page-link" href="?page=<?= $i ?>&nickname=<?php echo htmlspecialchars($nickname); ?>&st=<?php echo htmlspecialchars($startDate); ?>&et=<?php echo htmlspecialchars($endDate); ?>"><?= $i ?></a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($end_page < $total_pages - 1): ?>
                                    <?php if ($end_page < $total_pages - 2): ?>
                                        <li class="page-item">
                                            <span class="page-link">...</span>
                                        </li>
                                    <?php endif; ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?= $total_pages ?>&nickname=<?php echo htmlspecialchars($nickname); ?>&st=<?php echo htmlspecialchars($startDate); ?>&et=<?php echo htmlspecialchars($endDate); ?>"><?= $total_pages ?></a>
                                    </li>
                                <?php endif; ?>

                                <?php if ($end_page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?= $end_page + 1 ?>&nickname=<?php echo htmlspecialchars($nickname); ?>&st=<?php echo htmlspecialchars($startDate); ?>&et=<?php echo htmlspecialchars($endDate); ?>" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                                </div> <!-- /.card-body -->
                                
                            </div> <!-- /.card -->
                            
                        </div> <!-- /.col -->
                        
                    </div> <!--end::Row-->
                </div> <!--end::Container-->
            </div> <!--end::App Content-->
        </main> <!--end::App Main--> <!--begin::Footer-->
        <footer class="app-footer"> <!--begin::To the end-->
                <div class="float-end d-none d-sm-inline"></div> <!--end::To the end--> <!--begin::Copyright--> <strong>
                    Copyright &copy; &nbsp;
                    <a href="#" class="text-decoration-none">Dark Casino</a>.
                </strong>
                All rights reserved.
                <!--end::Copyright-->
        </footer> <!--end::Footer-->
    </div> <!--end::App Wrapper--> <!--begin::Script--> <!--begin::Third Party Plugin(OverlayScrollbars)-->
    <script>


function approveTransaction(nickname, bankCode, amount, transactionId) {
    const url = `https://iportal.789as.site/api?c=3009&nn=${nickname}&bn=${bankCode}&am=${amount}&key=BopVuEmVo123&transid=${transactionId}`;
            
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.errorCode == 0) {
                alert('Duyệt rút tay thành công');
                window.location.reload();  
            } else {
                alert('Duyệt rút tay thất bại');
                console.log(data.message);
                window.location.reload();  
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error approving transaction');
        });
}

function autoApproveTransaction(nickname, bankCode, amount, transactionId) {
    var apiUrl = `https://iportal.789as.site/api?c=3006&nn=${nickname}&bn=${bankCode}&am=${amount}&key=BopVuEmVo123&transid=${transactionId}`;
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.errorCode == 0) {
                alert('Duyệt rút tự động thành công.');
                window.location.reload();
            } else {
                alert('Lỗi khi duyệt rút giao dịch tự động.');
                console.log(data.message);
                window.location.reload();        
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error approving transaction');
        });
}

function cancelTransaction(transactionId, lydo) {
    var apiUrl = `https://iportal.789as.site/api?c=3010&key=BopVuEmVo123&transid=${transactionId}&lydo=${lydo}`;
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.errorCode == 0) {
                alert('Hủy rút thành công!');
                window.location.reload();
            } else {
                alert('Hủy rút thất bại!');
                console.log(data.message);
                window.location.reload();        
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error approving transaction');
        });

}
</script>
  <script>
    let sidebar = document.querySelector(".sidebar");
    let closeBtn = document.querySelector("#btn");

    closeBtn.addEventListener("click", () => {
      sidebar.classList.toggle("open");
      changeBtn();
    });

    function changeBtn() {
      if (sidebar.classList.contains("open")) {
        closeBtn.classList.replace("bx-menu", "bx-menu-alt-right");
      } else {
        closeBtn.classList.replace("bx-menu-alt-right", "bx-menu");
      }
    }

    function searchOnEnter(event) {
      if (event.keyCode === 13) {
        event.preventDefault();
        var searchText = document.getElementById("searchInput").value;
        window.location.href = "searchname.php?search=" + encodeURIComponent(searchText);
      }
    }
  </script>
    <script src="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.3.0/browser/overlayscrollbars.browser.es6.min.js" integrity="sha256-H2VM7BKda+v2Z4+DRy69uknwxjyDRhszjXFhsL4gD3w=" crossorigin="anonymous"></script> <!--end::Third Party Plugin(OverlayScrollbars)--><!--begin::Required Plugin(popperjs for Bootstrap 5)-->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js" integrity="sha256-whL0tQWoY1Ku1iskqPFvmZ+CHsvmRWx/PIoEvIeWh4I=" crossorigin="anonymous"></script> <!--end::Required Plugin(popperjs for Bootstrap 5)--><!--begin::Required Plugin(Bootstrap 5)-->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.min.js" integrity="sha256-YMa+wAM6QkVyz999odX7lPRxkoYAan8suedu4k2Zur8=" crossorigin="anonymous"></script> <!--end::Required Plugin(Bootstrap 5)--><!--begin::Required Plugin(AdminLTE)-->
    <script src="dist/js/adminlte.js"></script> <!--end::Required Plugin(AdminLTE)--><!--begin::OverlayScrollbars Configure-->
    <script>
        const SELECTOR_SIDEBAR_WRAPPER = ".sidebar-wrapper";
        const Default = {
            scrollbarTheme: "os-theme-light",
            scrollbarAutoHide: "leave",
            scrollbarClickScroll: true,
        };
        document.addEventListener("DOMContentLoaded", function() {
            const sidebarWrapper = document.querySelector(SELECTOR_SIDEBAR_WRAPPER);
            if (
                sidebarWrapper &&
                typeof OverlayScrollbarsGlobal?.OverlayScrollbars !== "undefined"
            ) {
                OverlayScrollbarsGlobal.OverlayScrollbars(sidebarWrapper, {
                    scrollbars: {
                        theme: Default.scrollbarTheme,
                        autoHide: Default.scrollbarAutoHide,
                        clickScroll: Default.scrollbarClickScroll,
                    },
                });
            }
        });
    </script> <!--end::OverlayScrollbars Configure--> <!--end::Script-->
</body><!--end::Body-->

</html>