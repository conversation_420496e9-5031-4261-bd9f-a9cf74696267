<?php
session_start();

// Kiểm tra session
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    http_response_code(401); // Tr<PERSON> về mã lỗi 401
    header("Location: index.php"); // Chuyển hướng về trang index.php
    exit; // Dừng thực thi các mã PHP tiếp theo
}
$nickname = $_GET['nickname'] ?? null;
$lastTransaction = isset($_GET['lastTransaction']) ? $_GET['lastTransaction'] : null;
$page = isset($_GET['p']) ? (int)$_GET['p'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 100;	; // Kiểm tra giá trị limit, mặc định là 10
$url = "https://bead.789as.site/api_backend?c=402&nn={$nickname}&p={$page}";

    // Cấu hình context cho yêu cầu POST
    $options = [
        'http' => [
            'method'  => 'GET',
            'header'  => "Content-Type: application/json\r\n" .
                         "Content-Length: " . strlen($data) . "\r\n",
        ]
    ];
    $context = stream_context_create($options);

    // Gửi yêu cầu và nhận phản hồi
    $response = file_get_contents($url, false, $context);

    // Kiểm tra lỗi
    if ($response === FALSE) {
        echo 'Lỗi khi gửi yêu cầu đến API';
        exit;
    }
    $data = json_decode($response, true);
    
?>




<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Adm 789</title><!--begin::Primary Meta Tags-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="title" content="AdminLTE 4 | Simple Tables">
    <meta name="author" content="ColorlibHQ">
    <meta name="description" content="AdminLTE is a Free Bootstrap 5 Admin Dashboard, 30 example pages using Vanilla JS.">
    <meta name="keywords" content="bootstrap 5, bootstrap, bootstrap 5 admin dashboard, bootstrap 5 dashboard, bootstrap 5 charts, bootstrap 5 calendar, bootstrap 5 datepicker, bootstrap 5 tables, bootstrap 5 datatable, vanilla js datatable, colorlibhq, colorlibhq dashboard, colorlibhq admin dashboard"><!--end::Primary Meta Tags--><!--begin::Fonts-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fontsource/source-sans-3@5.0.12/index.css" integrity="sha256-tXJfXfp6Ewt1ilPzLDtQnJV4hclT9XuaZUKyUvmyr+Q=" crossorigin="anonymous"><!--end::Fonts--><!--begin::Third Party Plugin(OverlayScrollbars)-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.3.0/styles/overlayscrollbars.min.css" integrity="sha256-dSokZseQNT08wYEWiz5iLI8QPlKxG+TswNRD8k35cpg=" crossorigin="anonymous"><!--end::Third Party Plugin(OverlayScrollbars)--><!--begin::Third Party Plugin(Bootstrap Icons)-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.min.css" integrity="sha256-Qsx5lrStHZyR9REqhUF8iQt73X06c8LGIUPzpOhwRrI=" crossorigin="anonymous"><!--end::Third Party Plugin(Bootstrap Icons)--><!--begin::Required Plugin(AdminLTE)-->
    <link rel="stylesheet" href="dist/css/adminlte.css"><!--end::Required Plugin(AdminLTE)-->
    <script>
    // Gửi yêu cầu định kỳ tới keep_alive.php để duy trì session
    setInterval(function() {
        fetch('keep_alive.php', {
            method: 'GET',
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'session_refreshed') {
                console.log('Session refreshed successfully.');
            } else {
                console.warn('Unexpected response:', data);
            }
        })
        .catch(error => {
            console.error('Error refreshing session:', error);
        });
    }, 30000);
    </script>
</head> <!--end::Head--> <!--begin::Body-->

<body class="layout-fixed sidebar-expand-lg bg-body-tertiary"> <!--begin::App Wrapper-->
    <div class="app-wrapper"> <!--begin::Header-->
        <nav class="app-header navbar navbar-expand bg-body"> <!--begin::Container-->
            <div class="container-fluid"> <!--begin::Start Navbar Links-->
                <ul class="navbar-nav">
                    <li class="nav-item"> <a class="nav-link" data-lte-toggle="sidebar" href="#" role="button"> <i class="bi bi-list"></i> </a> </li>
                    <li class="nav-item d-none d-md-block"> <a href="#" class="nav-link"></a> </li>
                </ul> <!--end::Start Navbar Links--> <!--begin::End Navbar Links-->
                <ul class="navbar-nav ms-auto">  <!--begin::Fullscreen Toggle-->
                    <li class="nav-item"> <a class="nav-link" href="#" data-lte-toggle="fullscreen"> <i data-lte-icon="maximize" class="bi bi-arrows-fullscreen"></i> <i data-lte-icon="minimize" class="bi bi-fullscreen-exit" style="display: none;"></i> </a> </li> <!--end::Fullscreen Toggle--> <!--begin::User Menu Dropdown-->
                    <li class="nav-item dropdown user-menu"> <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown"> <img src="dist/assets/img/user2-160x160.jpg" class="user-image rounded-circle shadow" alt="User Image"> <span class="d-none d-md-inline">SuperAdmin</span> </a>
                        <?php include 'Head.php'; ?>
                    </li> <!--end::User Menu Dropdown-->
                </ul> <!--end::End Navbar Links-->
            </div> <!--end::Container-->
        </nav> <!--end::Header--> <!--begin::Sidebar-->
			<?php include 'menu.php'; ?>  
		<!--begin::App Main-->
        <main class="app-main"> <!--begin::App Content Header-->
            <div class="app-content-header"> <!--begin::Container-->
                <div class="container-fluid"> <!--begin::Row-->
                    <div class="row">
                        <div class="col-sm-6">
                            <span class='badge text-bg-info' style="font-size: 15px;">Thư Đã Gửi</span>
                        </div>
                        <div class="col-sm-6">
                            <ol class="breadcrumb float-sm-end">
                                <li class="breadcrumb-item"><a href="#">Admin</a></li>
                                <li class="breadcrumb-item active" aria-current="page">
                                    Tin Nhắn
                                </li>
								<li class="breadcrumb-item active" aria-current="page">
                                Thư Đã Gửi
                                </li>
                            </ol>
                        </div>
                    </div> <!--end::Row-->
                </div> <!--end::Container-->
            </div> <!--end::App Content Header--> <!--begin::App Content-->

            <!--begin::Quick Example-->
                            <div class="card card-primary card-outline mb-4"> <!--begin::Header-->
                                <div class="card-header">
                                    <div class="card-title">Tìm Kiếm</div>
                                </div> <!--end::Header--> <!--begin::Form-->
                                <form method="GET"> <!--begin::Body-->
                                    <div class="card-body">
                                        <div class="mb-3"> 
                                            <label for="exampleInputEmail1" class="form-label">Nick Name</label> <input type="text" name="nickname" class="form-control" id="exampleInputEmail1" aria-describedby="emailHelp">
                                        </div>
                                        
                                    </div> <!--end::Body--> <!--begin::Footer-->
                                    <div class="card-footer"> <button type="submit" class="btn btn-primary">Tra Cứu</button> </div> <!--end::Footer-->
                                </form> <!--end::Form-->
                            </div> <!--end::Quick Example-->


            <div class="card card-primary card-outline mb-4"> <!--begin::Header-->
                
                
            </div>

            <div class="app-content"> <!--begin::Container-->
                <div class="container-fluid"> <!--begin::Row-->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card mb-12">
                                <div class="card-header">
                                    <h3 class="card-title">
                                        
                                    </h3>
                                </div> <!-- /.card-header -->
                                <div class="card-body">
                                


                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>ID Thư</th>
                                                <th>Tiêu Đề</th>
                                                <th>Thời Gian</th>
												<th>Nội Dung</th>
                                                <th>Hành Động</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <?php if (!empty($data['transactions'])): ?>
                                                    <?php foreach ($data['transactions'] as $record): ?>
                                                        <tr class="align-middle">
                                                            <td><?php echo htmlspecialchars($record['mail_id']); ?></td>
                                                            <td><?php echo htmlspecialchars($record['title']); ?></td>
                                                            <td><?php echo htmlspecialchars($record['createTime']); ?></td>
                                                            <td><?php echo htmlspecialchars($record['content']); ?></td>
                                                            <td>
                                                                <button 
                                                                    class="btn btn-info" 
                                                                    onclick="deleteEmail('<?php echo htmlspecialchars($record['mail_id'], ENT_QUOTES, 'UTF-8'); ?>')">
                                                                    Xóa Thư
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                <?php else: ?>
                                                    <tr>
                                                        <td colspan="8" class="text-center">Không có dữ liệu</td>
                                                    </tr>
                                                <?php endif; ?>
                                            </tbody>
                                                                                    
                                    </table>
                                    
<script>
function deleteEmail(mailId) {
    if (confirm('Bạn có chắc chắn muốn xóa email này?')) {
        fetch(`https://bead.789as.site/api_backend?c=403&mid=${mailId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Xóa email thành công!');
                location.reload(); // Reload lại trang để cập nhật danh sách
            } else {
                alert('Xóa email thất bại: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Lỗi:', error);
            alert('Đã xảy ra lỗi khi xóa email!');
        });
    }
}



</script>
									
									 <!-- Pagination Navigation -->
                
                                </div> <!-- /.card-body -->
                                <?php
function renderPagination($page, $totalPages, $nickname = null, $displayRange = 5) {
    $startPage = max(1, $page - floor($displayRange / 2));
    $endPage = min($totalPages, $startPage + $displayRange - 1);

    if ($endPage - $startPage + 1 < $displayRange) {
        $startPage = max(1, $endPage - $displayRange + 1);
    }

    // Tạo URL cơ sở để giữ lại tham số nickname
    $baseUrl = "?";
    if ($nickname) {
        $baseUrl .= "nickname=" . urlencode($nickname) . "&";
    }

    echo '<nav aria-label="Page navigation"><ul class="pagination justify-content-end">';

    // Nút <<
    if ($page > 1) {
        echo '<li class="page-item"><a class="page-link" href="' . $baseUrl . 'p=' . max(1, $page - 1) . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
    }

    // Hiển thị các trang trong phạm vi
    for ($i = $startPage; $i <= $endPage; $i++) {
        if ($i == $page) {
            echo '<li class="page-item active"><span class="page-link">' . $i . '</span></li>';
        } else {
            echo '<li class="page-item"><a class="page-link" href="' . $baseUrl . 'p=' . $i . '">' . $i . '</a></li>';
        }
    }

    // Nút >>
    if ($page < $totalPages) {
        echo '<li class="page-item"><a class="page-link" href="' . $baseUrl . 'p=' . min($totalPages, $page + 1) . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
    }

    echo '</ul></nav>';
}

// Sử dụng hàm renderPagination với các tham số từ URL
$page = isset($_GET['p']) ? (int)$_GET['p'] : 1;
$nickname = $_GET['nickname'] ?? null;
$totalPages = 2000; // Giả sử tổng số trang là 2000

// Gọi hàm renderPagination
renderPagination($page, $totalPages, $nickname);
?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <footer class="app-footer"> <!--begin::To the end-->
                <div class="float-end d-none d-sm-inline"></div> <!--end::To the end--> <!--begin::Copyright--> <strong>
                    Copyright &copy; &nbsp;
                    <a href="#" class="text-decoration-none">Dark Casino</a>.
                </strong>
                All rights reserved.
                <!--end::Copyright-->
            </footer> <!--end::Footer-->
    </div> <!--end::App Wrapper--> <!--begin::Script--> <!--begin::Third Party Plugin(OverlayScrollbars)-->
    <script src="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.3.0/browser/overlayscrollbars.browser.es6.min.js" integrity="sha256-H2VM7BKda+v2Z4+DRy69uknwxjyDRhszjXFhsL4gD3w=" crossorigin="anonymous"></script> <!--end::Third Party Plugin(OverlayScrollbars)--><!--begin::Required Plugin(popperjs for Bootstrap 5)-->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js" integrity="sha256-whL0tQWoY1Ku1iskqPFvmZ+CHsvmRWx/PIoEvIeWh4I=" crossorigin="anonymous"></script> <!--end::Required Plugin(popperjs for Bootstrap 5)--><!--begin::Required Plugin(Bootstrap 5)-->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.min.js" integrity="sha256-YMa+wAM6QkVyz999odX7lPRxkoYAan8suedu4k2Zur8=" crossorigin="anonymous"></script> <!--end::Required Plugin(Bootstrap 5)--><!--begin::Required Plugin(AdminLTE)-->
    <script src="dist/js/adminlte.js"></script> <!--end::Required Plugin(AdminLTE)--><!--begin::OverlayScrollbars Configure-->
    <script>
        const SELECTOR_SIDEBAR_WRAPPER = ".sidebar-wrapper";
        const Default = {
            scrollbarTheme: "os-theme-light",
            scrollbarAutoHide: "leave",
            scrollbarClickScroll: true,
        };
        document.addEventListener("DOMContentLoaded", function() {
            const sidebarWrapper = document.querySelector(SELECTOR_SIDEBAR_WRAPPER);
            if (
                sidebarWrapper &&
                typeof OverlayScrollbarsGlobal?.OverlayScrollbars !== "undefined"
            ) {
                OverlayScrollbarsGlobal.OverlayScrollbars(sidebarWrapper, {
                    scrollbars: {
                        theme: Default.scrollbarTheme,
                        autoHide: Default.scrollbarAutoHide,
                        clickScroll: Default.scrollbarClickScroll,
                    },
                });
            }
        });
    </script> <!--end::OverlayScrollbars Configure--> <!--end::Script-->

</body><!--end::Body-->

</html>