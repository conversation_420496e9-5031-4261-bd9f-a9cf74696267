<?php
    session_start();

    // Kiểm tra session
    if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
        http_response_code(401); // <PERSON><PERSON><PERSON> về mã lỗi 401
        header("Location: index.php"); // Chuyển hướng về trang index.php
        exit; // Dừng thực thi các mã PHP tiếp theo
    }
    $codedl = isset($_GET['codedl']) ? $_GET['codedl'] : '';
    $start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
    $end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';
    $page = isset($_GET['p']) ? (int)$_GET['p'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] :10;	
    $url = "https://gameplus.789as.site/napdaily?page={$page}&limit={$limit}";
    $data = json_encode([
        'codedl' => $codedl,
        'start_time' => $start_date,
        'end_time' => $end_date
        ]);
        // Cấu hình context cho yêu cầu POST
        $options = [
            'http' => [
                'method'  => 'POST',
                'header'  => "Content-Type: application/json\r\n" .
                            "Content-Length: " . strlen($data) . "\r\n",
                'content' => $data
            ]
        ];
        $context = stream_context_create($options);

        // Gửi yêu cầu và nhận phản hồi
        $response = file_get_contents($url, false, $context);
        $data = json_decode($response, true);
        $total_count = 500
    ?>

    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>Adm 789</title><!--begin::Primary Meta Tags-->
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="title" content="AdminLTE 4 | Simple Tables">
        <meta name="author" content="ColorlibHQ">
        <meta name="description" content="AdminLTE is a Free Bootstrap 5 Admin Dashboard, 30 example pages using Vanilla JS.">
        <meta name="keywords" content="bootstrap 5, bootstrap, bootstrap 5 admin dashboard, bootstrap 5 dashboard, bootstrap 5 charts, bootstrap 5 calendar, bootstrap 5 datepicker, bootstrap 5 tables, bootstrap 5 datatable, vanilla js datatable, colorlibhq, colorlibhq dashboard, colorlibhq admin dashboard"><!--end::Primary Meta Tags--><!--begin::Fonts-->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fontsource/source-sans-3@5.0.12/index.css" integrity="sha256-tXJfXfp6Ewt1ilPzLDtQnJV4hclT9XuaZUKyUvmyr+Q=" crossorigin="anonymous"><!--end::Fonts--><!--begin::Third Party Plugin(OverlayScrollbars)-->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.3.0/styles/overlayscrollbars.min.css" integrity="sha256-dSokZseQNT08wYEWiz5iLI8QPlKxG+TswNRD8k35cpg=" crossorigin="anonymous"><!--end::Third Party Plugin(OverlayScrollbars)--><!--begin::Third Party Plugin(Bootstrap Icons)-->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.min.css" integrity="sha256-Qsx5lrStHZyR9REqhUF8iQt73X06c8LGIUPzpOhwRrI=" crossorigin="anonymous"><!--end::Third Party Plugin(Bootstrap Icons)--><!--begin::Required Plugin(AdminLTE)-->
        <link rel="stylesheet" href="dist/css/adminlte.css"><!--end::Required Plugin(AdminLTE)-->
        <script>
    // Gửi yêu cầu định kỳ tới keep_alive.php để duy trì session
    setInterval(function() {
        fetch('keep_alive.php', {
            method: 'GET',
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'session_refreshed') {
                console.log('Session refreshed successfully.');
            } else {
                console.warn('Unexpected response:', data);
            }
        })
        .catch(error => {
            console.error('Error refreshing session:', error);
        });
    }, 30000);
    </script>
    </head> <!--end::Head--> <!--begin::Body-->

    <body class="layout-fixed sidebar-expand-lg bg-body-tertiary"> <!--begin::App Wrapper-->
        <div class="app-wrapper"> <!--begin::Header-->
            <nav class="app-header navbar navbar-expand bg-body"> <!--begin::Container-->
                <div class="container-fluid"> <!--begin::Start Navbar Links-->
                    <ul class="navbar-nav">
                        <li class="nav-item"> <a class="nav-link" data-lte-toggle="sidebar" href="#" role="button"> <i class="bi bi-list"></i> </a> </li>
                        <li class="nav-item d-none d-md-block"> <a href="#" class="nav-link">Trang chủ</a> </li>
                    </ul> <!--end::Start Navbar Links--> <!--begin::End Navbar Links-->
                    <ul class="navbar-nav ms-auto">  <!--begin::Fullscreen Toggle-->
                        <li class="nav-item"> <a class="nav-link" href="#" data-lte-toggle="fullscreen"> <i data-lte-icon="maximize" class="bi bi-arrows-fullscreen"></i> <i data-lte-icon="minimize" class="bi bi-fullscreen-exit" style="display: none;"></i> </a> </li> <!--end::Fullscreen Toggle--> <!--begin::User Menu Dropdown-->
                        <li class="nav-item dropdown user-menu"> <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown"> <img src="dist/assets/img/user2-160x160.jpg" class="user-image rounded-circle shadow" alt="User Image"> <span class="d-none d-md-inline">SuperAdmin</span> </a>
                            <?php include 'Head.php'; ?>
                        </li> <!--end::User Menu Dropdown-->
                    </ul> <!--end::End Navbar Links-->
                </div> <!--end::Container-->
            </nav> <!--end::Header--> <!--begin::Sidebar-->
            <?php include 'menu.php'; ?>  
            <!--begin::App Main-->
            <main class="app-main"> <!--begin::App Content Header-->
                <div class="app-content-header"> <!--begin::Container-->
                    <div class="container-fluid"> <!--begin::Row-->
                        <div class="row">
                            <div class="col-sm-6">
                                <span class='badge text-bg-info' style="font-size: 15px;">Thống Kê Nạp Đại Lý</span>
                            </div>
                            <div class="col-sm-6">
                                <ol class="breadcrumb float-sm-end">
                                <li class="breadcrumb-item"><a href="#">Admin</a></li>
                                <li class="breadcrumb-item active" aria-current="page">
                                    Tổng Đại Lý
                                </li>
								<li class="breadcrumb-item active" aria-current="page">
                                    Thống Kê Nạp Đại Lý
                                </li>
                            </ol>
                            </div>
                        </div> <!--end::Row-->
                    </div> <!--end::Container-->
                </div> <!--end::App Content Header--> <!--begin::App Content-->

                <!--begin::Quick Example-->
                                <div class="card card-primary card-outline mb-4"> <!--begin::Header-->
                                    <!--end::Header--> <!--begin::Form-->
                                    <form method="GET"> <!--begin::Body-->
                <div class="card-body">
                    <div class="form-group">
                    <label for="start_date" class="form-label">Mã Đại Lý</label>
                    <input type="text" name="codedl" class="form-control date-input" id="codedl" value="<?php echo isset($_GET['codedl']) ? htmlspecialchars($_GET['codedl']) : ''; ?>">
                                                <div class="mb-3"> <label for="exampleInputEmail1" class="form-label">Ngày Bắt Đầu</label>
                                                <input type="datetime-local" id="start_date" name="start_date" class="form-control" aria-describedby="emailHelp" value="<?php echo isset($_GET['start_date']) ? htmlspecialchars($_GET['start_date']) : ''; ?>">
        
                                                    
                                                </div>
                                                <div class="mb-3"> <label for="exampleInputEmail1" class="form-label">Ngày Kết Thúc</label>
                                                <input type="datetime-local" id="end_date" name="end_date" class="form-control" aria-describedby="emailHelp" value="<?php echo isset($_GET['end_date']) ? htmlspecialchars($_GET['end_date']) : ''; ?>">
        
                    </div>		
                
                </div> <!--end::Body--> <!--begin::Footer-->
                <div class="card-footer"> <button type="submit" class="btn btn-primary">Tra Cứu</button> </div> <!--end::Footer-->
            </form> <!--end::Form-->
                                </div> <!--end::Quick Example-->


                <div class="card card-primary card-outline mb-4"> <!--begin::Header-->
                    
                    
                </div>

                <div class="app-content"> <!--begin::Container-->
                    <div class="container-fluid"> <!--begin::Row-->
                        <div class="row">
                        <div class="col-md-12">
                                <div class="card mb-4">
                                    <div class="card-header">
                                       
                                    </div> <!-- /.card-header -->
                                    <div class="card-body">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
													<th>Mã Đại Lý</th>
													<th>ID Giao Dịch</th>
                                                    <th>NickName</th>
                                                    <th>Tiền</th>
                                                    <th>Kênh Nạp</th> 
                                                    <th>Ngày Nạp</th>
                                                    
                                                    
                                                    
                                                </tr>
                                            </thead>
                                            <tbody id="table-body-tai">
                                                <?php foreach ($data as $deposit): ?>
                                                <tr>
												<td><?php echo $deposit['codedl']; ?></td>
                                                <td><?php echo $deposit['TransactionId']; ?></td>
                                                    <td><?php echo $deposit['Nickname']; ?></td>
                                                    <td><?php echo number_format($deposit['Amount']); ?></td>
                                                    <td><?php echo $deposit['BankCode']; ?></td>
                                                    
                                                    <td><?php echo $deposit['ModifiedAt']; ?></td>
                                                    
                                                    
                                                    
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table><br>
                                        <?php
function renderPagination($page, $totalPages, $limit, $displayRange = 5, $codedl = null, $start_date = null, $end_date = null) {
    // Tính toán phạm vi trang sẽ hiển thị
    $startPage = max(1, $page - floor($displayRange / 2));
    $endPage = min($totalPages, $startPage + $displayRange - 1);

    if ($endPage - $startPage + 1 < $displayRange) {
        $startPage = max(1, $endPage - $displayRange + 1);
    }

    // Tạo URL cơ sở để giữ lại các tham số
    $baseUrl = "?limit={$limit}";
    if ($codedl) $baseUrl .= "&codedl=" . urlencode($codedl);
    if ($start_date) $baseUrl .= "&start_date=" . urlencode($start_date);
    if ($end_date) $baseUrl .= "&end_date=" . urlencode($end_date);

    // Thay đổi HTML để phù hợp với Bootstrap
    echo '<nav aria-label="Page navigation"><ul class="pagination justify-content-end">';

    // Nút <<
    if ($page > 1) {
        echo '<li class="page-item"><a class="page-link" href="' . $baseUrl . '&p=' . max(1, $page - 1) . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
    }

    // Hiển thị các trang trong phạm vi
    for ($i = $startPage; $i <= $endPage; $i++) {
        if ($i == $page) {
            echo '<li class="page-item active"><span class="page-link">' . $i . '</span></li>';
        } else {
            echo '<li class="page-item"><a class="page-link" href="' . $baseUrl . '&p=' . $i . '">' . $i . '</a></li>';
        }
    }

    // Nút >>
    if ($page < $totalPages) {
        echo '<li class="page-item"><a class="page-link" href="' . $baseUrl . '&p=' . min($totalPages, $page + 1) . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
    }

    echo '</ul></nav>';
}

// Sử dụng hàm renderPagination với các tham số từ URL
$page = isset($_GET['p']) ? (int)$_GET['p'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
$codedl = isset($_GET['codedl']) ? $_GET['codedl'] : '';
$start_date = isset($_GET['start_date']) && $_GET['start_date'] !== '' ? $_GET['start_date'] : null;
$end_date = isset($_GET['end_date']) && $_GET['end_date'] !== '' ? $_GET['end_date'] : null;
$totalPages = ceil($total_count / $limit);

// Gọi hàm renderPagination
renderPagination($page, $totalPages, $limit, 5, $codedl, $start_date, $end_date);
?>


                                    </div> <!-- /.card-body -->
                                    <div class="card-footer clearfix">
                                        <ul class="pagination pagination-sm m-0 float-end"></ul>
                                    </div>
                                </div> <!-- /.card -->
                            </div> 
                        </div>
                    </div>
                </div>
            </main>

            <footer class="app-footer"> <!--begin::To the end-->
                <div class="float-end d-none d-sm-inline"></div> <!--end::To the end--> <!--begin::Copyright--> <strong>
                    Copyright &copy; &nbsp;
                    <a href="#" class="text-decoration-none">Dark Casino</a>.
                </strong>
                All rights reserved.
                <!--end::Copyright-->
            </footer> <!--end::Footer-->
        </div> <!--end::App Wrapper--> <!--begin::Script--> <!--begin::Third Party Plugin(OverlayScrollbars)-->
        <script src="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.3.0/browser/overlayscrollbars.browser.es6.min.js" integrity="sha256-H2VM7BKda+v2Z4+DRy69uknwxjyDRhszjXFhsL4gD3w=" crossorigin="anonymous"></script> <!--end::Third Party Plugin(OverlayScrollbars)--><!--begin::Required Plugin(popperjs for Bootstrap 5)-->
        <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js" integrity="sha256-whL0tQWoY1Ku1iskqPFvmZ+CHsvmRWx/PIoEvIeWh4I=" crossorigin="anonymous"></script> <!--end::Required Plugin(popperjs for Bootstrap 5)--><!--begin::Required Plugin(Bootstrap 5)-->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.min.js" integrity="sha256-YMa+wAM6QkVyz999odX7lPRxkoYAan8suedu4k2Zur8=" crossorigin="anonymous"></script> <!--end::Required Plugin(Bootstrap 5)--><!--begin::Required Plugin(AdminLTE)-->
        <script src="dist/js/adminlte.js"></script> <!--end::Required Plugin(AdminLTE)--><!--begin::OverlayScrollbars Configure-->
        <script>
            const SELECTOR_SIDEBAR_WRAPPER = ".sidebar-wrapper";
            const Default = {
                scrollbarTheme: "os-theme-light",
                scrollbarAutoHide: "leave",
                scrollbarClickScroll: true,
            };
            document.addEventListener("DOMContentLoaded", function() {
                const sidebarWrapper = document.querySelector(SELECTOR_SIDEBAR_WRAPPER);
                if (
                    sidebarWrapper &&
                    typeof OverlayScrollbarsGlobal?.OverlayScrollbars !== "undefined"
                ) {
                    OverlayScrollbarsGlobal.OverlayScrollbars(sidebarWrapper, {
                        scrollbars: {
                            theme: Default.scrollbarTheme,
                            autoHide: Default.scrollbarAutoHide,
                            clickScroll: Default.scrollbarClickScroll,
                        },
                    });
                }
            });
        </script> <!--end::OverlayScrollbars Configure--> <!--end::Script-->
        <script>
        function approveTransaction(transactionId) {
        const url = `https://iportal.789as.site/api?c=3020&key=BopVuEmVo123&transid=${transactionId}`;
                
        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Duyệt tay thành công');
                    window.location.reload();  
                } else {
                    alert('Duyệt tay thất bại');
                    console.log(data.message);
                    window.location.reload();  
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error approving transaction');
            });
    }
        </script>
    </body><!--end::Body-->

    </html>